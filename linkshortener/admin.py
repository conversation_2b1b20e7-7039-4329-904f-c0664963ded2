from django.contrib import admin
from django.urls import reverse
from django.utils.html import escape
from django.utils.safestring import mark_safe
from import_export.admin import ImportExportModelAdmin
from import_export import resources

from linkshortener.models import (
    UrlData,
)

class UrlDataResource(resources.ModelResource):
    class Meta:
        model = UrlData

class UrlDataResourceAdmin(ImportExportModelAdmin):
    resource_class = UrlDataResource
    search_fields = ["slug", "url"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


admin.site.register(UrlData, UrlDataResourceAdmin)