from django.db import models

# Create your models here.


class UrlData(models.Model):
    slug = models.CharField(null=True, blank=True, max_length=15, unique=True)
    url = models.CharField(max_length=200)
    expires_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Short Url for: {self.url} is {self.slug}"

    def save(self, *args, **kwargs):

        slugify = False
        if not self.pk:
            slugify = True

        super(UrlData, self).save(*args, **kwargs)

        if slugify:
            self.refresh_from_db()
            self.slug = UrlData.gen_slug(self.id)

            self.save()

    def gen_slug(_id):

        import random

        l = "abcdefghijklmnopqrstuvwxyz"
        y = list(str(_id))+random.choices(l,k=2)+["0","0","0","0"]
        random.shuffle(y)
        slug = "".join(y)

        return slug

    @classmethod
    def slugify_url(cls, url):

        url_object = UrlData.objects.create(url=url)
        url_object.save()

        url_object.refresh_from_db()

        return url_object.slug

