from datetime import date
from typing import Optional

from celery import shared_task
from django.db.models import Q

from core.tasks import send_email
from stock_inventory import models


# Create your task(s) here.
@shared_task
def daily_branch_inventory_status():
    all_stocks = models.StockDetail.objects.all()
    for inventory in all_stocks:
        models.InventoryStatus.objects.create(
            company=inventory.company,
            branch=inventory.branch,
            item=inventory.item,
            opening_quantity=inventory.quantity,
            opening_value=inventory.stock_value,
        )
    return "SUCCESSFULLY CREATED BRANCH INVENTORY STATUS"


@shared_task
def update_branch_inventory_status():
    all_stocks = models.StockDetail.objects.all()
    for inventory in all_stocks:
        try:
            inventory_status = models.InventoryStatus.objects.get(
                created_at__date=date.today(),
                branch=inventory.branch,
                item=inventory.item,
            )
            inventory_status.closing_quantity = inventory.quantity
            inventory_status.closing_value = inventory.stock_value
            inventory_status.save()
        except models.InventoryStatus.DoesNotExist:
            pass
    return "SUCCESSFULLY UPDATED BRANCH INVENTORY STATUS"


@shared_task
def daily_branch_inventory_journal():
    pass


@shared_task
def supplier_purchase_order(request_id: str):
    from stock_inventory.models import PurchaseOrder

    purchase_order = PurchaseOrder.objects.filter(request_id=request_id)
    if not purchase_order.exists():
        return f"INVALID PURCHASE ORDER FOR: {request_id}."

    html_content = []
    for data in purchase_order:
        # Generate dynamic HTML content and append accordingly.
        item_content = f"""
        <tr>
            <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                {data.category.name}
            </td>
            <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                {data.item.name}
            </td>
            <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                {data.quantity_requested}
            </td>
        </tr>
        """
        html_content.append(item_content)
    # Re-generate HTML content as a single string variable.
    items = "\n".join(html_content)
    if purchase_order.last().supplier.email is not None:
        send_email.delay(
            recipient=purchase_order.last().supplier.email,
            subject=f"Purchase Order from {purchase_order.last().company.company_name}",
            template_dir="purchase_order.html",
            supplier_name=purchase_order.last().supplier.name,
            company_name=purchase_order.last().company.company_name,
            items=items,
        )
    return "SUPPLIER HAS NO EMAIL ADDRESS."


@shared_task
def company_product_price_variation(
    product_id: Optional[str] = None,
    price_tag_id: Optional[str] = None,
):
    # ON: company creates new product.
    if product_id is not None:
        company_product = models.Product.objects.filter(id=product_id).first()
        if company_product is not None:
            available_price_tags = models.PriceTag.objects.filter(
                Q(system_default=True) | Q(company=company_product.company)
            )
            if available_price_tags.exists():
                for price_tag in available_price_tags:
                    models.PriceVariation.objects.get_or_create(
                        company=company_product.company,
                        item=company_product,
                        price_tag=price_tag,
                    )
                return "SUCCESSFULLY ADDED PRODUCT TO PRICE VARIATION"
            return "NO AVAILABLE PRICE VARIATION(S)"
        return "INVALID PRODUCT ID"

    # ON: company creates new price tag.
    if price_tag_id is not None:
        company_price_tag = models.PriceTag.objects.filter(
            id=price_tag_id, company__isnull=False
        ).first()
        if company_price_tag is not None:
            company_products = models.Product.objects.filter(
                company=company_price_tag.company
            )
            if company_products.exists():
                for product in company_products:
                    models.PriceVariation.objects.get_or_create(
                        company=company_price_tag.company,
                        item=product,
                        price_tag=company_price_tag,
                    )
                return "SUCCESSFULLY CREATED NEW PRICE VARIATION"
            return "COMPANY HAS NO PRODUCT TO CREATE A VARIATION"
        return "INVALID PRICE TAG ID"
