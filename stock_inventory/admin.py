from django.contrib import admin

from import_export.admin import ImportExportModelAdmin

from stock_inventory import models, resources


# Register your model(s) here.
from stock_inventory.models import Brand


class BranchResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BranchResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "name",
    ]
    list_filter = [
        "is_super_branch",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        fields = [field.name for field in self.model._meta.concrete_fields]
        return fields


class CategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CategoryResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "name",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SubCategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SubCategoryResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "category__name",
        "name",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InventoryJournalResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.InventoryJournalResource
    search_fields = [
        "item__name",
        "branch__name",
        "company__company_name",
    ]
    list_filter = [
        "type",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InventoryStatusResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.InventoryStatusResource
    search_fields = [
        "item__name",
        "branch__name",
        "company__company_name",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PurchaseOrderResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PurchaseOrderResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "category__name",
        "item__name",
        "branch__name",
        "supplier__name",
    ]
    list_filter = [
        "category",
        "item",
        "supplier",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PriceListResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PriceListResource
    autocomplete_fields = [
        "company",
        "category",
        "item",
    ]
    search_fields = [
        "company__company_name",
        "company__user__email",
        "category__name",
        "item__name",
    ]
    list_filter = [
        "all_branches",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ProductResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = [
        "company",
        "category",
        "subcategory",
    ]
    resource_class = resources.ProductResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "category__name",
        "subcategory__name",
        "name",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockDetailResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "branch__name",
        "category__name",
        "subcategory__name",
        "item__name",
    ]
    list_filter = [
        "has_variants",
        "has_unique_ids",
        "branch",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockHistoryResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "branch__name",
        "category__name",
        "item__name",
    ]
    list_filter = [
        "branch",
        "transaction_type", 
        "status",
        "item",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SupplierResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SupplierResource
    search_fields = [
        "name",
        "email",
        "phone_number",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SupplierHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SupplierHistoryResource
    search_fields = [
        "category__name",
        "company__company_name",
        "item__name",
        "supplier__name",
        "supplier__email",
        "supplier__phone_number",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockOutResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockOutResource
    search_fields = [
        "category__name",
        "company__company_name",
        "branch__name",
        "item__name",
    ]
    list_filter = [
        "status",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockRequestResource
    search_fields = [
        "company__company_name",
        "demand_branch__name",
        "request_id",
        "supply_branch__name",
    ]
    list_filter = [
        "status",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockTransferResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockTransferResource
    search_fields = [
        "category__name",
        "company__company_name",
        "branch__name",
        "item__name",
        "request_id",
        "transfer_to__name",
    ]
    list_filter = [
        "status",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockUniqueIdResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockUniqueIdResource
    search_fields = [
        "stock_item__category__name",
        "stock_item__item__name",
        "unique_id",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        fields = [field.name for field in self.model._meta.concrete_fields]
        fields.remove("is_active")
        fields.remove("is_deleted")
        return fields


class StockVariantResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockVariantResource
    search_fields = [
        "branch__name",
        "company__company_name",
        "stock_item__category__name",
        "stock_item__item__name",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StockOnHoldHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.StockOnHoldHistoryResource
    search_fields = [
        "branch__name",
        "company__company_name",
        "category__name",
        "item__name",
    ]
    list_filter = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# class CompanyStoreResourceAdmin(ImportExportModelAdmin):
#     resource_class = resources.CompanyStoreResource
#     search_fields = [
#         "company__company_name",
#         "store_url",
#     ]
#     list_filter = ["header_alignment", "navigation_alignment"]

#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


class APILogResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.APILogResource

    search_fields = [
        "user__email",
    ]
    list_filter = [
        "method",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PriceTagResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PriceTagResource

    search_fields = [
        "company__user__email",
    ]
    list_filter = [
        "system_default",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]



class PriceVariationResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PriceVariationResource

    search_fields = [
        "company__user__email",
        "item__name",
        "price_tag__name",
    ]
    list_filter = [
        "method",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(Brand)
admin.site.register(models.Branch, BranchResourceAdmin)
admin.site.register(models.Category, CategoryResourceAdmin)
admin.site.register(models.SubCategory, SubCategoryResourceAdmin)
admin.site.register(models.InventoryJournal, InventoryJournalResourceAdmin)
admin.site.register(models.InventoryStatus, InventoryStatusResourceAdmin)
admin.site.register(models.PurchaseOrder, PurchaseOrderResourceAdmin)
admin.site.register(models.Product, ProductResourceAdmin)
admin.site.register(models.PriceList, PriceListResourceAdmin)
admin.site.register(models.StockDetail, StockDetailResourceAdmin)
admin.site.register(models.StockHistory, StockHistoryResourceAdmin)
admin.site.register(models.StockVariant, StockVariantResourceAdmin)
admin.site.register(models.StockOut, StockOutResourceAdmin)
admin.site.register(models.StockRequest, StockRequestResourceAdmin)
admin.site.register(models.StockTransfer, StockTransferResourceAdmin)
admin.site.register(models.StockUniqueId, StockUniqueIdResourceAdmin)
admin.site.register(models.Supplier, SupplierResourceAdmin)
admin.site.register(models.SupplierHistory, SupplierHistoryResourceAdmin)
admin.site.register(models.StockOnHoldHistory, StockOnHoldHistoryResourceAdmin)
# admin.site.register(models.CompanyStore, CompanyStoreResourceAdmin)
admin.site.register(models.APILog, APILogResourceAdmin)
admin.site.register(models.PriceTag, PriceTagResourceAdmin)
admin.site.register(models.PriceVariation, PriceVariationResourceAdmin)
