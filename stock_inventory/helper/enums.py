from django.db.models import TextChoices


# Create your enumeration type(s) here.
class InventoryJournalChoices(TextChoices):
    PURCHASE = "PURCHASE", "PURCHASE"
    SALES = "SALES", "SALES"


class PriceVariationMethods(TextChoices):
    FIXED = "FIXED", "FIXED"
    MARGIN = "MARGIN", "MARGIN"
    MARK_DOWN = "MARK_DOWN", "MARK_DOWN"
    MARK_UP = "MARK_UP", "MARK_UP"


class RequestStatusChoices(TextChoices):
    APPROVED = "APPROVED", "APPROVED"
    DECLINED = "DECLINED", "DECLINED"
    DELIVERED = "DELIVERED", "DELIVERED"
    PENDING = "PENDING", "PENDING"
    TRANSIT = "TRANSIT", "TRANSIT"


class StockHistoryChoices(TextChoices):
    DEPLETE = "DEPLETE", "DEPLETE"
    REFUND = "REFUND", "REFUND"
    REQUEST = "REQUEST", "REQUEST"
    SALES = "SALES", "SALES"
    STOCK_IN = "STOCK_IN", "STOCK_IN"
    STOCK_OUT = "STOCK_OUT", "STOCK_OUT"
    SWAP = "SWAP", "SWAP"
    TRANSFER = "TRANSFER", "TRANSFER"


class StockHistoryStatusChoices(TextChoices):
    INCOMING = "INCOMING", "INCOMING"
    NEUTRAL = "NEUTRAL", "NEUTRAL"
    OUTGOING = "OUTGOING", "OUTGOING"


class ProductSource(TextChoices):
    SAVINGS = "SAVINGS", "SAVINGS"
    MANUAL_ENTRY = "MANUAL_ENTRY", "MANUAL_ENTRY"
