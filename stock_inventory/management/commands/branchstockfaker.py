from datetime import datetime
import pytz

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,
)

from core.models import User
from requisition.models import Company
from stock_inventory import models


############################################################################
############################################################################
available_stock_items = {
    "iphone": [
        {
            "brand": "iPhone 14 Pro",
            "quantity": 10,
            "cost_price": 5000,
            "selling_price": 7500,
        },
        {
            "brand": "iPhone 15 Pro",
            "quantity": 25,
            "cost_price": 6800,
            "selling_price": 9900,
        },
        {
            "brand": "iPhone SE (3rd generation)",
            "quantity": 18,
            "cost_price": 4200,
            "selling_price": 6100,
        },
        {
            "brand": "iPhone 13 Mini",
            "quantity": 12,
            "cost_price": 6500,
            "selling_price": 8700,
        },
        {
            "brand": "iPhone 11",
            "quantity": 9,
            "cost_price": 4000,
            "selling_price": 5800,
        },
        {
            "brand": "iPhone XR",
            "quantity": 7,
            "cost_price": 3500,
            "selling_price": 5200,
        },
        {"brand": "iPhone X", "quantity": 5, "cost_price": 3000, "selling_price": 4500},
        {
            "brand": "iPhone 8 Plus",
            "quantity": 11,
            "cost_price": 2800,
            "selling_price": 4200,
        },
        {"brand": "iPhone 7", "quantity": 8, "cost_price": 2500, "selling_price": 3800},
        {
            "brand": "iPhone 6s Plus",
            "quantity": 6,
            "cost_price": 2000,
            "selling_price": 3200,
        },
    ],
    "samsung": [
        {
            "brand": "Galaxy S23 Ultra",
            "quantity": 8,
            "cost_price": 7200,
            "selling_price": 9800,
        },
        {
            "brand": "Galaxy Z Fold 4",
            "quantity": 15,
            "cost_price": 8500,
            "selling_price": 12000,
        },
        {
            "brand": "Galaxy S22+",
            "quantity": 12,
            "cost_price": 6500,
            "selling_price": 8900,
        },
        {
            "brand": "Galaxy A53 5G",
            "quantity": 10,
            "cost_price": 4200,
            "selling_price": 6100,
        },
        {
            "brand": "Galaxy M53",
            "quantity": 9,
            "cost_price": 3500,
            "selling_price": 5200,
        },
        {
            "brand": "Galaxy A33",
            "quantity": 11,
            "cost_price": 3000,
            "selling_price": 4500,
        },
        {
            "brand": "Galaxy Note 20 Ultra",
            "quantity": 7,
            "cost_price": 5800,
            "selling_price": 8300,
        },
        {
            "brand": "Galaxy S21 FE",
            "quantity": 8,
            "cost_price": 4800,
            "selling_price": 7200,
        },
        {
            "brand": "Galaxy A73",
            "quantity": 6,
            "cost_price": 4000,
            "selling_price": 5800,
        },
        {
            "brand": "Galaxy A13",
            "quantity": 14,
            "cost_price": 2200,
            "selling_price": 3500,
        },
    ],
    "google": [
        {"brand": "Pixel 6", "quantity": 15, "cost_price": 4000, "selling_price": 5000},
        {"brand": "Pixel 5", "quantity": 10, "cost_price": 3500, "selling_price": 4500},
        {
            "brand": "Pixel 4a",
            "quantity": 20,
            "cost_price": 2500,
            "selling_price": 3200,
        },
        {"brand": "Pixel 4", "quantity": 12, "cost_price": 3000, "selling_price": 3800},
        {"brand": "Pixel 3", "quantity": 8, "cost_price": 2000, "selling_price": 2800},
        {"brand": "Pixel 2", "quantity": 6, "cost_price": 1500, "selling_price": 2000},
        {"brand": "Pixel XL", "quantity": 5, "cost_price": 1800, "selling_price": 2200},
        {"brand": "Pixel C", "quantity": 7, "cost_price": 1400, "selling_price": 1900},
        {
            "brand": "Pixel Slate",
            "quantity": 9,
            "cost_price": 3000,
            "selling_price": 3500,
        },
        {
            "brand": "Pixelbook",
            "quantity": 4,
            "cost_price": 4500,
            "selling_price": 6000,
        },
    ],
    "oneplus": [
        {
            "brand": "OnePlus 9 Pro",
            "quantity": 18,
            "cost_price": 4500,
            "selling_price": 6000,
        },
        {
            "brand": "OnePlus 8T",
            "quantity": 22,
            "cost_price": 3500,
            "selling_price": 4500,
        },
        {
            "brand": "OnePlus Nord",
            "quantity": 30,
            "cost_price": 2500,
            "selling_price": 3500,
        },
        {
            "brand": "OnePlus 7 Pro",
            "quantity": 12,
            "cost_price": 3000,
            "selling_price": 4200,
        },
        {
            "brand": "OnePlus 6T",
            "quantity": 10,
            "cost_price": 2000,
            "selling_price": 2800,
        },
        {
            "brand": "OnePlus 5",
            "quantity": 15,
            "cost_price": 1500,
            "selling_price": 2000,
        },
        {
            "brand": "OnePlus 3T",
            "quantity": 8,
            "cost_price": 1800,
            "selling_price": 2300,
        },
        {
            "brand": "OnePlus 2",
            "quantity": 6,
            "cost_price": 1400,
            "selling_price": 1800,
        },
        {
            "brand": "OnePlus X",
            "quantity": 4,
            "cost_price": 1200,
            "selling_price": 1600,
        },
        {
            "brand": "OnePlus 7T",
            "quantity": 20,
            "cost_price": 3200,
            "selling_price": 4000,
        },
    ],
    "xiaomi": [
        {"brand": "Mi 11", "quantity": 25, "cost_price": 3800, "selling_price": 4800},
        {
            "brand": "Redmi Note 10",
            "quantity": 40,
            "cost_price": 2500,
            "selling_price": 3200,
        },
        {"brand": "Poco X3", "quantity": 35, "cost_price": 2000, "selling_price": 2800},
        {"brand": "Mi 10", "quantity": 15, "cost_price": 3000, "selling_price": 3800},
        {"brand": "Redmi 9", "quantity": 30, "cost_price": 1500, "selling_price": 2000},
        {"brand": "Poco F3", "quantity": 20, "cost_price": 2800, "selling_price": 3600},
        {"brand": "Mi A3", "quantity": 18, "cost_price": 1800, "selling_price": 2500},
        {
            "brand": "Redmi Note 9",
            "quantity": 22,
            "cost_price": 2200,
            "selling_price": 2800,
        },
        {"brand": "Poco M2", "quantity": 14, "cost_price": 1400, "selling_price": 1900},
        {"brand": "Mi 9T", "quantity": 12, "cost_price": 2400, "selling_price": 3000},
    ],
    "huawei": [
        {"brand": "P40 Pro", "quantity": 20, "cost_price": 4200, "selling_price": 5200},
        {"brand": "Mate 40", "quantity": 18, "cost_price": 3800, "selling_price": 4800},
        {
            "brand": "P30 Lite",
            "quantity": 25,
            "cost_price": 2000,
            "selling_price": 2500,
        },
        {"brand": "Mate 30", "quantity": 12, "cost_price": 3000, "selling_price": 3800},
        {"brand": "Nova 7", "quantity": 30, "cost_price": 2500, "selling_price": 3200},
        {"brand": "Y9s", "quantity": 22, "cost_price": 1500, "selling_price": 2000},
        {"brand": "P20 Pro", "quantity": 10, "cost_price": 3500, "selling_price": 4500},
        {"brand": "Mate 20", "quantity": 15, "cost_price": 2700, "selling_price": 3500},
        {
            "brand": "Honor 10",
            "quantity": 18,
            "cost_price": 1900,
            "selling_price": 2400,
        },
        {"brand": "P Smart", "quantity": 35, "cost_price": 1300, "selling_price": 1800},
    ],
}
############################################################################
############################################################################


class Command(BaseCommand):
    help = "CREATES STOCKS RECORD(S) FOR THE SPECIFIED COMPANY."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("user_id")
        parser.add_argument("company_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        user = User.objects.filter(id=kwargs["user_id"]).first()
        if user is None:
            raise CommandError("USER RECORD NOT FOUND.")
        company = Company.objects.filter(id=kwargs["company_id"]).first()
        if company is None:
            raise CommandError("COMPANY RECORD NOT FOUND.")
        branch = models.Branch.create_company_branch(
            user=user,
            company=company,
            name="BNPL Head Office I",
            address="Lagos, Nigeria",
            vat=7.5,
            is_super_branch=True,
        )
        # select and create a category.
        for category in available_stock_items:
            category_record = models.Category.retrieve_create_category_name(
                user=user,
                company=company,
                name=category
            )
            # select and create the category associated product(s).
            for item in available_stock_items[category]:
                product_record = models.Product.retrieve_create_product_name(
                    user=user,
                    company=company,
                    category=category_record,
                    name=item["brand"],
                )
                # Upload stock details.
                models.StockDetail.upload_stock_and_variant(
                    user=user,
                    company=company,
                    branch=branch,
                    category=category_record,
                    item=product_record,
                    quantity=item["quantity"],
                    stock_price=item["cost_price"],
                    selling_price=item["selling_price"],
                )
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(self.style.SUCCESS("SUCCESSFULLY UPLOADED ITEM DETAILS."))
