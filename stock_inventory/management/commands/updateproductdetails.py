from datetime import datetime

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
)
import pytz

from stock_inventory.models import StockDetail


class Command(BaseCommand):
    help = "UPDATE INVENTORY DETAILS."

    def handle(self, *args, **kwargs):
        head_branch = "09efed20-983f-41e3-acbe-2e7b23d3c870"
        sub_branch = "73ae3049-ac5b-4e68-8b44-bd9b5eef7918"
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        inventory_record = StockDetail.objects.filter(branch=head_branch)

        for inventory in inventory_record:
            StockDetail.objects.filter(
                branch=sub_branch,
                category=inventory.category,
                item=inventory.item,
            ).update(
                stock_price=inventory.stock_price,
                selling_price=inventory.selling_price,
                stock_value=inventory.stock_value,
            )
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(self.style.SUCCESS("SUCCESSFULLY UPDATED INVENTORY DETAILS."))
