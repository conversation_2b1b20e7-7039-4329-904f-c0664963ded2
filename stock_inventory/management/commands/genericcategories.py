from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import IntegrityError

from stock_inventory.models import Category


logger = settings.LOGGER


# Available generic categories.
product_categories = [
    "Electronics",
    "Clothing and Apparel",
    "Home and Garden",
    "Health and Beauty",
    "Automotive",
    "Sports and Outdoors",
    "Toys and Games",
    "Books and Magazines",
    "Food and Grocery",
    "Jewelry and Accessories",
    "Furniture",
    "Office Supplies",
    "Pet Supplies",
    "Music and Instruments",
    "Art and Craft Supplies",
    "Baby and Kids' Products",
    "Tools and Hardware",
    "Appliances",
    "Fitness and Exercise Equipment",
    "Electronics Accessories",
    "Outdoor and Camping Gear",
    "Party and Event Supplies",
    "Collectibles and Memorabilia",
    "Home Decor",
    "Kitchen and Dining",
    "Travel and Luggage",
    "Shoes and Footwear",
    "Stationery and School Supplies",
    "Industrial and Scientific Equipment",
    "Musical Instruments",
    "Electronics Components",
    "Gardening Supplies",
    "Beauty and Personal Care Products",
    "Cleaning Supplies",
    "Hobbies and Collectibles",
    "Software and Digital Products",
    "Vintage and Antiques",
    "Specialty Foods and Beverages",
    "Office Equipment",
    "Hunting and Fishing Gear",
    "Pharmaceuticals",
    "Medical Equipment and Supplies",
    "Construction Materials",
    "Home Improvement",
    "Building and Construction Tools",
    "Agricultural and Farming Equipment",
    "Industrial Machinery",
    "Chemicals and Raw Materials",
    "Packaging and Labeling Materials",
    "Printing and Publishing",
    "Automotive Parts and Accessories",
    "Electronics Manufacturing Equipment",
    "Aircraft and Aerospace Components",
    "Marine Equipment and Supplies",
    "Energy and Power Generation Equipment",
    "Environmental and Sustainability Products",
    "Educational Supplies and Materials",
    "Scientific and Laboratory Equipment",
    "Security and Surveillance Products",
    "Religious and Spiritual Items",
    "Party and Entertainment Services",
    "Event Planning and Decorations",
    "Home Security and Automation",
    "Renewable Energy Solutions",
    "Home Entertainment Systems",
    "Educational Toys and Games",
    "Customized and Personalized Products",
    "Luxury and High-End Goods",
    "Fine Art and Collectible Paintings",
    "Sports Memorabilia and Merchandise"
]


class Command(BaseCommand):
    help = "CREATES GENERIC CATEGORIES OF PRODUCTS THAT CUTS ACROSS ALL INDUSTRIES."

    def handle(self, *args, **kwargs):
        for name in product_categories:
            try:
                Category.objects.create(name=name)
            except IntegrityError as error:
                logger.error(f"error: category already exists = {error}")
        print(
            "\n\n\nT H E  J O B  I S  D O N E ! ! ! !\n\n\n"
        )
