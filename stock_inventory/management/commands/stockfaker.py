from datetime import datetime
import pytz

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,

)

from core.models import User
from requisition.models import Company
from stock_inventory import models


categories = [
    "fresh produce",
    "dairy and eggs",
    "meat and poultry",
    "seafood",
    "bakery",
    "frozen foods",
    "canned goods",
    "packaged grains",
    "breakfast foods",
    "beverages"
]


items = [
    [
        "apples",
        "bananas",
        "oranges",
        "carrots",
        "broccoli",
        "tomatoes",
        "spinach",
        "bell peppers",
        "onions",
        "potatoes"
    ],
    [
        "whole milk",
        "skim milk",
        "almond milk",
        "eggs",
        "butter",
        "cheddar cheese",
        "mozzarella cheese",
        "yogurt",
        "cottage cheese",
        "sour cream"
    ],
    [
        "chicken breasts",
        "ground beef",
        "pork chops",
        "bacon",
        "ham",
        "turkey breast",
        "lamb chops",
        "sausages",
        "steak",
        "rotisserie chicken"
    ],
    [
        "shrimp",
        "tuna",
        "cod fillets",
        "tilapia",
        "crab legs",
        "mussels",
        "scallops",
        "clams",
        "lobster tails",
        "squid"
    ],
    [
        "baguettes",
        "sourdough bread",
        "ciabatta rolls",
        "whole wheat bread",
        "multigrain bread",
        "danish pastries",
        "muffins",
        "croissants",
        "donuts",
        "cookies"
    ],
    [
        "frozen peas",
        "frozen corn",
        "frozen mixed vegetables",
        "frozen pizza",
        "frozen waffles",
        "frozen chicken nuggets",
        "frozen burritos",
        "frozen fruit popsicle",
        "frozen spinach",
        "frozen french fries"
    ],
    [
        "canned beans",
        "canned tomatoes",
        "canned tuna",
        "canned soup",
        "canned corn",
        "canned fruit",
        "canned vegetables",
        "canned salmon",
        "canned chili",
        "canned olives"
    ],
    [
        "white rice",
        "brown rice",
        "pasta",
        "instant oatmeal",
        "quinoa",
        "couscous",
        "barley",
        "bulgur",
        "polenta",
        "farro"
    ],
    [
        "cereal",
        "pancake mix",
        "maple syrup",
        "breakfast bars",
        "instant oatmeal packets",
        "breakfast sausage links",
        "pancake syrup",
        "instant grits",
        "breakfast cereals",
        "breakfast sandwiches"
    ],
    [
        "bottled water",
        "soda",
        "fruit juice",
        "iced tea",
        "energy drinks",
        "flavoured water",
        "coconut water",
        "sparkling water",
        "coffee",
        "soya milk"
    ]
]


class Command(BaseCommand):
    help = "CREATES STOCKS RECORD(S) FOR THE SPECIFIED COMPANY."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("user_id")
        parser.add_argument("company_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        user = User.objects.filter(id=kwargs["user_id"]).first()
        if user is None:
            raise CommandError("USER RECORD NOT FOUND.")
        company = Company.objects.filter(id=kwargs["company_id"]).first()
        if company is None:
            raise CommandError("COMPANY RECORD NOT FOUND.")
        company_branches = models.Branch.objects.filter(company=company)
        if not company_branches.exists():
            raise CommandError("COMPANY HAS NO BRANCH RECORDs.")

        counter = 0
        products = []

        # select and create a category.
        for category in categories:
            category_record = models.Category.objects.create(
                name=category
            )
            # select and create the category associated product(s).
            for item in items[counter]:
                product_record = models.Product.objects.create(
                    created_by=user,
                    company=company,
                    category=category_record,
                    name=item,
                    all_branches=True
                )
                products.append(product_record)

            counter += 1

        for branch in company_branches:
            # select item and upload stocks.
            for product in products:
                models.StockDetail.upload_stock_and_variant(
                    user=user,
                    company=company,
                    branch=branch,
                    category=product.category,
                    item=product,
                    quantity=120000,
                    stock_price=1250,
                    selling_price=1750
                )
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        print(
            f"TIME DIFFERENCE:  {END_TIME - START_TIME}"
        )
