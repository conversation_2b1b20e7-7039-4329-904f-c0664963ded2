from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
import pytz

from stock_inventory import models


class Command(BaseCommand):
    help = "PRODUCT PRICE VARIATION RECORD(S)."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        products = models.Product.objects.all()
        if products.exists():
            tags = models.PriceTag.objects.filter(system_default=True)
            if tags.exists():
                for product in products:
                    for tag in tags:
                        models.PriceVariation.objects.get_or_create(
                            company=product.company,
                            item=product,
                            price_tag=tag,
                        )
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(
            self.style.SUCCESS(
                "SUCCESSFULLY UPDATED PRODUCT PRICE VARIATION RECORD(S)."
            )
        )
