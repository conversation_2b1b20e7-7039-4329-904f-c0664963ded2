from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Subquery
import pytz

from core.models import User
from stock_inventory.models import PriceList, Product


class Command(BaseCommand):
    help = "UPDATES PRICE LIST RECORD."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # # Query for all distinct combinations of company, category, and item
        # distinct_combinations = PriceList.objects.values(
        #     "company", "category", "item"
        # ).distinct()

        # for combination in distinct_combinations:
        #     # Get all records with the current combination
        #     records = PriceList.objects.filter(
        #         company=combination["company"],
        #         category=combination["category"],
        #         item=combination["item"],
        #     ).order_by("-created_at")
        #     # Delete all but the latest record
        #     if records.count() > 1:
        #         duplicate_records = records[1:]
        #         PriceList.objects.filter(
        #             id__in=Subquery(duplicate_records.values("id"))
        #         ).delete()
        
        user = User.objects.filter(id="fd149850-b107-4a39-ad0e-8852d86690a3").last()
        
        available_product = [
            "bc20b23d-df17-444a-b684-f820f4507223",
            "9137ffa7-5498-4048-bb8c-b4cbbfb2a8f2",
            "ba8d755c-e55b-4dd8-af2b-a1744dc60e0f",
            "853af1a2-312c-4ca9-902d-252ad449c44c",
            "1a9c63b8-4844-4660-9931-26e9fbdc43a2",
            "fd07934a-d7e9-40cf-b83a-becae23ba4f6",
            "a6e65077-a4f7-4040-a45b-3476e5d4ed7a",
            "23b29038-891d-4874-8ed6-dc0a6eb3dab5",
            "69cdc704-4a4b-4c85-a907-9df67dbecc4d",
        ]
        for key in available_product:
            bnpl_product = Product.objects.filter(id=key).last()
            if bnpl_product is not None:
                price_list = PriceList.objects.filter(
                    company=bnpl_product.company,
                    category=bnpl_product.category,
                    item=bnpl_product,
                ).last()
                if price_list is None:
                    PriceList.objects.create(
                        company=bnpl_product.company,
                        category=bnpl_product.category,
                        item=bnpl_product,
                        price=bnpl_product.selling_price,
                        created_by=user,
                    )
                else:
                    price_list.price = bnpl_product.selling_price
                    price_list.save()

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(self.style.SUCCESS("SUCCESSFULLY UPLOADED ITEM DETAILS."))
