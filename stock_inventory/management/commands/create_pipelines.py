from django.core.management.base import BaseCommand
from stock_inventory.models import Branch
from cart_management.models import OrderPipeline, OrderStage

class Command(BaseCommand):
    help = 'Create default pipeline and stages for all existing branches.'

    def handle(self, *args, **kwargs):
        branches = Branch.objects.all()
        stages = [
            "New Order",
            "Processing",
            "Ready For Delivery",
            "Completed",
            "Cancelled",
            "Refunded"
        ]

        for branch in branches:
            # Check if the branch already has a pipeline
            if not OrderPipeline.objects.filter(branch=branch).exists():
                # Create a pipeline for the branch
                pipeline = OrderPipeline.objects.create(
                    name=f"{branch.name} Pipeline",
                    company=branch.company,
                    branch=branch,
                    is_default=True
                )

                # Create stages for the pipeline
                for position, stage_name in enumerate(stages, start=1):
                    OrderStage.objects.create(
                        name=stage_name,
                        position=position,
                        pipeline=pipeline
                    )

                self.stdout.write(self.style.SUCCESS(f'Pipeline and stages created for branch: {branch.name}'))
            else:
                self.stdout.write(self.style.WARNING(f'Pipeline already exists for branch: {branch.name}'))
