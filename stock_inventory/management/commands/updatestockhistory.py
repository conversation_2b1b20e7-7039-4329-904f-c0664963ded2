from datetime import datetime

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
)
import pytz

from stock_inventory.models import StockHistory


class Command(BaseCommand):
    help = "UPDATE INVENTORY DETAILS."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        inventory_record = StockHistory.objects.all()

        for inventory in inventory_record:
            inventory.stock_value = (
                inventory.quantity * inventory.price
            )
            inventory.save()
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(self.style.SUCCESS("SUCCESSFULLY UPDATED STOCK HISTORY DETAILS."))
