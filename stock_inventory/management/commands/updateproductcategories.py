from datetime import datetime
import pytz

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,
)
from django.db.models import Q

from core.models import User
from requisition.models import Company
from stock_inventory import models


class Command(BaseCommand):
    help = "UPDATES PRICE LIST FOR THE SPECIFIED BRANCH."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("user_id")
        parser.add_argument("company_id")
        parser.add_argument("branch_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        user = User.objects.filter(id=kwargs["user_id"]).first()
        if user is None:
            raise CommandError("USER RECORD NOT FOUND.")
        company = Company.objects.filter(id=kwargs["company_id"]).first()
        if company is None:
            raise CommandError("COMPANY RECORD NOT FOUND.")
        branch = models.Branch.objects.filter(id=kwargs["branch_id"]).first()
        if branch is None:
            raise CommandError("BRANCH RECORD NOT FOUND.")
        stock_details = (
            models.PriceList.objects.filter(company=company)
            .filter(Q(all_branches=True) | Q(selected_branches=branch))
            .order_by("item__id")
            .distinct("item")
        )
        for stock in stock_details:
            product = stock.item.name.split(" ")
            if len(product) > 1:
                category_name = product[0]
                category = models.Category.retrieve_create_category_name(
                    name=category_name,
                    company=company,
                    user=user,
                )
                stock.item.category = category
                stock.item.save()
                stock.category = category
                stock.save()
                available_stock = models.StockDetail.objects.filter(
                    branch=branch,
                    item=stock.item,
                ).first()
                if available_stock is not None:
                    available_stock.category = category
                    available_stock.save()
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
        )
        self.stdout.write(
            self.style.SUCCESS("SUCCESSFULLY UPDATED ITEM CATEGORY DETAILS.")
        )
