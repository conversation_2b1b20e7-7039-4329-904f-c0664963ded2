from import_export import resources

from stock_inventory import models


# Create your resource(s) here.
class BranchResource(resources.ModelResource):
    class Meta:
        model = models.Branch


class CategoryResource(resources.ModelResource):
    class Meta:
        model = models.Category


class SubCategoryResource(resources.ModelResource):
    class Meta:
        model = models.SubCategory


class InventoryJournalResource(resources.ModelResource):
    class Meta:
        model = models.InventoryJournal


class InventoryStatusResource(resources.ModelResource):
    class Meta:
        model = models.InventoryStatus


class PurchaseOrderResource(resources.ModelResource):
    class Meta:
        model = models.PurchaseOrder


class PriceListResource(resources.ModelResource):
    class Meta:
        model = models.PriceList


class ProductResource(resources.ModelResource):
    class Meta:
        model = models.Product


class StockDetailResource(resources.ModelResource):
    class Meta:
        model = models.StockDetail


class StockHistoryResource(resources.ModelResource):
    class Meta:
        model = models.StockHistory


class StockOutResource(resources.ModelResource):
    class Meta:
        model = models.StockOut


class StockRequestResource(resources.ModelResource):
    class Meta:
        model = models.StockRequest


class StockTransferResource(resources.ModelResource):
    class Meta:
        model = models.StockTransfer


class StockUniqueIdResource(resources.ModelResource):
    class Meta:
        model = models.StockUniqueId


class StockVariantResource(resources.ModelResource):
    class Meta:
        model = models.StockVariant


class SupplierHistoryResource(resources.ModelResource):
    class Meta:
        model = models.SupplierHistory


class SupplierResource(resources.ModelResource):
    class Meta:
        model = models.Supplier


class StockOnHoldHistoryResource(resources.ModelResource):
    class Meta:
        model = models.StockOnHoldHistory


# class CompanyStoreResource(resources.ModelResource):
#     class Meta:
#         model = models.CompanyStore


class APILogResource(resources.ModelResource):
    class Meta:
        model = models.APILog


class PriceTagResource(resources.ModelResource):
    class Meta:
        model = models.PriceTag


class PriceVariationResource(resources.ModelResource):
    class Meta:
        model = models.PriceVariation
