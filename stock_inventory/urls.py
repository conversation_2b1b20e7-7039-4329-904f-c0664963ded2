from django.urls import include, path

from stock_inventory import views


# Create your url pattern(s) here.
auth_users_urls = [
    path("user/companies", views.UserCompaniesAPIView.as_view()),
]

branch_urls = [
    path("branch/products", views.BranchProductOverviewAPIView.as_view()),
    path("branch/category_stock", views.BranchCategoryAPIView.as_view()),
    path("branch/settings/", views.BranchSettingsAPIView.as_view()),
    path("branches/", views.BranchAPIView.as_view()),
    path("branches/create/", views.BranchCreateAPIView.as_view()),
    path("branch/stock_details", views.BranchStockAPIView.as_view()),
    path("branch/stock_summary", views.BranchInventorySummaryAPIView.as_view()),
    path("branch/available_stock", views.AvailableStockDetailsAPIView.as_view()),
    path(
        "branches/with-instant-web/",
        views.BranchWithInstantWebAPIView.as_view(),
        name="branches-with-instant-web",
    ),
    path(
        "external/query_category_subcategoty/",
        views.GetCreateCategorySubCategory.as_view(),
    ),
]

category_urls = [
    path("categories/", views.CategoriesAPIView.as_view()),
    path("subcategories/", views.SubCategoriesAPIView.as_view()),
]

company_urls = [
    path("user/companies", views.UserCompaniesAPIView.as_view()),
    path("company", views.CompanyOverviewAPIView.as_view()),
    path("company/categories", views.CompanyCategoryOverviewAPIView.as_view()),
    path("company/products", views.CompanyProductOverviewAPIView.as_view()),
    path("companies", views.CompaniesOverviewAPIView.as_view()),
    path("companies/branches", views.UserCompanyBranchesOverviewAPIView.as_view()),
    path("available_categories", views.CompanyProductCategoryAPIView.as_view()),
]

inventory_urls = [
    path("inventory_status", views.BranchInventoryStatusAPIView.as_view()),
    path("inventory_summary", views.BranchInventoryStatusSummaryAPIView.as_view()),
]

price_list_urls = [
    path("price_list/", views.PriceListAPIView.as_view()),
    path("ai_converter/price_list/", views.AIPriceListConverterAPIView.as_view()),
    path("price_list_upload/", views.PriceListUploadView.as_view()),
    path("price_tags/", views.PriceTagAPIView.as_view()),
    path("price_variations/", views.PriceVariationAPIView.as_view()),
]

product_urls = [
    path("products/", views.ProductAPIView.as_view()),
    path("create_instant_web_product/", views.InstantWebProductAPIView.as_view()),
    path(
        "finished-products/",
        views.FinishedProductListCreateView.as_view(),
        name="finishedproduct-list-create",
    ),
    path(
        "finished-products/<int:pk>/",
        views.FinishedProductRetrieveUpdateDestroyView.as_view(),
        name="finishedproduct-detail",
    ),
    # FinishedProductRecipe URLs
    path(
        "finished-product-recipes/",
        views.FinishedProductRecipeListCreateView.as_view(),
        name="finishedproductrecipe-list-create",
    ),
    path(
        "finished-product-recipes/<int:pk>/",
        views.FinishedProductRecipeRetrieveUpdateDestroyView.as_view(),
        name="finishedproductrecipe-detail",
    ),
    path(
        "company/<uuid:company_id>/branch/<uuid:branch_id>/product/<uuid:product_id>/orders/",
        views.ProductOrdersView.as_view(),
        name="product-orders",
    ),
    path("manage_products/", views.ManageProductAPIView.as_view()),
    path("deplete/", views.DepleteStockAPIView.as_view()),
    path("products/unlist/", views.UnlistProductAPIView.as_view()),
]


purchase_order_urls = [
    path("purchase_orders/", views.PurchaseOrderAPIView.as_view()),
    path("purchase_details", views.PurchaseOrderDetailsAPIView.as_view()),
    path("purchase_summary", views.PurchaseOrderSummaryAPIView.as_view()),
]
stock_actions_urls = [
    path("manual_upload/", views.StockManualUploadAPIView.as_view()),
    path("file_upload/", views.StockFileUploadAPIView.as_view()),
    path("download_sample_file", views.StockSampleSheetAPIView.as_view()),
    path("chat_upload/", views.ChatStockUploadAPIView.as_view()),
    path("ai_converter/", views.AIStockConverterAPIView.as_view()),
    path("download_sample_image", views.AIStockSampleImageAPIView.as_view()),
    path("ai_upload/", views.AIStockUploadAPIView.as_view()),
    path("stock-with-bulk/", views.StockAPIView.as_view()),
]

stock_history_urls = [
    path("histories", views.BranchStockHistoryAPIView.as_view()),
]

stock_out_urls = [
    path("stockout_details", views.BranchStockOutDetailsAPIView.as_view()),
    path("stockout_requests/", views.StockOutAPIView.as_view()),
    path("stockout_action/", views.BranchStockOutActionAPIView.as_view()),
]

stock_request_urls = [
    path("requests/", views.StockRequestAPIView.as_view()),
    path("requests_summary", views.BranchStockRequestDetailsAPIView.as_view()),
    path("request_details", views.RequestDetailsAPIView.as_view()),
    path("request_action/", views.StockRequestActionAPIView.as_view()),
    path("request_transit/", views.BranchStockRequestTransitAPIView.as_view()),
    path("request_received/", views.BranchStockRequestReceiveAPIView.as_view()),
]

stock_transfer_urls = [
    path("transfers/", views.StockTransferAPIView.as_view()),
    path("transfer_details", views.StockTransferDetailsAPIView.as_view()),
    path("transfer_summary", views.StockTransferSummaryAPIView.as_view()),
]

stock_unique_id_urls = [
    path("unique_ids/", views.UniqueIdAPIView.as_view()),
]

supplier_urls = [
    path("suppliers/", views.SupplierAPIView.as_view()),
    path("suppliers/ratings", views.SupplierRatingAPIView.as_view()),
    path("suppliers/histories", views.SupplierHistoryAPIView.as_view()),
    path("suppliers/product", views.SupplierProductAPIView.as_view()),
    path("suppliers/brand", views.BrandListAPIView.as_view()),
    path("suppliers/product/<str:pk>", views.SupplierProductAPIView.as_view()),
    path("suppliers/sub-categories", views.SubCategoryListAPIView.as_view()),
    path("suppliers/product-image", views.UploadSupplierProductImageAPIView.as_view()),
    path("suppliers/product-image/<str:pk>", views.UploadSupplierProductImageAPIView.as_view()),
    path("suppliers/bnpl-product", views.BNPLSupplierProduct.as_view()),
    path("suppliers/bnpl-product/<str:pk>", views.BNPLSupplierProduct.as_view()),
]

variant_urls = [
    path("variants", views.StockVariantAPIView.as_view()),
]

# web_store = [
#     path("create_store/", views.CreateStoreAPIView.as_view()),
#     path("edit_store/", views.EditStoreAPIView.as_view()),
#     path("view_company_store/", views.ViewStoreDataAPIView.as_view()),
#     path("edit_company_logo/", views.EditStoreLogoAPIView.as_view()),
#     path("edit_company_header_image/", views.EditStoreHeaderImageAPIView.as_view()),
# ]

urlpatterns = [
    path("", include(branch_urls)),
    path("", include(category_urls)),
    path("", include(company_urls)),
    path("", include(inventory_urls)),
    path("", include(purchase_order_urls)),
    path("", include(price_list_urls)),
    path("", include(product_urls)),
    path("", include(stock_actions_urls)),
    path("", include(stock_history_urls)),
    path("", include(stock_out_urls)),
    path("", include(stock_request_urls)),
    path("", include(stock_transfer_urls)),
    path("", include(stock_unique_id_urls)),
    path("", include(supplier_urls)),
    path("", include(variant_urls)),
    # path("", include(web_store)),
]
