from datetime import datetime, timedelta
from django.utils import timezone
from .helpers import filter_dates, get_workdays_in_month
from django.db.models import Sum, Count
from core.models import User
from requisition.models import Company
from payroll_app.models import CompanyEmployeeList as Employee, CompanyPayrollSettings as CompanyProfile
from leave_management.models import LeaveRecord, LeaveRequest
from .models import (
    Break,
    Clock,
    CompanyMeeting,
    Location,
    OverTime,
    Record,
    Task,
    Screenshot,
    AttendanceSheet)

# Utility service manager(s).
employees_qs = Employee.objects.filter(is_deleted=False)
record_qs = Record.objects.filter(is_deleted=False)
task_qs = Task.objects.filter(is_deleted=False)
overtime_qs = OverTime.objects.filter(is_deleted=False)
screenshot_qs = Screenshot.objects.filter(is_deleted=False)
attendance_sheet_qs = AttendanceSheet.objects.filter(is_deleted=False)
leave_request_qs = LeaveRequest.objects.filter(is_deleted=False)

records = record_qs.values(
    'id', 'employee', 'created_at__date', 'company', 'clock_in_time', 'lateness_duration',
    'is_late', 'is_break_exceeded', 'break_duration', 'break_excess', 'overtime__overtime_end_time',
    'overtime_duration', 'clock_out_time', 'work_duration', 'task', 'screenshot', 'location')

employees = employees_qs.values(
    'employee__id', 'employee_first_name', 'employee_last_name', 
    'employee_email', 'company', 'created_at__date')
    
tasks = task_qs.values(
    'id', 'record', 'project_name', 'title', 'created_by', 'assignee', 'start_date', 'due_date',
    'task_description', 'status', 'challenges', 'is_deleted', 'created_at__date')


class DateFilter:
    def __init__(self):
        filter_date = filter_dates(datetime=datetime)
        self.inception = filter_date.get("inception")
        self.today = filter_date.get("today")

    def get_date_filter(self, request):
        filter = request.query_params.get("filter")

        specific_day = request.query_params.get("specific_day")
        if specific_day is not None:
            specific_day = datetime.strptime(specific_day, "%Y-%m-%d")
            self.date_filter = {"date_created__date": specific_day}
            self.date_filter_two = {"created_at__date": specific_day}
            self.date_filter_three = {"updated_at__date": specific_day}
            return {"date_filter": self.date_filter,
                    "date_filter_two": self.date_filter_two,
                    "date_filter_three": self.date_filter_three}

        if filter is not None:
            self.date_filter = {"date_created__date": timezone.now().date()}
            self.date_filter_two = {"created_at__date": timezone.now().date()}
            self.date_filter_three = {"updated_at__date": timezone.now().date()}
            return {"date_filter": self.date_filter, 
                    "date_filter_two": self.date_filter_two,
                    'date_filter_three': self.date_filter_three}
        else:
            self.date_filter = {"date_created__date__gte": self.inception}
            self.date_filter_two = {"created_at__date__gte": self.inception}
            self.date_filter_three = {"updated_at__date__gte": self.inception}
            return {"date_filter": self.date_filter, 
                    "date_filter_two": self.date_filter_two,
                    'date_filter_three': self.date_filter_three}


class ActivitiesDashboard:
    def __init__(self, request):
        today = datetime.now()
        self.today = DateFilter().today

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")
        self.company_id=request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.total_employees = employees_qs.filter(company=company).count()
        self.active_employees = record_qs.filter(date_created__date=today.date(), company=company).count()
        self.absent = self.total_employees - self.active_employees
        self.late = record_qs.filter(date_created__date=today.date(), is_late=True, company=company).count()
        self.overtime = record_qs.filter(company=company, date_created__date=today.date(), 
                                         overtime_duration__gt = "0 00:00:00").count()
        self.on_leave = leave_request_qs.filter(company=company, status="APPROVED").count()

        self.overtime_rate = (self.overtime if self.overtime else 0) / \
            (self.total_employees if self.total_employees else 1) * 100
        self.absence_rate = (self.absent if self.absent else 0) / \
            (self.total_employees if self.total_employees else 1) * 100
        self.lateness_rate = (self.late if self.late else 0) / \
            (self.total_employees if self.total_employees else 1) * 100

        total = employees_qs.filter(company=company).count()

        present = record_qs.filter(**self.filter, company=company).count()
        on_leave = leave_request_qs.filter(**self.filter_three, company=company).count()
        absent = total - (present)
        on_time = record_qs.filter(**self.filter, company=company,
                                   is_late=False).count()
        late = record_qs.filter(**self.filter, company=company,
                                is_late = True).count()
        overtime = record_qs.filter(company=company, **self.filter,
                                    overtime_duration__gt = "0 00:00:00").count()

        self.work_trends = [present, absent, on_time, late, overtime, on_leave]

        attendance_list = []
        full_records = []
        serial_number = 1
        
        records = record_qs.filter(company=company, **self.filter).values('id', 'employee', 
                            'date_created__date','company', 'clock__clock_in', 
                            'lateness_duration', 'is_late', 'is_break_exceeded', 'break_duration', 
                            'break_excess', 'overtime__overtime_end_time', 'overtime_duration', 
                            'clock__clock_out', 'work_duration', 'task', 'screenshot', 'location',
                            'clock_in_time', 'clock_out_time', 'attendance', 'location_type',
                            ).order_by('-date_created')

        for record in records:
            record_id = record['id']
            this_name = Employee.objects.get(id=record["employee"])
            full_name = this_name.full_name if this_name else ""
            if record["location"]:
                this_location = Location.objects.get(id=record["location"])
                location_name = this_location.name
            else:
                location_name = "REMOTE"
            company_name = company.company_name
            clock_in_time = record['clock_in_time'] if record['clock_in_time'] else ""
            clock_out_time = record['clock_out_time'] if record['clock_out_time'] else ""
            record_date = record['date_created__date']
            lateness_duration = record['lateness_duration'] if record['lateness_duration'] else 0
            break_duration = record['break_duration'] if record['break_duration'] else 0
            overtime_duration = record['overtime_duration'] if record['overtime_duration'] else 0
            work_duration = record['work_duration'] if record['work_duration'] else 0
            attendance_list.append({
                's_n': serial_number,
                'id': record_id,
                'name': full_name,
                'date': record_date,
                'company': company_name,
                'location': location_name,
                'clock_in': clock_in_time,
                'lateness_duration': lateness_duration,
                'break_duration': break_duration,
                'overtime_duration': overtime_duration,
                'clock_out': clock_out_time,
                'work_duration': work_duration
            })
            full_records.append({
                's/n': serial_number,
                'id': record_id,
                'name': full_name,
                'date': record_date,
                'company': record['company'],
                'location': location_name,
                'clock_in': clock_in_time,
                'lateness_duration': lateness_duration,
                'is_late': record['is_late'],
                'is_break_exceeded': record['is_break_exceeded'],
                'break_duration': break_duration,
                'break_excess': record['break_excess'],
                'overtime_end': record['overtime__overtime_end_time'],
                'overtime_duration': overtime_duration,
                'clock_out': clock_out_time,
                'work_duration': work_duration
            })
            serial_number += 1
        self.attendance_list = attendance_list
        self.full_records = full_records

    # def get_activities_dashboard_metrics(self):
    #     data = {
    #         "total": self.total_employees if self.total_employees else 0,
    #         "active_employees": self.active_employees if self.active_employees else 0,
    #         "absent": self.absent if self.absent else 0,
    #         "late": self.late if self.late else 0,
    #         "overtime": self.overtime if self.overtime else 0,
    #         "on_leave": self.on_leave if self.on_leave else 0
    #     }
    #     return data

    # def get_weekly_record_stats(self):
    #     data = {
    #         "overtime_rate": self.overtime_rate if self.overtime_rate else 0,
    #         "absence_rate": self.absence_rate if self.absence_rate else 0,
    #         "lateness_rate": self.lateness_rate if self.lateness_rate else 0
    #     }
    #     return data

    def get_work_trends(self):
        data = {
            "keys": ['present', 'absent', 'on_time', 'late', 'overtime', 'on_leave'],
            "values": self.work_trends
        }
        return data

    def get_attendance(self):
        data = {
            "attendance": self.attendance_list if self.attendance_list else [],
            "count": len(self.attendance_list)
        }
        return data


class EmployeesRecord:
    def __init__(self, request):
        self.today = DateFilter().today

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")
        self.company_id=request.query_params.get('company_uuid')
        self.company = Company.objects.filter(id=self.company_id).last()
        self.employee_id=request.query_params.get('employee_id')
        self.employee = Employee.objects.filter(id=self.employee_id, company=self.company, is_deleted=False).last()

        employee_list = []
        serial_number = 1
        
        employees = employees_qs.filter(company=self.company, **self.filter_two).values('employee__id', 
                                                                               'employee_first_name', 
                                                                               'employee_last_name',
                                                                               'employee_email', 
                                                                               'company__company_name', 
                                                                               'created_at__date',
                                                                               'employee_status').order_by('-created_at')

        for employee in employees:
            employee_list.append({
                's/n': serial_number,
                'first_name': employee['employee_first_name'],
                'last_name': employee['employee_last_name'],
                'email': employee['employee_email'],
                'company': employee['company__company_name'],
                'employee_id': employee['employee__id'],
                'date_added': employee['created_at__date'],
                'status': employee['employee_status']
            })
            serial_number += 1

        self.employee_list = employee_list
        
        self.work_hours = record_qs.filter(company=self.company, employee=self.employee, **self.filter_two
                                           ).aggregate(Sum('work_duration'))['work_duration__sum'] or 0
        self.overtime = record_qs.filter(company=self.company, **self.filter_two, employee=self.employee
                                         ).aggregate(Sum('overtime_duration'))['overtime_duration__sum'] or 0
        self.breaks_taken = record_qs.filter(company=self.company, **self.filter_two, employee=self.employee
                                             ).aggregate(Sum('break_duration'))['break_duration__sum'] or 0
        self.lateness = record_qs.filter(company=self.company, **self.filter_two, employee=self.employee
                                         ).aggregate(Sum('lateness_duration'))['lateness_duration__sum'] or 0
        self.screenshots = screenshot_qs.filter(company=self.company, **self.filter_two, record__employee=self.employee
                                                ).aggregate(Count('id'))['id__count'] or 0
        self.location = record_qs.filter(company=self.company, **self.filter_two, 
                            employee=self.employee).values_list('location__name', flat=True).distinct()
        self.task_completed = task_qs.filter(company=self.company, **self.filter_two, status='COMPLETED',
                            assignee=self.employee).aggregate(Count('id'))['id__count'] or 0


    def get_employees_list(self):
        data = {
            "employee_record": self.employee_list if self.employee_list else None,
            "count": len(self.employee_list)
        }
        return data

    def get_work_history(self):
        data = {
            'email': self.employee.employee_email,
            'phone': self.employee.employee_phone_number if self.employee.employee_phone_number else "", 
            'company': self.company.company_name, 
            'employee_id': self.employee.employee.id, 
            'date_added': self.employee.created_at, 
            'status': self.employee.employee_status,
            'work_hours': self.work_hours if self.work_hours else 0,
            'overtime': self.overtime if self.overtime else 0,
            'breaks_taken': self.breaks_taken if self.breaks_taken else 0,
            'lateness': self.lateness if self.lateness else 0,
            'screenshots': self.screenshots if self.screenshots else 0,
            'location': self.location if self.location else 0,
            'task_completed': self.task_completed if self.task_completed else 0
        }
        return data


class AttendanceRecord:
    def __init__(self, request):        
        today = datetime.now()
        self.today = DateFilter().today
        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")
        self.company_id=request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.active_employees = record_qs.filter(date_created__date=today, company=company).count()
        self.late = record_qs.filter(date_created__date=today, is_late=True, company=company).count()
        self.overtime = record_qs.filter(date_created__date=today, overtime_duration__gt = "0 00:00:00",
                                         company=company).count()
        self.screenshots = screenshot_qs.filter(date_created__date=today, company=company).count()
        
        self.employee_id=request.query_params.get('employee_id')
        self.employee = Employee.objects.filter(id=self.employee_id).last()
        self.employee_record =  record_qs.filter(company=company, employee=self.employee, date_created__date=today.date()).last()
        self.employee_tasks = task_qs.filter(company=company, assignee=self.employee, status="IN PROGRESS")
        self.employee_screenshots = screenshot_qs.filter(date_created__date=today.date(), company=company, record__employee=self.employee).last()
        self.employee_overtime = overtime_qs.filter(date_created__date=today.date(), company=company, record__employee=self.employee).last()

        attendance_list = []
        serial_number = 1

        records = record_qs.filter(company=company, **self.filter).values('id', 'employee', 
                            'date_created__date','company__company_name', 'clock__clock_in', 
                            'lateness_duration', 'is_late', 'is_break_exceeded', 'break_duration', 
                            'break_excess', 'overtime__overtime_end_time', 'overtime_duration', 
                            'clock__clock_out', 'work_duration', 'task', 'screenshot', 'location',
                            'clock_in_time', 'clock_out_time',
                            ).order_by('-date_created')

        for record in records:
            record_id = record['id']
            this_name = Employee.objects.get(id=record["employee"])
            full_name = this_name.full_name if this_name else ""
            if record["location"]:
                this_location = Location.objects.get(id=record["location"])
                location_name = this_location.name
            else:
                location_name = "REMOTE"
            company_name = record['company__company_name']
            clock_in_time = record['clock_in_time'] if record['clock_in_time'] else ""
            clock_out_time = record['clock_out_time'] if record['clock_out_time'] else ""
            record_date = record['date_created__date']
            lateness_duration = record['lateness_duration'] if record['lateness_duration'] else 0
            break_duration = record['break_duration'] if record['break_duration'] else 0
            overtime_duration = record['overtime_duration'] if record['overtime_duration'] else 0
            work_duration = record['work_duration'] if record['work_duration'] else 0

            attendance_list.append({
                's_n': serial_number,
                'id': record_id,
                'name': full_name,
                'company': company_name,
                'date': record_date,
                'location': location_name,
                'clock_in': clock_in_time,
                'lateness_duration': lateness_duration,
                'break_duration': break_duration,
                'overtime_duration': overtime_duration,
                'clock_out': clock_out_time,
                'work_duration': work_duration
            })
            serial_number += 1
        self.attendance_list = attendance_list

    def get_attendance_record_metrics(self):
        data = {
            "present_employees_today": self.active_employees if self.active_employees else 0,
            "late_employees_today": self.late if self.late else 0,
            "total_overtimes_today": self.overtime if self.overtime else 0,
            "total_screenshots_today": self.screenshots if self.screenshots else 0
        }
        return data
    
    def get_attendance_list(self):
        data = {
            "attendance_records": self.attendance_list if self.attendance_list else [],
            "count": len(self.attendance_list)
        }
        return data

    def get_user_details(self):
        data = {
            'employee_id': self.employee.employee.id,
            'email': self.employee.employee_email,
            'phone_number': self.employee.employee_phone_number if self.employee.employee_phone_number else "", 
            'date_joined': self.employee.created_at, 
            'clock_in_time': self.employee_record.clock_in_time if self.employee_record.clock_in_time else "",
            'clock_out_time': self.employee_record.clock_out_time if self.employee_record.clock_out_time else "",
            'location': self.employee_record.location.name if self.employee_record.location else "REMOTE",
            'lateness': self.employee_record.lateness_duration if self.employee_record.lateness_duration else 0,
            'break_duration': self.employee_record.break_duration if self.employee_record.break_duration else 0,
            'work_duration': self.employee_record.work_duration if self.employee_record.work_duration else 0,
            'overtime_start': self.employee_overtime.overtime_start_time if self.employee_overtime else "",
            'overtime_end': self.employee_overtime.overtime_end_time if self.employee_overtime else "",
            'ongoing_tasks': self.employee_tasks,
            'screenshots': self.employee_screenshots if self.employee_screenshots else 0,
        }
        return data


class TaskRecord:
    def __init__(self, request):
        self.today = DateFilter().today

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")
        self.company_id = request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.total_assigned_tasks = task_qs.filter(company=company).count()
        self.total_pending = task_qs.filter(company=company, status__icontains= 'PENDING').count()
        self.total_in_progress = task_qs.filter(company=company, status__icontains= 'IN_PROGRESS').count()
        self.total_completed = task_qs.filter(company=company, status__icontains= 'COMPLETED').count()

        task_list = []
        serial_number = 1
        tasks = task_qs.filter(company=company, **self.filter).order_by('-date_created')
        for task in tasks:
            task_list.append({
                's/n': serial_number,
                'id': task.id,
                'task_name': task.project_name,
                'description': task.task_description,
                'assigned_to': [assignee.employee_email for assignee in task.assignee.all()],
                'priority': task.priority,
                'due_date': task.date_created.date(),
                'status': task.status
            })
            serial_number += 1
        self.task_list = task_list

        self.task_id=request.query_params.get('task_id')
        self.task = Task.objects.filter(id=self.task_id).last()      

    def get_task_metrics(self):
        data = {
            "total_assigned_tasks": self.total_assigned_tasks if self.total_assigned_tasks else 0,
            "total_pending": self.total_pending if self.total_pending else 0,
            "total_in_progress": self.total_in_progress if self.total_in_progress else 0,
            "total_completed": self.total_completed if self.total_in_progress else 0
        }
        return data

    def get_task_list(self):
        data = {
            "task_records": self.task_list if self.task_list else None,
            "count": len(self.task_list)
        }
        return data

    def get_ongoing_details(self):
        data = {
            'assigned_to': [assignee.employee_email for assignee in self.task.assignee.all()],
            'date_created': self.task.date_created,
            'due_date': self.task.due_date,
            'project': self.task.project_name,
            'description': self.task.task_description,
            'priority': self.task.priority
        }
        return data


class EmployeeAttendanceDashboard:
    def __init__(self, request):
        today = timezone.now()
        year = today.year
        month = today.month
        self.today = DateFilter().today

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")
        self.company_id = request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()
        
        self.employee = Employee.objects.filter(employee=request.user, company=company, is_deleted=False).last()

        self.total_hours_worked = record_qs.filter(employee=self.employee,
                                                    company=company, date_created__year=year,
                                                    date_created__month=month).aggregate(Sum('work_duration'))['work_duration__sum'] or 0
                                                        
        workdays_count = get_workdays_in_month()
        company_profile = CompanyProfile.objects.filter(company=company).last()
        self.total_required_work_hours = company_profile.calculate_total_seconds * workdays_count

        task_list = []
        serial_number = 1

        tasks = task_qs.filter(assignee__employee = request.user, company = company).order_by('-date_created')

        for task in tasks:
            task_list.append({
                's/n': serial_number,
                'id': task.id,
                'task_name': task.project_name,
                'description': task.task_description,
                'assigned_to': [assignee.employee_email for assignee in task.assignee.all()],
                'due_date': task.date_created.date(),
                'status': task.status
            })
            serial_number += 1
        self.task_list = task_list
        
        self.task_id=request.query_params.get('task_id')
        self.task = Task.objects.filter(id=self.task_id).last()

    def get_employee_attendance_metrics(self):
        data = {
            "total_hours_worked": self.total_hours_worked,
            "total_required_work_hours": self.total_required_work_hours if self.total_required_work_hours else 0,
        }
        return data

    def get_task_list(self):
        data = {
            "task_records": self.task_list if self.task_list else [],
            "count": len(self.task_list)
        }
        return data

    def get_ongoing_details(self):
        all_task = [assignee.employee_email for assignee in self.task.assignee.all()] if self.task else []
        task_date = self.task.date_created if self.task else ""
        task_due_date = self.task.due_date if self.task else ""
        task_project = self.task.project_name if self.task else ""
        task_priority = self.task.priority if self.task else ""
        task_description = self.task.task_description if self.task else ""
        if self.task:
            data = {      
                'assigned_to': all_task,
                'date_created': task_date,
                'due_date': task_due_date,
                'project': task_project,
                'priority': task_priority,
                'description': task_description,
            }
        else:
            data = None
        
        return data


class EmployeeAssignedTask:
    def __init__(self, request):
        self.today = DateFilter().today

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")
        self.company_id = request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.total_assigned_tasks = task_qs.filter(assignee__employee = request.user, company = company).count()
        self.total_pending = task_qs.filter(assignee__employee = request.user, status__icontains= 'PENDING', company = company).count()
        self.total_in_progress = task_qs.filter(assignee__employee = request.user, status__icontains= 'IN_PROGRESS', company = company).count()
        self.total_completed = task_qs.filter(assignee__employee = request.user, status__icontains= 'COMPLETED', company = company).count()
        self.total_overdue = task_qs.filter(assignee__employee = request.user, status__in = ['IN_PROGRESS', 'PENDING'], company = company, date_updated__date__lt = timezone.now() ).count()

        task_list = []
        serial_number = 1
        tasks = task_qs.filter(assignee__employee = request.user, company = company, 
                               **self.filter).order_by('-date_created__date')

        for task in tasks:
            task_list.append({
                's/n': serial_number,
                'id': task.id,
                'task_name': task.project_name,
                'description': task.task_description,
                'assigned_to': [assignee.employee_email for assignee in task.assignee.all()],
                'due_date': task.date_created.date(),
                'status': task.status
            })
            serial_number += 1
        self.assigned_tasks = task_list

        
        self.task_id=request.query_params.get('task_id')
        self.task = Task.objects.filter(id=self.task_id).last() 

    def get_employee_assigned_task_metrics(self):
        data = {
            "total_assigned_tasks": self.total_assigned_tasks if self.total_assigned_tasks else 0,
            "total_pending": self.total_pending if self.total_pending else 0,
            "total_in_progress": self.total_in_progress if self.total_in_progress else 0,
            "total_completed": self.total_completed if self.total_completed else 0,
            "total_overdue": self.total_overdue if self.total_overdue else 0
        }
        return data

    def get_task_list(self):
        data = {
            "assigned_task": self.assigned_tasks if self.assigned_tasks else None,
            "count": len(self.assigned_tasks)
        }
        return data

    def get_ongoing_details(self):
        data = {
            'assigned_to': self.task.assignee,
            'date_created': self.task.date_created,
            'due_date': self.task.due_date,
            'project': self.task.project_name,
            'description': self.task.task_description
        }
        return data


class EmployeeAttendanceRecord: 
    def __init__(self, request):
        self.today = DateFilter().today

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.filter_two = DateFilter().get_date_filter(request=request).get("date_filter_two")
        self.filter_three = DateFilter().get_date_filter(request=request).get("date_filter_three")

        self.company_id = request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.total_work_hours = list(record_qs.filter(employee__employee = request.user, company = company
                                                      ).aggregate(Sum('work_duration')).values())[0]
        self.total_lateness_duration = list(record_qs.filter(employee__employee = request.user, company = company
                                                             ).aggregate(Sum('lateness_duration')).values())[0]
        self.total_overtime_duration = list(record_qs.filter(employee__employee = request.user, company = company
                                                             ).aggregate(Sum('overtime_duration')).values())[0]
        self.total_break_duration = list(record_qs.filter(employee__employee = request.user, company = company
                                                          ).aggregate(Sum('break_duration')).values())[0]

        record_list = []
        serial_number = 1

        records = record_qs.filter(employee__employee = request.user, 
                                   company = company).order_by('-date_created')

        for record in records:
            record_list.append({
                's/n': serial_number,
                'id': record.id,
                'date': record.created_at.date(),
                'location': record.location.name if record.location else "REMOTE",
                'clock_in': record.clock_in_time if record.clock_in_time else "",
                'lateness_duration': record.lateness_duration if record.lateness_duration else 0,
                'break_duration': record.break_duration if record.break_duration else 0,
                'overtime_duration': record.overtime_duration if record.overtime_duration else 0,
                'clock_out': record.clock_out_time if record.clock_out_time else "",
            })
            serial_number += 1
        self.attendance_records = record_list

    def get_employee_attendance_metrics(self):
        data = {
            "total_work_hours": self.total_work_hours if self.total_work_hours else 0,
            "total_lateness_duration": self.total_lateness_duration if self.total_lateness_duration else 0,
            "total_overtime_duration": self.total_overtime_duration if self.total_overtime_duration else 0,
            "total_break_duration": self.total_break_duration if self.total_break_duration else 0,
        }
        return data

    def get_record_list(self):
        data = {
            "attendance_records": self.attendance_records if self.attendance_records else None,
            "count": len(self.attendance_records)
        }
        return data


class EmployeeStatus:
    def __init__(self, request):
        today = datetime.now()
        company_id = request.query_params.get("company_uuid")
        self.company = Company.objects.filter(id=company_id, is_deleted=False).first()
        self.user = Employee.objects.filter(employee=request.user, company=self.company, is_deleted=False).first()
        record_data = Record.objects.filter(employee=self.user, 
                                                date_created__date=today.date(), company=self.company)
        self.is_clocked_in = record_data
        self.is_clocked_out = record_data.filter(clock_in_time__isnull=False, clock_out_time__isnull=False).exists()
        record = record_data.last()
        closing_time = CompanyProfile.objects.filter(company=self.company).first().closing_time
        self.meeting = CompanyMeeting.objects.filter(company=self.company, meeting_date=today.date()).first()
        if record and record.work_duration:
            self.work_duration=record.work_duration
            self.is_on_overtime=False
            self.on_going_overtime_duration=timedelta(hours=0, minutes=0, seconds=0, microseconds=0)
        else:
            self.work_duration=timedelta(hours=0, minutes=0, seconds=0, microseconds=0)
            if self.is_clocked_out is False and today.time() > closing_time:
                self.is_on_overtime=True 
            else:
                self.is_on_overtime=False

            if self.is_on_overtime:
                closing = datetime.combine(today.date(), closing_time)
                self.on_going_overtime_duration=today-closing
            else:
                self.on_going_overtime_duration=timedelta(hours=0, minutes=0, seconds=0, microseconds=0)


        self.has_an_overtime=OverTime.objects.filter(record__employee=self.user, 
                                                    overtime_start_time__isnull=False,
                                                    overtime_end_time__isnull=False, date_created__date=today.date(),
                                                    company=self.company).exists()
        if self.has_an_overtime:
            overtime_record=OverTime.objects.filter(record__employee=self.user,
                                                    overtime_start_time__isnull=False,
                                                    overtime_end_time__isnull=False, date_created__date=today.date(),
                                                    company=self.company).last()
            self.overtime_duration=overtime_record.overtime_end_time-overtime_record.overtime_start_time
        else:
            self.overtime_duration=timedelta(hours=0, minutes=0, seconds=0, microseconds=0)
        self.is_on_break=Break.objects.filter(record__employee=self.user, break_start_time__isnull=False,
                                            break_end_time__isnull=True, date_created__date=today.date(),
                                            company=self.company).exists()

        
        self.break_duration = Break.objects.filter(record__employee=self.user, break_start_time__isnull=False,
                                            break_end_time__isnull=False, date_created__date=today.date(),
                                            company=self.company).aggregate(total_duration=Sum('break_duration'))['total_duration'] or timedelta(hours=0, minutes=0, seconds=0, microseconds=0)

        time_wasted = self.break_duration        


        if self.is_on_break:
            break_start_time=Break.objects.filter(record__employee=self.user, 
                                            break_start_time__isnull=False,
                                            break_end_time__isnull=True, date_created__date=today.date(),
                                            company=self.company).last().break_start_time
            break_start_timezone=break_start_time.tzinfo
            now=datetime.now(break_start_timezone)
            self.on_going_break_duration=now-break_start_time
            time_wasted += self.on_going_break_duration
        else:
            self.on_going_break_duration=timedelta(hours=0, minutes=0, seconds=0, microseconds=0)

        if self.is_clocked_in:
            clock_in = record_data.filter(clock_in_time__isnull = False,
                                        ).last().clock_in_time

            if today.time() <= closing_time:
                closing_time = datetime.combine(datetime.today(), today.time())
                clock_in_time = datetime.combine(datetime.today(), clock_in) 
                work_duration = closing_time - clock_in_time
            else: 
                closing_time = datetime.combine(datetime.today(), closing_time)
                clock_in_time = datetime.combine(datetime.today(), clock_in)           
                work_duration = timedelta(hours=0, minutes=0, seconds=0, microseconds=0)

            if not time_wasted:
                self.on_going_work_duration=work_duration
    
            else:
                work_duration_in_seconds = work_duration.total_seconds()
                time_wasted_in_seconds = time_wasted.total_seconds()
                work_duration_in_seconds -= time_wasted_in_seconds
                updated_work_duration = timedelta(seconds=work_duration_in_seconds)
                self.on_going_work_duration = updated_work_duration
        else:
                self.on_going_work_duration=timedelta(hours=0, minutes=0, seconds=0, microseconds=0)
                
    def get_employee_status(self):
        data={
            'clocked_in': True if self.is_clocked_in else False,
            'clocked_out': self.is_clocked_out,
            'is_on_break': self.is_on_break,
            'on_going_break_duration': self.on_going_break_duration,
            'break_duration': self.break_duration,
            'is_on_overtime': self.is_on_overtime,
            'on_going_overtime_duration': self.on_going_overtime_duration,
            'overtime_duration': self.overtime_duration,
            'on_going_work_duration': self.on_going_work_duration,
            'work_duration': self.work_duration,
            'meeting_link_time': self.meeting.meeting_time if self.meeting else ""
        }   

        return data


class AttendanceSheetPopup:
    def __init__(self, request):        
        company_id=request.query_params.get('company_uuid')
        self.company=Company.objects.filter(id=company_id).last()
        attendance_sheets = attendance_sheet_qs.filter(company=self.company).order_by('date_created__date')
        self.company_attendance_sheets=[]
        for attendance_sheet in attendance_sheets:
            self.company_attendance_sheets.append({
                'id': attendance_sheet.id,
                'title': attendance_sheet.title,
                'description': attendance_sheet.description
            })
    def get_attendance_sheets(self):
        data={
            'attendance_sheet': self.company_attendance_sheets,
            'count': len(self.company_attendance_sheets)
        }
        return data


class AttendanceSheetEmployee:
    def __init__(self, request):
        company_id=request.query_params.get('company_uuid')
        self.company=Company.objects.filter(id=company_id).last()

        attendance_sheet_id=request.query_params.get('attendance_sheet_id')
        self.attendance_sheet=AttendanceSheet.objects.filter(id=attendance_sheet_id, company=self.company).last()
        
        attendance_list=[]
        serial_number=1

        for employee in [att.id for att in self.attendance_sheet.employee.all()]:

            employee_object = Employee.objects.get(id=employee)

            record = record_qs.filter(company=self.company, employee=employee_object,
                                       date_created__date=datetime.now().date()).last()

            attendance_list.append({
                's/n': serial_number,
                'id': record.id if record else 0,
                'name': f"{employee_object.employee.first_name} {employee_object.employee.last_name}",
                'company': record.company.company_name if record else 0,
                'date': record.date_created.date() if record else 0,
                'location': record.location.name if record else 0,
                'clock_in': record.clock_in_time if record.clock_in_time else "",
                'lateness_duration': record.lateness_duration if record.lateness_duration else 0,
                'break_duration': record.break_duration if record.break_duration else 0,
                'overtime_duration': record.overtime_duration if record.overtime_duration else 0,
                'clock_out': record.clock_out_time if record.clock_out_time else "",
                'work_duration': record.work_duration if record.work_duration else 0
                })

            serial_number += 1

        self.attendance_list = attendance_list

    def get_attendance_sheet_employees(self):
        data={
            'attendance_records': self.attendance_list,
            'count': len(self.attendance_list),
        }
        return data