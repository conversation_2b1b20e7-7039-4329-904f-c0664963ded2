from datetime import time
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from clock_app.tasks import biometric_signin, biometric_signout
from helpers.reusable_functions import Paginator
from leave_management.models import LeaveRequest
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework import status, filters, generics

from core.tasks import send_email, upload_file_aws_s3_bucket
from stock_inventory.models import Branch
from .models import CompanyBranchInvite, Shift, Record, Clock, Break, OverTime, Screenshot, Task, Location, AttendanceSheet #LocationOneOffLink
from payroll_app.models import CompanyEmployeeList as Employee, CompanyPayrollSettings as CompanyProfile
from .serializers import AddEmployeeBranchSerializer, AttendanceRecordSerializer, BranchLocationInviteSerializer, BranchRecordSerializer, BranchSerializer, ClockInInviteSerializer, CompanyBranchLocationSerializer, CompanyProfileSerializer, ClockInSerializer, ClockOutSerializer, CreateOnsiteLocationSerializer, EmployeeClockInSerializer, EmployeeClockOutSerializer, EmployeeEndBreakSerializer, EmployeeStartBreakSerializer,\
    EndBreakSerializer, EndOverTimeSerializer, MapEmployeeBranchSerializer, RecordSerializer, ScreenshotSerializer, \
    StartBreakSerializer, StartOverTimeSerializer, TaskSerializer, LocationSerializer, \
    RemoteLocationSerializer, ShiftSerializer, UpdateLocationSerializer, UpdateShiftSerializer,\
    UpdateTaskSerializer, AttendanceSheetSerializer
from .services import ActivitiesDashboard, AttendanceRecord, EmployeesRecord, TaskRecord,\
    EmployeeAttendanceDashboard, EmployeeAttendanceRecord, EmployeeAssignedTask, EmployeeStatus,\
    AttendanceSheetPopup, AttendanceSheetEmployee
from rest_framework.permissions import IsAuthenticated
from core.auth.custom_auth import CustomUserAuthentication
from payroll_app.permissions import AdminPermission, UserIsActive, EmployeeIsActive
from requisition.models import Company
from .helpers import AllDateFilter, calculate_distance
from django.utils import timezone

class CustomRecordPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 60
    page_query_param = 'page'


class CompanySettings(APIView):
    permission_classes = [AdminPermission, UserIsActive]
    authentication_classes = [CustomUserAuthentication]
    
    def get(self, request):        
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        instance = CompanyProfile.objects.filter(is_deleted=False, company=company).first()        
        if instance is None:
            return Response({"error": "Company doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = CompanyProfileSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()

        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        instance = CompanyProfile.objects.filter(company=company).last()

        serializer = CompanyProfileSerializer(instance, data=request.data, context={"company":company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    

class AttendanceSheetList(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        query_set=AttendanceSheet.objects.filter(is_deleted=False, company=company)
        serializer=AttendanceSheetSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = AttendanceSheetSerializer(data=request.data, context={"company":company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class AttendanceSheetDetail(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):      
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
  
        attendance_sheet_id = request.query_params.get("attendance_sheet_id")
        instance = AttendanceSheet.objects.filter(id=attendance_sheet_id, 
            company=company, is_deleted= False).first()
        if instance is None:
            return Response({"error": "attendance sheet doesn't exist"}, 
                            status=status.HTTP_404_NOT_FOUND)  

        serializer = AttendanceSheetSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK) 

    def put(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, 
                            status=status.HTTP_400_BAD_REQUEST)

        attendance_sheet_id = request.query_params.get("attendance_sheet_id")
        instance = AttendanceSheet.objects.filter(id=attendance_sheet_id, 
            company=company, is_deleted= False).first()
        if instance is None:
            return Response({"error": "attendance sheet doesn't exist"}, 
                            status=status.HTTP_404_NOT_FOUND) 
        
        serializer = AttendanceSheetSerializer(instance, data=request.data, context = {"company":company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        attendance_sheet_id = request.query_params.get("attendance_sheet_id")
        instance = AttendanceSheet.objects.filter(id=attendance_sheet_id, 
            company=company, is_deleted= False).first()
        if instance is None:
            return Response({"error": "attendance sheet doesn't exist"}, 
                            status=status.HTTP_404_NOT_FOUND)        
        serializer = AttendanceSheetSerializer(instance)
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ShiftList(APIView):
    permission_classes = [UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        queryset = Shift.objects.filter(is_deleted=False, company=company)
        serializer = ShiftSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        company_uuid = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_uuid).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        if user is None:
            return Response({"error": "valid employee is required"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ShiftSerializer(data=request.data, context={"company":company,
                                                                 "user":user})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)    


class ShiftDetail(APIView):
    permission_classes = [UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        shift_id = request.query_params.get("shift_id")
        instance = Shift.objects.filter(id=shift_id, company=company).first()
        if instance is None:
            return Response({"error": "shift doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ShiftSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        shift_id = request.query_params.get("shift_id")
        instance = Shift.objects.filter(id=shift_id, company=company).first()
        if instance is None:
            return Response({"error": "shift doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = UpdateShiftSerializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        shift_id = request.query_params.get("shift_id")
        instance = Shift.objects.filter(id=shift_id, company=company).first()
        if instance is None:
            return Response({"error": "shift doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class RecordList(generics.ListAPIView):
    permission_classes = [UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    pagination_class = CustomRecordPagination
    serializer_class = RecordSerializer

    filter_backends = (DjangoFilterBackend, filters.SearchFilter,)

    # filterset_fields = ("id", "team_name", "company", "branch")
    # search_fields = ("team_name",)

    def get_queryset(self):
        company_id = self.request.query_params.get("company_uuid")
        company_ins = Company.objects.filter(id=company_id, is_deleted=False).first()
        queryset = Record.objects.filter(is_deleted=False, company=company_ins)
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        if queryset.exists():
            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "company_name": queryset.first().company.company_name,
                "results": serializer.data
            }
            return Response(response_data, status=status.HTTP_200_OK)

        else:
            response_data = {
                "count": 0,
                "next": None,
                "previous": None,
                "company_name": "",
                "results": []
            }
            return Response(response_data, status=status.HTTP_200_OK)

    # def get(self, request):
    #     company_id = request.query_params.get("company_uuid")
    #     company = Company.objects.filter(id=company_id).first()
    #     if company is None:
    #         return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

    #     query_set=Record.objects.filter(is_deleted=False, company=company)
    #     serializer=RecordSerializer(query_set, many=True)
    #     return Response(serializer.data, status=status.HTTP_200_OK)


class RecordDetail(APIView):
    permission_classes = [UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        record_id = request.query_params.get("record_id")
        instance = Record.objects.filter(id=record_id, company=company).first()
        if instance is None:
            return Response({"error": "record doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = RecordSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ClockInList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        query_set=Clock.objects.filter(is_deleted=False, company=company)
        serializer=ClockInSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    # def post(self, request):
    #     company_id = request.query_params.get("company_uuid")
    #     user = request.user
    #     company = Company.objects.filter(id=company_id).first()
    #     if company is None:
    #         return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
    #     else:
    #         employee = Employee.objects.filter(employee = user, company__id=company_id).last()
    #         if employee is None:
    #             return Response({"error": "you must be an employee of the company"}, 
    #                             status=status.HTTP_400_BAD_REQUEST)
    #         else:
    #             location_status = False
    #             locations = Location.objects.filter(company=company)
    #             for location in locations:
    #                 print("####################################\n")
    #                 print("################# ", employee.employee_email," ###################\n")
    #                 print("############ LOCATION 1 ############\n\n")
    #                 print("### lon 1 " ,location.longitude, "lat 1", location.latitude,"###\n\n")
    #                 print("############ LOCATION 2 ############\n\n")
    #                 print("### lon 2 " ,float(request.data["longitude"]), "lat 2", float(request.data["latitude"]),"###\n\n")
    #                 print("#####   INSIDE CLOCK IN VIEW  #####\n\n")
    #                 print("#####################################\n\n")
    #                 if calculate_distance(location.latitude, location.longitude, float(request.data["latitude"]), float(request.data["longitude"])) <= 100:
    #                     location_status = True
    #                     location = location
    #                     serializer=ClockInSerializer(data=request.data, context={'employee_id': employee.id, 
    #                                                                             'location': location.id,
    #                                                                             'company': company})
    #                     serializer.is_valid(raise_exception=True)
    #                     serializer.save()
    #                     break
    #             print("#####################################\n")
    #             print("##### LOCATION STATUS =>>",location_status, " #####\n\n")
    #             print("#####################################\n\n")
    #             if location_status:
    #                 return Response(serializer.data, status=status.HTTP_201_CREATED)
    #             else:
    #                 return Response({"error":"you can only clock in from the office or remote work place"},
    #                                         status=status.HTTP_400_BAD_REQUEST)      
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        user = request.user
        company = Company.objects.filter(id=company_id).first()
        employee = Employee.objects.filter(employee=user, company__id=company_id, is_active=True).first()
        if not employee:
            return Response({"error": "employee is not active"}, status=status.HTTP_400_BAD_REQUEST)
       
        serializer = EmployeeClockInSerializer(data=request.data, context={"company":company,
                                                                            "employee":employee})
        serializer.is_valid(raise_exception=True)
        return Response(
            {
                "message":"clock-in successful",
                "data":serializer.validated_data
            }, status=status.HTTP_201_CREATED)

class ClockInDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        clock_id = request.query_params.get("clock_id")
        instance = Clock.objects.filter(id=clock_id, company=company).first()
        if instance is None:
            return Response({"error": "clock-in doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ClockInSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)   

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        clock_id = request.query_params.get("clock_id")
        instance = Clock.objects.filter(id=clock_id, company=company).first()
        if instance is None:
            return Response({"error": "clock-in doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ClockOutList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
            
        query_set=Clock.objects.filter(is_deleted=False, company=company)
        serializer=ClockOutSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    # def post(self, request):
    #     company_id = request.query_params.get("company_uuid")
    #     user = request.user
    #     company = Company.objects.filter(id=company_id).first()
    #     if company is None:
    #         return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
    #     else:
    #         employee = Employee.objects.filter(employee = user, company=company).last()
    #         if employee is None:
    #             return Response({"error": "you must be an employee of the company"}, 
    #                             status=status.HTTP_400_BAD_REQUEST)
    #         else:
    #             locations = Location.objects.filter(company=company)
    #             location_status = False
    #             for location in locations:
    #                 print("####################################\n")
    #                 print("############ LOCATION 1 ############\n\n")
    #                 print("### lon 1 " ,location.longitude, "lat 1", location.latitude,"###\n\n")
    #                 print("############ LOCATION 2 ############\n\n")
    #                 print("### lon 2 " ,float(request.data["longitude"]), "lat 2", float(request.data["latitude"]),"###\n\n")
    #                 print("#####   INSIDE CLOCK OUT VIEW  #####\n\n")
    #                 print("#####################################\n\n")
    #                 if calculate_distance(location.latitude, location.longitude, float(request.data["latitude"]), float(request.data["longitude"])) <= 100:
    #                     location_status = True
                        
    #                     location = location
    #                     serializer=ClockOutSerializer(data=request.data, context={'employee_id': employee.id, 
    #                                                                             'location': location,
    #                                                                             'company': company})
    #                     serializer.is_valid(raise_exception=True)
    #                     serializer.save()
    #                     break
    #             print("#####################################\n")
    #             print("##### LOCATION STATUS =>>",location_status," #####\n\n")
    #             print("#####################################\n\n")
    #             if location_status:
    #                 return Response(serializer.data, status=status.HTTP_201_CREATED)
    #             else:
    #                 return Response({"error":"you can only clock out from the office or remote work place"},
    #                                     status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        user = request.user
        company = Company.objects.filter(id=company_id).first()
       
        employee = Employee.objects.filter(employee=user, company__id=company_id, is_active=True).first()
        if not employee:
            return Response({"error": "employee is not active"}, status=status.HTTP_400_BAD_REQUEST)
       
        serializer = EmployeeClockOutSerializer(data=request.data, context={"company":company,
                                                                            "employee":employee})
        serializer.is_valid(raise_exception=True)
        return Response(
            {
                "message":"clock-out successful",
                "data":serializer.validated_data
            }, status=status.HTTP_200_OK)


class ClockOutDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        clock_id = request.query_params.get("clock_id")
        instance = Clock.objects.filter(id=clock_id, company=company).first()
        if instance is None:
            return Response({"error": "clock out doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ClockOutSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        clock_id = request.query_params.get("clock_id")
        instance = Clock.objects.filter(id=clock_id, company=company).first()
        if instance is None:
            return Response({"error": "clock out doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)    


class StartBreakList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        query_set=Break.objects.filter(is_deleted=False, company=company)
        serializer=StartBreakSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)  

    # def post(self, request):
    #     company_id = request.query_params.get("company_uuid")
    #     user = request.user
    #     company = Company.objects.filter(id=company_id).first()
    #     if company is None:
    #         return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
    #     else:
    #         employee = Employee.objects.filter(employee = user, company__id = company_id).last()
    #         if employee is None:
    #             return Response({"error": "you must be an employee of the company"}, 
    #                             status=status.HTTP_400_BAD_REQUEST)
    #         else:
                
    #             locations = Location.objects.filter(company=company)
    #             for location in locations:
    #                 if calculate_distance(location.latitude, location.longitude, float(request.data["latitude"]), float(request.data["longitude"])) <= 100:
    #                     location = location
    #                     serializer=StartBreakSerializer(data=request.data, context={'employee_id': employee.id, 
    #                                                                             'location': location,
    #                                                                             'company': company})
    #                     serializer.is_valid(raise_exception=True)
    #                     serializer.save()
    #                     return Response(serializer.data, status=status.HTTP_201_CREATED)
    #             return Response({"error":"you can only take breaks from the office or remote work place"},
    #                                     status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        user = request.user
        company = Company.objects.filter(id=company_id).first()
        
        employee = Employee.objects.filter(employee=user, company__id=company_id, is_active=True).first()
        if not employee:
            return Response({"error": "employee is not active"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer=EmployeeStartBreakSerializer(data=request.data, context={'employee': employee,
                                                                        'company': company})
        serializer.is_valid(raise_exception=True)
        return Response(
            {
                "message":"break taken successfully",
                "data":serializer.validated_data
            }, status=status.HTTP_201_CREATED)


class StartBreakDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        break_id = request.query_params.get("break_id")
        instance = Break.objects.filter(id=break_id, company=company).first()
        if instance is None:
            return Response({"error": "break doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = StartBreakSerializer(instance, many=False)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        break_id = request.query_params.get("break_id")
        instance = Break.objects.filter(id=break_id, company=company).first()
        if instance is None:
            return Response({"error": "break doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class EndBreakList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        query_set=Break.objects.filter(is_deleted=False, company=company)
        serializer=EndBreakSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    # def post(self, request):
    #     company_id = request.query_params.get("company_uuid")
    #     user = request.user
    #     company = Company.objects.filter(id=company_id).first()
    #     if company is None:
    #         return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
    #     else:
    #         employee = Employee.objects.filter(employee = user, company__id = company_id).last()
    #         if employee is None:
    #             return Response({"error": "you must be an employee of the company"}, 
    #                             status=status.HTTP_400_BAD_REQUEST)
    #         else:
    #             locations = Location.objects.filter(company=company)
    #             for location in locations:
    #                 if calculate_distance(location.latitude, location.longitude, float(request.data["latitude"]), float(request.data["longitude"])) <= 100:
    #                     location = location
    #                     serializer=EndBreakSerializer(data=request.data, context={'employee_id': employee.id, 
    #                                                                             'location': location,
    #                                                                             'company': company})
    #                     serializer.is_valid(raise_exception=True)
    #                     serializer.save()
    #                     return Response(serializer.data, status=status.HTTP_201_CREATED)
    #             return Response({"error":"you can only end breaks from the office or remote work place"},
    #                                     status=status.HTTP_400_BAD_REQUEST)
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        user = request.user
        company = Company.objects.filter(id=company_id).first()

        employee = Employee.objects.filter(employee=user, company__id=company_id, is_active=True).first()
        if not employee:
            return Response({"error": "employee is not active"}, status=status.HTTP_400_BAD_REQUEST)

        serializer=EmployeeEndBreakSerializer(data=request.data, context={'employee': employee,
                                                                        'company': company})
        serializer.is_valid(raise_exception=True)
        return Response(
            {
                "message":"break ended successfully",
                "data":serializer.validated_data
            }, status=status.HTTP_200_OK)

class EndBreakDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        break_id = request.query_params.get("break_id")
        instance = Break.objects.filter(id=break_id, company=company).first()
        if instance is None:
            return Response({"error": "break doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = EndBreakSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        break_id = request.query_params.get("break_id")
        instance = Break.objects.filter(id=break_id, company=company).first()
        if instance is None:
            return Response({"error": "break doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class StartOverTimeList(APIView):
    permission_classes = [AdminPermission, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        query_set=OverTime.objects.filter(is_deleted=False, company=company)
        serializer=StartOverTimeSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        user = request.user
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, 
                            status=status.HTTP_400_BAD_REQUEST)
        else:
            employee = Employee.objects.filter(employee = user, company__id=company_id).last()
            if employee is None:
                return Response({"error": "you must be an employee of the company"}, 
                                status=status.HTTP_400_BAD_REQUEST)
            else:
                locations = Location.objects.filter(company=company)
                for location in locations:
                    if calculate_distance(location.latitude, location.longitude, float(request.data["latitude"]), float(request.data["longitude"])) <= 100:
                        location = location
                        serializer=StartOverTimeSerializer(data=request.data, context={'employee_id': employee.id, 
                                                                                'location': location,
                                                                                'company': company})
                        serializer.is_valid(raise_exception=True)
                        serializer.save()
                        return Response(serializer.data, status=status.HTTP_201_CREATED)
                return Response({"error":"you can only start overtime from the office or remote work place"}, 
                         status=status.HTTP_400_BAD_REQUEST)


class StartOverTimeDetail(APIView):
    permission_classes = [AdminPermission, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        overtime_id = request.query_params.get("overtime_id")
        instance = OverTime.objects.filter(pk=overtime_id, company=company).first()
        if instance is None:
            return Response({"error": "overtime doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = StartOverTimeSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        overtime_id = request.query_params.get("overtime_id")
        instance = OverTime.objects.filter(id=overtime_id, company=company).first()
        if instance is None:
            return Response({"error": "overtime doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class EndOverTimeList(APIView):
    permission_classes = [AdminPermission, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        query_set=OverTime.objects.filter(is_deleted=False, company=company)
        serializer=EndOverTimeSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        user = request.user
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            employee = Employee.objects.filter(employee = user, company__id=company_id).last()
            if employee is None:
                return Response({"error": "you must be an employee of the company"}, 
                                status=status.HTTP_400_BAD_REQUEST)
            else:
                locations = Location.objects.filter(company=company)
                for location in locations:
                    if calculate_distance(location.latitude, location.longitude, float(request.data["latitude"]), float(request.data["longitude"])) <= 100:
                        location = location
                        serializer=EndOverTimeSerializer(data=request.data, context={'employee_id': employee.id, 
                                                                                'location': location,
                                                                                'company': company})
                        serializer.is_valid(raise_exception=True)
                        serializer.save()
                        return Response(serializer.data, status=status.HTTP_201_CREATED)
                return Response({"error":"you can only end overtime from the office or remote work place"},
                                        status=status.HTTP_400_BAD_REQUEST)


class EndOverTimeDetail(APIView):
    permission_classes = [AdminPermission, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        overtime_id = request.query_params.get("overtime_id")
        instance = OverTime.objects.filter(id=overtime_id, company=company).first()
        if instance is None:
            return Response({"error": "overtime doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = EndOverTimeSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        overtime_id = request.query_params.get("overtime_id")
        instance = OverTime.objects.filter(id=overtime_id, company=company).first()
        if instance is None:
            return Response({"error": "overtime doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT) 


class TaskList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        query_set= Task.objects.filter(is_deleted=False, company=company)
        serializer= TaskSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        user = request.user
        employee = Employee.objects.filter(employee = user, company=company).last()
        if employee is None:
            return Response({"error": "employee doesn't exist"}, status=status.HTTP_404_NOT_FOUND)

        serializer=TaskSerializer(data=request.data, context={'employee': employee,
                                                              'company': company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class TaskDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        task_id = request.query_params.get("task_id")
        instance = Task.objects.filter(id=task_id, company=company).first()
        if instance is None:
            return Response({"error": "task doesn't exist"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = TaskSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)
        
    def put(self, request):
        company_id = request.query_params.get("company_uuid")   
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)   
         
        user = request.user
        employee = Employee.objects.filter(employee=user, company=company).last()
        if employee is None:
            return Response({"error": "employee doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
          
        task_id = request.query_params.get("task_id")
        instance = Task.objects.filter(id=task_id, company=company).first()
        if instance is None:
            return Response({"error": "task doesn't exist"}, status=status.HTTP_404_NOT_FOUND)

        serializer=UpdateTaskSerializer(instance, data=request.data, partial=True,
                                        context={'employee' : employee.id,
                                                'company' : company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        task_id = request.query_params.get("task_id")
        instance = Task.objects.filter(id=task_id, company=company).first()
        if instance is None:
            return Response({"error": "task doesn't exist"}, status=status.HTTP_404_NOT_FOUND)

        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)    


class ScreenshotList(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        queryset = Screenshot.objects.filter(is_deleted=False, company=company)
        serializer = ScreenshotSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        employee = Employee.objects.filter(employee = user, company=company).last()
        if employee is None:
            return Response({"error": "employee doesn't exist"}, status=status.HTTP_404_NOT_FOUND)

        serializer = ScreenshotSerializer(data=request.data, context={'employee_id': employee.id, 'company':company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)    


class ScreenshotDetail(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        screenshot_id = request.query_params.get("screenshot_id")
        instance = Screenshot.objects.filter(pk=screenshot_id, company=company).first()
        if instance is None:
            return Response({"error": "screenshot doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ScreenshotSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        screenshot_id = request.query_params.get("screenshot_id")
        instance = Screenshot.objects.filter(pk=screenshot_id, company=company).first()
        if instance is None:
            return Response({"error": "screenshot doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class LocationList(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        query_set=Location.objects.filter(is_deleted=False, company=company)
        serializer=LocationSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        request_user = request.user
        company = Company.objects.filter(id=company_id).first()
        if not company:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        employee = Employee.objects.filter(employee=request_user, company=company, is_active=True).first()
        if not employee and company.user != request_user:
            return Response({"error": "You cannot perform this operation"}, status=status.HTTP_400_BAD_REQUEST)    
                
        serializer=CreateOnsiteLocationSerializer(data=request.data, context={'employee': employee,
                                                                    'company': company})
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_201_CREATED)       


class LocationDetail(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication] 

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        location_id = request.query_params.get("location_id")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        instance = Location.objects.filter(id=location_id, company = company).first()
        if instance is None:
            return Response({"error": "location doesn't exist"}, status=status.HTTP_404_NOT_FOUND)

        serializer = LocationSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def patch(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        employee = Employee.objects.filter(company__id=company_id, employee=user).last()
        if not employee:
            return Response({"error": "employee doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        location_id = request.query_params.get("location_id")
        instance = Location.objects.filter(id=location_id, company__id=company_id).first()
        if instance is None:
            return Response({"error": "location doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        branch_id = request.query_params.get("branch_id")
        if branch_id:
            branch_ins = Branch.objects.filter(id=branch_id, is_active=True).first()
            if not branch_ins:
                return Response({"message": "branch does not exist"}, status=status.HTTP_404_NOT_FOUND)
        else:
            branch_ins = None

        
        company = instance.company
        serializer=UpdateLocationSerializer(instance, data=request.data, partial=True, context={'employee': employee, 'company':company, "branch":branch_ins})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED) 

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        location_id = request.query_params.get("location_id")
        instance = Location.objects.filter(id=location_id,company=company).first()
        if instance is None:
            return Response({"error": "location doesn't exist"}, status=status.HTTP_404_NOT_FOUND)

        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class RemoteLocationList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        request_user = request.user
        employee = Employee.objects.filter(employee = request_user, company=company).last()
        if employee is None:
            return Response({"error": "employee not found"}, status=status.HTTP_404_NOT_FOUND)
        
        query_set=Location.objects.filter(is_deleted=False, company=company, location_type="REMOTE", employee=employee)
        serializer=RemoteLocationSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.get(id=company_id)
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        request_user = request.user
        employee = Employee.objects.filter(employee = request_user, company=company).last()
        if employee is None:
            return Response({"error": "employee not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer=RemoteLocationSerializer(data=request.data, 
                                            context={'company': company,
                                                    'employee': employee})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class RemoteLocationDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication] 

    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        request_user = request.user
        employee = Employee.objects.filter(employee = request_user, company=company).last()
        if employee is None:
            return Response({"error": "employee not found"}, status=status.HTTP_404_NOT_FOUND)
        
        location_id = request.query_params.get("location_id")
        instance = Location.objects.filter(id=location_id, company__id=company_id, location_type="REMOTE",
                                           employee=employee, is_deleted=False).first()
        if instance is None:
            return Response({"error": "location doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = RemoteLocationSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)    

    def put(self, request):
        company_id = request.query_params.get("company_uuid")
        location_id = request.query_params.get("location_id")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        user = request.user
        employee = Employee.objects.filter(employee=user, company__id=company_id).last()
        if not employee:
            return Response({"error": "employee doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        location_instance = Location.objects.filter(id=location_id, company__id=company_id).first()
        if location_instance is None:
            return Response({"error": "location doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        company = location_instance.company
        serializer=UpdateLocationSerializer(location_instance, data=request.data, context={'employee': employee,
                                                                                            'company':company})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)    

    def delete(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        location_id = request.query_params.get("location_id")
        instance = Location.objects.filter(pk=location_id, company=company).first()
        if instance is None:
            return Response({"error": "location doesn't exist"}, status=status.HTTP_404_NOT_FOUND)
        
        instance.is_deleted=True
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ActivitiesDashboardMetrics(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        
        # try:
        #     response = ActivitiesDashboard(request=request).get_activities_dashboard_metrics()
        #     return Response({"data": response}, status=status.HTTP_200_OK)
        # except Exception as e:
        #     return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        company_id = request.query_params.get("company_uuid")
        today = timezone.localtime()
        employee_qs = Employee.objects.filter(company__id=company_id, is_deleted=False)
        record_qs = Record.objects.filter(company__id=company_id, date_created__date=today.date(), is_deleted=False)
        leave_request_qs = LeaveRequest.objects.filter(company__id=company_id, is_deleted=False)
        payroll_settings = CompanyProfile.objects.filter(company__id=company_id).first()
        active_leave = leave_request_qs.filter(status = "ACTIVE").count() or 0
        over_time_qs = record_qs.filter(clock_out_time__isnull=True)
        if payroll_settings and payroll_settings.closing_time:
            print(today.time(), payroll_settings.closing_time)
            if today.time() > payroll_settings.closing_time:
                overtime_count = over_time_qs.count() or 0
            else:
                overtime_count = 0
        else:
            if today.time() > time(17,30):
                overtime_count = over_time_qs.count() or 0
            else:
                overtime_count = 0
        employee_count = employee_qs.count() or 0
        active_employee_count = employee_qs.filter(is_active=True).count() or 0
        present_employee_count = record_qs.count() or 0
        late_employee = record_qs.filter(is_late=True).count() or 0
        response = {
            "total": employee_count,
            "active_employees": active_employee_count,
            "absent": (employee_count - present_employee_count),
            "late": late_employee,
            "overtime": overtime_count,
            "on_leave": active_leave
        }
        return Response(response, status=status.HTTP_200_OK)


class ActivitiesDashboardStats(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        # try:
        #     response = ActivitiesDashboard(request=request).get_weekly_record_stats()
        #     return Response({"data": response}, status=status.HTTP_200_OK)
        # except Exception as e:
        #     return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        company_id = request.query_params.get("company_uuid")
        
        today = timezone.localtime()
        employee_qs = Employee.objects.filter(company__id=company_id, is_deleted=False)
        record_qs = Record.objects.filter(company__id=company_id, date_created__date=today.date(), is_deleted=False)
        payroll_settings = CompanyProfile.objects.filter(company__id=company_id).first()
        over_time_qs = record_qs.filter(clock_out_time__isnull=True)
        if payroll_settings and payroll_settings.closing_time:
            if today.time() > payroll_settings.closing_time:
                overtime_count = over_time_qs.count() or 0
            else:
                overtime_count = 0
        else:
            if today.time() > time(17,30):
                overtime_count = over_time_qs.count() or 0
            else:
                overtime_count = 0
        employee_count = employee_qs.count() or 0
        present_employee_count = record_qs.count() or 0
        late_employee = record_qs.filter(is_late=True).count() or 0
        response = {
            "overtime_rate": overtime_count,
            "absence_rate": (employee_count - present_employee_count),
            "lateness_rate": late_employee
        }
        return Response(response, status=status.HTTP_200_OK)


class ActivitiesDashboardTrends(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        # try:
        #     response = ActivitiesDashboard(request=request).get_work_trends()
        #     return Response({"data": response}, status=status.HTTP_200_OK)
        # except Exception as e:
        #     return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        company_id = request.query_params.get("company_uuid")
        
        # AllDateFilter.get_date_filter(request=request)
        
        today = timezone.localtime()
        employee_qs = Employee.objects.filter(company__id=company_id, is_deleted=False)
        record_qs = Record.objects.filter(company__id=company_id, date_created__date=today.date(), is_deleted=False)
        leave_request_qs = LeaveRequest.objects.filter(company__id=company_id, is_deleted=False)
        active_leave = leave_request_qs.filter(status = "ACTIVE").count() or 0

        payroll_settings = CompanyProfile.objects.filter(company__id=company_id).first()
        over_time_qs = record_qs.filter(clock_out_time__isnull=True)
        if payroll_settings and payroll_settings.closing_time:
            if today.time() > payroll_settings.closing_time:
                overtime_count = over_time_qs.count() or 0
            else:
                overtime_count = 0
        else:
            if today.time() > time(17,30):
                overtime_count = over_time_qs.count() or 0
            else:
                overtime_count = 0
        employee_count = employee_qs.count() or 0
        present_employee_count = record_qs.count() or 0
        late_employee = record_qs.filter(is_late=True).count() or 0
        early_employee = record_qs.filter(is_late=False).count() or 0
        
        present = present_employee_count
        absent = employee_count - present_employee_count
        work_trends = [present, absent, early_employee, late_employee, overtime_count, active_leave]
        response = {
            "keys": ['present', 'absent', 'on_time', 'late', 'overtime', 'on_leave'],
            "values": work_trends
        }
        return Response(response, status=status.HTTP_200_OK)



class ActivitiesDashboardAttendance(generics.ListAPIView):
    permission_classes = [UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = AttendanceRecordSerializer
    pagination_class = CustomRecordPagination      

    def get_queryset(self):
        # queryset = ActivitiesDashboard(request=self.request).get_attendance()
        # return queryset.get("attendance", [])
        today_date = timezone.localtime().date()
        company_id = self.request.query_params.get("company_uuid")
        queryset = Record.objects.filter(company__id=company_id, date_created__date=today_date, is_deleted=False)
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        if queryset:
            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "results": serializer.data
            }
            return Response(response_data, status=status.HTTP_200_OK)

        else:
            response_data = {
                "count": 0,
                "next": None,
                "previous": None,
                "results": []
            }
            return Response(response_data, status=status.HTTP_200_OK)


class EmployeesRecordHistory(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeesRecord(request=request).get_work_history()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeesRecordList(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeesRecord(request=request).get_employees_list()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AttendanceRecordMetrics(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = AttendanceRecord(request=request).get_attendance_record_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class AttendanceRecordsList(generics.ListAPIView):
    permission_classes = [UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    serializer_class = AttendanceRecordSerializer
    pagination_class = CustomRecordPagination

    def get_queryset(self):
        queryset = AttendanceRecord(request=self.request).get_attendance_list()
        return queryset.get("attendance_records", [])

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        if queryset:
            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "company_name": queryset[0].get("company"),
                "results": serializer.data
            }
            return Response(response_data, status=status.HTTP_200_OK)

        else:
            response_data = {
                "count": 0,
                "next": None,
                "previous": None,
                "company_name": "",
                "results": []
            }
            return Response(response_data, status=status.HTTP_200_OK)


class TaskRecordMetrics(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = TaskRecord(request=request).get_task_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TaskRecordList(APIView):
    permission_classes = [IsAuthenticated, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = TaskRecord(request=request).get_task_list()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAttendanceDashboardMetrics(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAttendanceDashboard(request=request).get_employee_attendance_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAttendanceDashboardTasks(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAttendanceDashboard(request=request).get_task_list()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAssignedTaskMetrics(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAssignedTask(request=request).get_employee_assigned_task_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAssignedTaskList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAssignedTask(request=request).get_task_list()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAttendanceRecordMetrics(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAttendanceRecord(request=request).get_employee_attendance_metrics()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAttendanceRecordList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAttendanceRecord(request=request).get_record_list()
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeStatusList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeStatus(request=request).get_employee_status()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AttendanceRecordUserDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = AttendanceRecord(request=request).get_user_details()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TaskRecordDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = TaskRecord(request=request).get_ongoing_details()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAttendanceDashboardDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAttendanceDashboard(request=request).get_ongoing_details()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class EmployeeAssignedTaskDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = EmployeeAssignedTask(request=request).get_ongoing_details()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AttendanceSheetPopupDetail(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = AttendanceSheetPopup(request=request).get_attendance_sheets()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AttendanceSheetEmployeeList(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        try:
            response = AttendanceSheetEmployee(request=request).get_attendance_sheet_employees()
            return Response({'data': response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
class InviteCompanyBranchAPIView(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        company = Company.objects.filter(id=company_id).first()
        if company is None:
            return Response({"error": "valid company is required"}, status=status.HTTP_400_BAD_REQUEST)

        employee_id = request.query_params.get("employee_id")
        employee_ins = Employee.objects.filter(id=employee_id, company__id=company_id, is_deleted=False).last()
        if not employee_ins:
            return Response({"message": "employee not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = BranchLocationInviteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        location_name = serializer.validated_data.get("name")
        branch = serializer.validated_data.get("branch")
        if employee_ins:
            update_invite = CompanyBranchInvite.objects.filter(company_id=company_id, employee=employee_ins, is_completed=False).update(is_expired=True, invite_status="EXPIRED")
        CompanyBranchInvite.create_branch_location_invite(location_name, employee_ins.company, employee_ins, branch)
        return Response({"message": "branch location invite sent"}, status=status.HTTP_200_OK)
    
class OnboardingCompanyBranchAPIView(APIView):
    def post(self, request):
        invite_id = request.query_params.get("invite_id")
        invite_ins = CompanyBranchInvite.objects.filter(invite_id=invite_id).last()
        if not invite_ins:
            return Response({"message": "invalid invite link"}, status=status.HTTP_400_BAD_REQUEST)
        if invite_ins.invite_status != "PENDING":
            if invite_ins.invite_status == "COMPLETED":
                return Response({"message": "this link has already been used!"}, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"message": "this link has expired, contact HR!"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            serializer = CompanyBranchLocationSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            latitude = serializer.validated_data.get("latitude")
            longitude = serializer.validated_data.get("longitude")
            image = serializer.validated_data.get("image")

            if invite_ins.branch:
                branch = Location.objects.filter(company=invite_ins.company, location_type="ONSITE", branch=invite_ins.branch, is_deleted=False).first()
                if branch:
                    return Response({"message": "branch already exists"}, status=status.HTTP_400_BAD_REQUEST)
                
            Location.create_company_branch(
                employee=invite_ins.employee,
                company=invite_ins.company,
                location_type="ONSITE",
                name=invite_ins.name,
                latitude=latitude,
                longitude=longitude,
                branch=invite_ins.branch,
            )
            invite_ins.is_completed = True
            invite_ins.invite_status = "COMPLETED"
            invite_ins.longitude = longitude
            invite_ins.latitude = latitude

            invite_ins.save()
            
            uploaded_file = upload_file_aws_s3_bucket(
                    model_instance_id=invite_ins.invite_id, file=image, model_name="CompanyBranchInvite"
                    )
            send_email.delay(recipient=invite_ins.employee.employee_email, subject="Company Branch Created",
                template_dir="completed_branch_invite.html", company_name=invite_ins.company.company_name,
                branch_name=invite_ins.name)
            response = {
                "status": "Success",
                "message": "branch location created successfully",
                "file_url": uploaded_file if uploaded_file else ""
            }
            return Response(response, status=status.HTTP_200_OK)


class ClockInInvite(APIView):    
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_id =  request.query_params.get("company_uuid")

        user = request.user
        employee = Employee.objects.filter(company__id=company_id, employee=user, 
                                           is_active=True).first()
        if not employee:
            return Response({"message":"employee not found"}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = ClockInInviteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        get_clock_type=serializer.validated_data.get("clock_type")

        if get_clock_type == "CLOCK_IN":
            message = f"Hi, you can clock in to {employee.company.company_name} using the button below"
            button_name = "Clock In"
        elif get_clock_type == "START_BREAK":
            message = f"Hi, you can start break at {employee.company.company_name} using the button below"
            button_name = "Start Break"
        elif get_clock_type == "END_BREAK":
            message = f"Hi, you can end break at {employee.company.company_name} using the button below"
            button_name = "End Break"
        elif get_clock_type == "CLOCK_OUT":
            message = f"Hi, you can clock out from {employee.company.company_name} using the button below"
            button_name = "Clock Out"

        company_ins = employee.company
        employee_email = employee.employee_email

        this_company_name = company_ins.company_name
        company_name = this_company_name.replace(" ","%20") 
        send_email.delay(recipient=employee_email, subject="Clock-In Invite",
                         message = message,
                         button_name = button_name,
                         template_dir="clock_in_invite.html",
                         company_name=company_name,
                         call_back_url=f"https://www.home.paybox360.com/login?callbackUrl=payroll/employee-time-attendance/{company_id}/{company_name}/attendance-dashboard")

        return Response(serializer.data, status=status.HTTP_200_OK)
    
class UpdateEmployeeWorkType(APIView):    
    permission_classes = [EmployeeIsActive, UserIsActive]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_id =  request.query_params.get("company_uuid")
        
        all_employee = Employee.objects.filter(company__id=company_id, is_deleted=False)
        if request.data["work_type"] == "HYBRID":
            if request.data["for_who"] == "ALL":
               all_employee.update(work_type="HYBRID")
            elif request.data["for_who"] == "SINGLE":
                all_employee.filter(employee_email=request.data["users"]).update(work_type="HYBRID")
            else:
                for employee in request.data["users"]:
                    all_employee.filter(employee_email=employee).update(work_type="HYBRID")
        elif request.data["work_type"] == "REMOTE":
            if request.data["for_who"] == "ALL":
               all_employee.update(work_type="REMOTE")
            elif request.data["for_who"] == "SINGLE":
                all_employee.filter(employee_email=request.data["users"]).update(work_type="REMOTE")
            else:
                for employee in request.data["users"]:
                    all_employee.filter(employee_email=employee).update(work_type="REMOTE")
        elif request.data["work_type"] == "ONSITE":
            if request.data["for_who"] == "ALL":
               all_employee.update(work_type="ONSITE")
            elif request.data["for_who"] == "SINGLE":
                all_employee.filter(employee_email=request.data["users"]).update(work_type="ONSITE")
            else:
                for employee in request.data["users"]:
                    all_employee.filter(employee_email=employee).update(work_type="ONSITE")
        
        return Response({"message":"successful"}, status=status.HTTP_200_OK)

class AddEmployeeBranchAPIView(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive]
    authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        company_id = request.query_params.get("company_uuid")
        serializer = AddEmployeeBranchSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        all_employee_email = serializer.validated_data.get("employees")
        branch_id = serializer.validated_data.get("branch_id")
        successful = []
        unsuccessful = []
        for employee_email in all_employee_email:
            employee_ins = Employee.objects.filter(company__id=company_id, employee_email=employee_email, is_active=True).first()
            if employee_ins:
                get_location_ins = Location.objects.filter(id=branch_id).first()
                if get_location_ins:
                    employee_ins.employee_branch = get_location_ins
                    employee_ins.save()
                    successful.append(employee_email)
                else:
                    unsuccessful.append(employee_email)
            else:
                unsuccessful.append(employee_email)
        data = {
            "message": "successful",
            "all_employee_email": serializer.data,
            "successful": successful,
            "unsuccessful": unsuccessful

        }
        return Response(data, status=status.HTTP_200_OK)
    
class MapEmployeeBranchAPIView(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    def put(self, request):
        company_id = request.query_params.get("company_uuid")
        serializer = MapEmployeeBranchSerializer(data=request.data, context={"company_id": company_id})
        serializer.is_valid(raise_exception=True)
        branch_name = serializer.validated_data.get("branch_name")
        return Response({"message": f"employee added to {branch_name} successfully"}, status=status.HTTP_200_OK)
    
class GetAllBranchAPIView(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        all_branch = Location.objects.filter(company__id=company_id, location_type="ONSITE", is_deleted=False)
        serializer = BranchSerializer(all_branch, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

class GetAllBranchEmployeeAPIView(APIView):
    permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    authentication_classes = [CustomUserAuthentication]
    def get(self, request):
        company_id = request.query_params.get("company_uuid")
        branch_id = request.query_params.get("branch_id")
        all_branch_employees = Employee.objects.filter(company__id=company_id, employee_branch__id=branch_id, is_deleted=False)
        serializer = BranchRecordSerializer(all_branch_employees, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class BiometricSignInAPIView(APIView):
    # permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    # authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        data = dict(request.data)
        biometric_data = data.get("employees_data")
        biometric_signin(biometric_data)
        return Response({"message": "success"}, status=status.HTTP_200_OK)

class BiometricSignOutAPIView(APIView):
    # permission_classes = [EmployeeIsActive, UserIsActive, EmployeeIsActive, AdminPermission]
    # authentication_classes = [CustomUserAuthentication]
    def post(self, request):
        data = dict(request.data)
        biometric_data = data.get("employees_data")
        biometric_signout(biometric_data)
        return Response({"message": "success"}, status=status.HTTP_200_OK)