# from django.conf import settings
from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from .models import AttendanceSheet, CompanyBranchInvite, Record
# from requisition.models import Company
from core.tasks import send_email

# @receiver(post_save, sender=Company)
# def create_company_profile_for_new_company(sender, **kwargs):
#     company_profile = CompanyProfile.objects.get_or_create(company=kwargs['instance'])

@receiver(post_save, sender=CompanyBranchInvite)
def employee_onboarding_form_email(sender, instance, created, **kwargs):
    if created:
        send_email.delay(recipient=instance.employee.employee_email, subject="Company Branch Invite",
                        template_dir="company_branch_invite.html", company_name=instance.company.company_name,
                        branch_name=instance.name,
                        form_invite_link=f"https://www.home.paybox360.com/company-branch-invite-link/{instance.invite_id}")


@receiver(pre_save, sender=AttendanceSheet)
def copy_old_title(sender, instance, **kwargs):
    # Check if the instance is being updated
    if instance.pk:
        # Retrieve the old title before the instance is updated
        old_instance = AttendanceSheet.objects.get(pk=instance.pk)
        instance._old_title = old_instance.title.upper()  # Store old title in instance

@receiver([post_save, post_delete], sender=AttendanceSheet)
def update_records_on_attendance_sheet_change(sender, instance, created, **kwargs):
    # Update related Record instances when AttendanceSheet instances change
    # Logic here to update Record instances based on AttendanceSheet changes
    # For example:
    if not created:
        # Get the old title from the instance attribute
        old_title = getattr(instance, '_old_title', None)
        updated_title = instance.title.upper() # Updated title of the AttendanceSheet

        if old_title:
            # Update related Record instances based on the old title
            affected_records = Record.objects.filter(company=instance.company, attendance=old_title)
            affected_records.update(attendance=updated_title)

    