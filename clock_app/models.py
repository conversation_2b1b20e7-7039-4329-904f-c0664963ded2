from django.db import models
from core.tasks import send_email
from requisition.models import Company
from payroll_app.models import CompanyEmployeeList as Employee
from core.models import BaseModel
import uuid

LOCATION_TYPE = [
        ("REMOTE", "remote"),
        ("ONSITE", "onsite")
    ]

class AttendanceSheet(BaseModel):
    ATTENDANCE_STATUS = [
        ("INACTIVE", 'INACTIVE'),
        ("ACTIVE", 'ACTIVE'),
    ]
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True, related_name='+')
    title = models.CharField(max_length=255, null=True, blank=True)
    description = models.CharField(max_length=255, null=True, blank=True)
    employee = models.ManyToManyField(Employee, related_name='attendance')
    status = models.CharField(max_length=255, choices=ATTENDANCE_STATUS, default="ACTIVE", null=True, blank=True)
    is_deleted = models.Bo<PERSON>anField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.title

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "ATTENDANCE SHEET"
        verbose_name_plural = "ATTENDANCE SHEETS"


class Shift(BaseModel):
    employee = models.ManyToManyField(Employee, related_name='shift')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True, related_name='shift')
    title = models.CharField(max_length=255, null=True, blank=True)
    shift_resumption_time = models.TimeField(null=True, blank=True)
    shift_closing_time = models.TimeField(null=True, blank=True)
    attendance = models.ForeignKey(AttendanceSheet, on_delete=models.CASCADE, blank=True, null=True, related_name='attendance_shift')
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.title
    
    class Meta:
        ordering = ["-date_created"]
        verbose_name = "SHIFT"
        verbose_name_plural = "SHIFTS"


class Location(BaseModel):
    LOCATION_TYPE = [
        ("REMOTE", "remote"),
        ("ONSITE", "onsite")
    ]
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='location')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='location')
    location_type = models.CharField(max_length=255, choices=LOCATION_TYPE, default="ONSITE")
    name = models.CharField(max_length=255)
    latitude = models.FloatField(default=0)
    longitude = models.FloatField(default=0)
    radius = models.FloatField(default=100.0)
    branch = models.ForeignKey("stock_inventory.Branch", on_delete=models.CASCADE, null=True, blank=True, related_name="location_branch")
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.branch:
            return f'{self.branch.company.company_name} {self.branch.name}'
        else:
            return f'{self.name}'

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "LOCATION"
        verbose_name_plural = "LOCATIONS"

    @classmethod
    def create_company_branch(cls, employee, company, location_type, name, latitude, longitude, branch=None):
        cls.objects.create(
            employee=employee,
            company=company,
            location_type=location_type,
            name=name,
            latitude=latitude,
            longitude=longitude,
            branch=branch,
        )
        send_email.delay(recipient=company.user.email, subject="Company Branch Created",
                template_dir="completed_branch_invite.html", company_name=company.company_name,
                branch_name=name)
        return True
    

class Record(BaseModel):
    WEEK_DAYS = [
        ("MONDAY","MONDAY"),
        ("TUESDAY","TUESDAY"),
        ("WEDNESDAY","WEDNESDAY"),
        ("THURSDAY","THURSDAY"),
        ("FRIDAY","FRIDAY"),
        ("SATURDAY","SATURDAY"),
        ("SUNDAY","SUNDAY")
    ]
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='record')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='record')    
    location = models.ForeignKey(Location, on_delete=models.CASCADE, null=True, blank=True, related_name='record')
    shift = models.ForeignKey(Shift, on_delete=models.CASCADE, null=True, blank=True, related_name='record_shift')
    work_duration = models.DurationField(null=True, blank=True)
    clock_in_longitude = models.FloatField(null=True, blank=True, default=0.0)
    clock_in_latitude = models.FloatField(null=True, blank=True, default=0.0)
    clock_out_longitude = models.FloatField(null=True, blank=True, default=0.0)
    clock_out_latitude = models.FloatField(null=True, blank=True, default=0.0)
    location_type = models.CharField(max_length=255, choices=LOCATION_TYPE, default="ONSITE")
    is_late = models.BooleanField(default=False)
    lateness_duration = models.DurationField(null=True, blank=True)
    break_duration = models.DurationField(null=True, blank=True)
    is_break_exceeded = models.BooleanField(default=False)
    break_excess = models.DurationField(null=True, blank=True)
    overtime_duration = models.DurationField(null=True, blank=True)
    clock_in_time = models.TimeField(null=True, blank=True)
    clock_out_time = models.TimeField(null=True, blank=True)
    week_day = models.CharField(max_length=255, choices=WEEK_DAYS, default="MONDAY")
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    attendance = models.CharField(max_length=255, choices=[], null=True, blank=True)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.update_attendance_choices()

    def update_attendance_choices(self):
        if self.company:
            this_attendance = AttendanceSheet.objects.filter(company=self.company, is_deleted=False)
            self.attendance_choices = [(attendance.title.upper(), attendance.title.upper()) for attendance in this_attendance]
            default_attendance = 'MAIN'  # Change this to the desired default project name
            self.attendance_choices.insert(0, (default_attendance.upper(), default_attendance.upper()))
            self._meta.get_field('attendance').choices = self.attendance_choices
    
    def save(self, *args, **kwargs):
        self.update_attendance_choices()
        super().save(*args, **kwargs)

    def __str__(self):
        if self.employee and self.employee.employee is not None:
            return f'{self.employee.employee.first_name} {self.employee.employee.last_name}'
        else:
            return f'{self.id}'

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "RECORD"
        verbose_name_plural = "RECORDS"


class Clock(BaseModel):
    record = models.OneToOneField(Record, on_delete=models.CASCADE, related_name='clock')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='clock')
    clock_in = models.TimeField(null=True, blank=True)
    clock_out = models.TimeField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.record and self.record.employee.employee is not None:
            return f'{self.record.employee.employee.first_name} {self.record.employee.employee.last_name}'
        else:
            return f'{self.id}'

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "CLOCK"
        verbose_name_plural = "CLOCKS"


class Break(BaseModel):
    record = models.ForeignKey(Record, on_delete=models.CASCADE, related_name= '+')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='+')
    break_start_time = models.DateTimeField(null=True, blank=True)
    break_end_time = models.DateTimeField(null=True, blank=True)
    break_duration = models.DurationField(null=True, blank=True)
    start_break_longitude = models.FloatField(null=True, blank=True, default=0.0)
    start_break_latitude = models.FloatField(null=True, blank=True, default=0.0)
    end_break_longitude = models.FloatField(null=True, blank=True, default=0.0)
    end_break_latitude = models.FloatField(null=True, blank=True, default=0.0)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.record and self.record.employee.employee is not None:
            return f'{self.record.employee.employee.first_name} {self.record.employee.employee.last_name}'
        else:
            return f'{self.id}'

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "BREAK"
        verbose_name_plural = "BREAKS"


class OverTime(BaseModel):
    record = models.OneToOneField(Record, on_delete=models.CASCADE, related_name= 'overtime')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='overtime')
    overtime_start_time = models.DateTimeField(null=True, blank=True)
    overtime_end_time = models.DateTimeField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.record and self.record.employee.employee is not None:
            return f'{self.record.employee.employee.first_name} {self.record.employee.employee.last_name}'
        else:
            return f'{self.id}'

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "OVERTIME"
        verbose_name_plural = "OVERTIMES"


class Task(BaseModel):
    STATUSES = [
        ("PENDING", 'pending'),
        ("IN_PROGRESS", 'in_progress'),
        ("COMPLETED", 'Completed'),
        ("CLOSED", 'closed')]
    PRIORITY = [
        ("IMPORTANT", 'important'),
        ("URGENT", 'urgent')]
    record = models.ForeignKey(Record, on_delete=models.CASCADE, null=True, blank=True, related_name='task')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='task')
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='task')
    assignee = models.ManyToManyField(Employee, related_name='task_assignee')
    project_name = models.CharField(max_length=255, null=True, blank=True)
    title = models.CharField(max_length=255, null=True, blank=True)
    start_date = models.DateTimeField(null=True, blank=True)
    due_date = models.DateTimeField(null=True, blank=True)
    task_description = models.CharField(max_length=255, null=True, blank=True)
    status = models.CharField(max_length=255, choices=STATUSES, default="PENDING", null=True, blank=True)
    challenges = models.CharField(max_length=255, null=True, blank=True)
    achievement =  models.CharField(max_length=255, null=True, blank=True)
    other_information =  models.CharField(max_length=255, null=True, blank=True)
    priority =  models.CharField(max_length=255, choices=PRIORITY, default="IMPORTANT", null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    date_updated = models.DateTimeField(auto_now=True, null=True, blank=True)
    def __str__(self):
        if self.record and self.record.employee.employee is not None:
            return f'{self.record.employee.employee.first_name} {self.record.employee.employee.last_name}'
        else:
            return f'{self.id}'
    class Meta:
        ordering = ["-date_created"]
        verbose_name = "TASK"
        verbose_name_plural = "TASKS"


class Screenshot(BaseModel):
    record = models.ForeignKey(Record, on_delete=models.CASCADE, related_name='screenshot', null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='screenshot')
    timestamp = models.DateTimeField(auto_now_add=True)
    image_url = models.URLField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    date_updated = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        if self.record and self.record.employee.employee is not None:
            return f'{self.record.employee.employee.first_name} {self.record.employee.employee.last_name}'
        else:
            return f'{self.id}'
 
    class Meta:
        ordering = ["-date_created"]
        verbose_name = "SCREENSHOT"
        verbose_name_plural = "SCREENSHOTS"


class CompanyBranchInvite(BaseModel):
    INVITE_STATUS = [
        ("PENDING", "PENDING"),
        ("COMPLETED", "COMPLETED"),
        ("EXPIRED", "EXPIRED"),
    ]
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='company_branch_employee')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='company_branch_location')
    latitude = models.FloatField(default=0)
    longitude = models.FloatField(default=0)
    invite_id = models.UUIDField(
        default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    location_radius = models.FloatField(default=0.0, null=True, blank=True)
    invite_status = models.CharField(max_length=255, choices=INVITE_STATUS, null=True, blank=True, default="PENDING")
    branch = models.ForeignKey("stock_inventory.Branch", on_delete=models.CASCADE, null=True, blank=True, related_name="company_invite_branch")
    image = models.TextField(blank=True, null=True)
    is_expired = models.BooleanField(default=False)
    is_completed = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


    def __str__(self):
        return f"{self.company.company_name} {self.invite_id}"

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "COMPANY BRANCH LOCATION"
        verbose_name_plural = "COMPANY BRANCH LOCATIONS"

    @classmethod
    def create_branch_location_invite(cls, location_name, company, employee, branch=None):
        cls.objects.create(
            name=location_name,
            company=company,
            employee=employee,
            branch=branch,
        )

class CompanyMeeting(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True, related_name='company_meeting')
    meeting_name = models.CharField(max_length=255, null=True, blank=True)
    meeting_link = models.CharField(max_length=255, null=True, blank=True)
    meeting_employee = models.ManyToManyField(Employee, related_name='meeting_employee')
    meeting_date = models.DateField(null=True, blank=True)
    meeting_time = models.TimeField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "COMPANY MEETING"
        verbose_name_plural = "COMPANY MEETINGS"