from import_export import resources

from . import models


# Create your resource(s) here.
class ClockResource(resources.ModelResource):

    class Meta:
        model = models.Clock


class AttendanceSheetResource(resources.ModelResource):

    class Meta:
        model = models.AttendanceSheet


class BreakResource(resources.ModelResource):

    class Meta:
        model = models.Break


class LocationResource(resources.ModelResource):

    class Meta:
        model = models.Location


class OverTimeResource(resources.ModelResource):

    class Meta:
        model = models.OverTime


class RecordResource(resources.ModelResource):

    class Meta:
        model = models.Record


class TaskResource(resources.ModelResource):

    class Meta:
        model = models.Task


class ScreenshotResource(resources.ModelResource):

    class Meta:
        model = models.Screenshot


class ShiftResource(resources.ModelResource):

    class Meta:
        model = models.Shift

class CompanyBranchInviteResource(resources.ModelResource):

    class Meta:
        model = models.CompanyBranchInvite

class CompanyMeetingResource(resources.ModelResource):

    class Meta:
        model = models.CompanyMeeting