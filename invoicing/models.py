from datetime import date, datetime
import secrets
import string
from typing import Optional

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models, transaction
from django.db.models.functions import Coalesce, ExtractMonth
from django.db.models.query import QuerySet
import pytz

from core.models import BaseModel
from helpers.custom_filters import QuerysetCustomFilter
from invoicing.helpers.enums import InvoiceTypes
from requisition.models import Company
from sales_app.models import Customer
from stock_inventory.models import Branch


User = settings.AUTH_USER_MODEL
logger = settings.LOGGER


# Create your model(s) here.
class ConstantVariable(BaseModel):
    invoice_ref = models.CharField(max_length=10)

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CONSTANT VARIABLE"
        verbose_name_plural = "CONSTANT VARIABLES"

    @classmethod
    def invoice_number(cls):
        month = date.today().strftime("%b").upper()
        random_string = "".join(
            secrets.choice(string.ascii_letters + string.digits) for _ in range(8)
        )
        return f"INV-{month}-{random_string}"


class Invoice(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    reference = models.CharField(
        max_length=25,
        unique=True,
        editable=False,
    )
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )
    status = models.CharField(
        max_length=25,
        choices=InvoiceTypes.choices,
        default=InvoiceTypes.PENDING,
    )
    due_date = models.DateField(null=True, blank=True)
    paid = models.BooleanField(default=False, editable=False)
    paid_at = models.DateTimeField(null=True, blank=True)
    due = models.BooleanField(default=False, editable=False)
    due_days = models.IntegerField(default=0, editable=False)
    tax_rate = models.FloatField(default=0.0)
    charges = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )
    description = models.TextField(
        null=True,
        blank=True,
        help_text="Invoice summary or description",
    )
    received_part_payment = models.BooleanField(default=False)
    amount_received = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    postdated = models.BooleanField(default=False)
    logo = models.TextField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, editable=False)

    def __str__(self) -> str:
        return self.reference

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "INVOICE"
        verbose_name_plural = "INVOICES"

    @property
    def is_due(self):
        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        return True if current_time.date() >= self.due_date else False

    @classmethod
    def confirm_payment(
        cls,
        reference: str,
        datetime: object,
        is_paid: Optional[bool] = False,
        customer: Optional[Customer] = None,
    ):
        invoice = cls.objects.filter(reference=reference).last()
        if invoice is not None:
            if is_paid:
                invoice.status=InvoiceTypes.PAID
                invoice.paid=True
                invoice.paid_at=datetime
            if customer is not None:
                invoice.customer = customer
            invoice.save()
        return invoice

    @classmethod
    def paid_and_due_analytics(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_invoices = cls.objects.filter(company=company)
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=company_invoices,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            invoices_analytics = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    paid_invoices=models.Count(
                        models.Case(models.When(paid=True, then=1))
                    ),
                    due_invoices=models.Count(
                        models.Case(models.When(due=True, then=1))
                    ),
                )
                .values("month", "paid_invoices", "due_invoices")
                .order_by("month")
            )
            return {"status": True, "details": invoices_analytics}
        return custom_queryset

    @classmethod
    def branch_comparison(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_invoices = cls.objects.filter(company=company)
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=company_invoices,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            branches_comparison = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month", "branch")
                .annotate(
                    count=models.Count("id"),
                    branch_name=models.F("branch__name"),
                )
                .values("month", "branch_name", "count")
                .order_by("month")
            )
            return {"status": True, "details": branches_comparison}
        return custom_queryset
    
    @classmethod
    def branch_analytics(
        cls,
        company: Company,
        branch: Branch,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_invoices = cls.objects.filter(company=company)
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=company_invoices,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            invoices_analytics = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    paid_invoices=models.Count(
                        models.Case(models.When(paid=True, then=1))
                    ),
                    paid_amount=Coalesce(models.Sum(
                        models.Case(models.When(paid=True, then="amount"))
                    ), models.Value(0, output_field=models.DecimalField())),
                    due_invoices=models.Count(
                        models.Case(models.When(due=True, then=1))
                    ),
                    due_amount=Coalesce(models.Sum(
                        models.Case(models.When(due=True, then="amount"))
                    ), models.Value(0, output_field=models.DecimalField())),
                    unpaid_invoices=models.Count(
                        models.Case(models.When(paid=False, then=1))
                    ),
                    unpaid_amount=Coalesce(models.Sum(
                        models.Case(models.When(paid=False, then="amount"))
                    ), models.Value(0, output_field=models.DecimalField())),
                    total_draft=models.Count("id"),
                    total_draft_amount=models.Sum("amount"),
                )
                .values(
                    "month",
                    "paid_invoices",
                    "paid_amount",
                    "due_invoices",
                    "due_amount",
                    "unpaid_invoices",
                    "unpaid_amount",
                    "total_draft",
                    "total_draft_amount",
                )
                .order_by("month")
            )
            return {"status": True, "details": invoices_analytics}
        return custom_queryset


class InvoiceItem(BaseModel):
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        editable=False,
    )
    item_description = models.CharField(max_length=255)
    unit_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    quantity = models.IntegerField(editable=False)
    discount = models.FloatField(
        default=0.0,
        editable=False,
    )
    total_amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "INVOICE ITEM"
        verbose_name_plural = "INVOICE ITEMS"

    @classmethod
    def register_item(
        cls,
        invoice: Invoice,
        item_description: str,
        unit_price: float,
        quantity: int,
        discount: Optional[float] = 0.0,
    ):
        """
        NOTE [LOGIC]:
        - each item discount value will be subtracted from the total cost of the item.
        """
        total_cost = float(unit_price * quantity)
        discount_amount = total_cost * float(round((discount / 100), 2))

        item = cls.objects.create(
            invoice=invoice,
            item_description=item_description,
            unit_price=unit_price,
            quantity=quantity,
            discount=discount,
            total_amount=total_cost - discount_amount,
        )
        return item

    @classmethod
    @transaction.atomic
    def generate_invoice(
        cls,
        company: Company,
        items: list,
        created_by: User,
        customer: Optional[Customer] = None,
        tax_rate: Optional[float] = 0.0,
        description: Optional[str] = None,
        discount_value: Optional[float] = 0.0,
        delivery_fee: Optional[float] = 0.0,
        received_part_payment: Optional[bool] = False,
        amount_received: Optional[float] = 0.0,
        logo: Optional[str] = None,
        branch: Optional[Branch] = None,
        batch_id: Optional[str] = None,
        due_date: Optional[object] = None,
    ):
        """ """
        from core.models import PaystackPayment

        invoice = Invoice.objects.create(
            company=company,
            branch=branch,
            customer=customer,
            reference=ConstantVariable.invoice_number(),
            due_date=due_date,
            tax_rate=tax_rate,
            description=description,
            received_part_payment=received_part_payment,
            amount_received=amount_received,
            logo=logo,
            created_by=created_by,
        )
        total_amount = 0
        for data in items:
            item = cls.register_item(
                invoice=invoice,
                item_description=data.get("item_description"),
                unit_price=data.get("unit_price"),
                quantity=data.get("quantity"),
                discount=data.get("discount", 0.0),
            )
            total_amount += item.total_amount
        # calculate the total cost.
        sub_total = float(total_amount)
        tax_amount = float((tax_rate / 100) * sub_total)
        amount_payable = (
            sum([sub_total, float(delivery_fee), tax_amount]) - float(discount_value)
        )
        invoice.amount = amount_payable
        invoice.save()
        if customer is not None and customer.email is not None:
            payment_methods = PaystackPayment.generate_payment_link(
                email=company.user.email,
                amount=float(amount_payable),
                reference=batch_id,
            )
            cls.send_customer_invoice(
                company_name=company.company_name,
                customer_name=customer.name,
                customer_email=customer.email,
                invoice_reference=invoice.reference,
                datetime=f"{invoice.created_at.date()} | {invoice.created_at.time().strftime('%H:%M')}",
                sales_items=items,
                sub_total=sub_total,
                vat_value=tax_amount,
                total=amount_payable,
                discount_value=discount_value,
                delivery_fee=delivery_fee,
                balance_due=amount_payable if not received_part_payment else 0.0,
                payment_link=payment_methods.get("payment_link"),
            )
        return invoice

    @classmethod
    def send_customer_invoice(
        cls,
        company_name: str,
        customer_name: str,
        customer_email: str,
        invoice_reference: str,
        datetime: str,
        sales_items: list,
        sub_total: float,
        vat_value: float,
        total: float,
        discount_value: Optional[float] = 0.0,
        delivery_fee: Optional[float] = 0.0,
        partial_payment: Optional[float] = 0.0,
        balance_due: Optional[float] = 0.0,
        payment_link: Optional[str] = None,
    ):
        from core.tasks import send_email
        from helpers.reusable_functions import format_currency

        html_content = []
        for data in sales_items:
            # Generate dynamic HTML content and append accordingly.
            item_content = f"""
            <tr>
                <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                    {data.get("item_description")}
                </td>
                <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                    {data.get("quantity")}
                </td>
                <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                    {data.get("unit_price")}
                </td>
                <td style="border-bottom: 0.1px solid #d3d3d3; padding: 8px; text-align: left; font-size: 12px; color: #242424;";>
                    {data.get("quantity") * data.get("unit_price")}
                </td>
            </tr>
            """
            html_content.append(item_content)
        # Re-generate HTML content as a single string variable.
        items = "\n".join(html_content)
        # Forward email to the customer.
        send_email.delay(
            recipient=customer_email,
            subject=f"Invoice from {company_name}",
            template_dir="dynamic_invoice.html",
            company_name=company_name,
            customer_name=customer_name,
            invoice_reference=invoice_reference,
            datetime=datetime,
            items=items,
            sub_total=format_currency(sub_total),
            vat_value=format_currency(vat_value),
            total=format_currency(total),
            discount_value=format_currency(discount_value),
            delivery_fee=format_currency(delivery_fee),
            partial_payment=format_currency(partial_payment),
            balance_due=format_currency(balance_due),
            payment_method=payment_link,
        )


class Tax(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    title = models.CharField(max_length=125)
    rate = models.FloatField()
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "TAX"
        verbose_name_plural = "TAXES"

    def save(self, *args, **kwargs) -> None:
        self.title = self.title.title()
        super(Tax, self).save(*args, **kwargs)

    @classmethod
    def add(
        cls,
        company: Company,
        title: str,
        rate: float,
        created_by: User,
        branch: Optional[Branch] = None,
    ):
        company_tax = cls.objects.filter(company=company, title=title.title()).first()
        if company_tax is not None:
            return {"status": False, "tax": company_tax}
        company_tax = cls.objects.create(
            company=company,
            branch=branch,
            title=title,
            rate=rate,
            created_by=created_by,
        )
        return {"status": True, "tax": company_tax}
