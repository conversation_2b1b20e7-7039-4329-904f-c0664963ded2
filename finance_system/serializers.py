from rest_framework import serializers

from finance_system.models import AccountType, ChartOfAccount, CompanyManualBank, Journal

class AllCompanyManualBankSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyManualBank
        fields = [
            "id",
            "account_name",
            "account_number",
            "balance",
            "previous_balance",
            "bank_name",
            "bank_code",
        ]

class EditCompanyManualBankSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyManualBank
        fields = [
            "id",
            "balance",
            "previous_balance",
        ]

class AddCompanyManualBankSerializer(serializers.Serializer):
    account_name = serializers.CharField(required=True, allow_null=False)
    account_number = serializers.CharField(required=True, allow_null=False)
    bank_name = serializers.CharField(required=True, allow_null=False)
    bank_code = serializers.CharField(required=True, allow_null=False)
    
    def validate(self, values):
        company_uuid = self.context["company_uuid"]
        account_number = values["account_number"]
        account = CompanyManualBank.objects.filter(
            company__id=company_uuid, 
            account_number=account_number,
            is_deleted=False
            ).exists()
        if account:
            raise serializers.ValidationError(
                {"message": f"{account_number} already exist"}
            )
        return values
    
class AllJournalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Journal
        fields = [
            "account_type",
            "journal_name",
            "journal_debit_amount",
            "journal_credit_amount",
            "journal_description",
            "journal_date",
            "journal_reference_no",
        ]

class AddJournalSerializer(serializers.Serializer):
    journal_name = serializers.CharField(required=True, allow_null=False)
    journal_debit_amount = serializers.FloatField(required=True, allow_null=False)
    journal_credit_amount = serializers.FloatField(required=True, allow_null=False)
    journal_description = serializers.CharField(required=True, allow_null=False)
    journal_reference_no = serializers.CharField(required=True, allow_null=False)
    journal_date = serializers.DateTimeField(required=True, allow_null=False)

    def validate(self, values):
        journal_debit_amount = values["journal_debit_amount"]
        journal_credit_amount = values["journal_credit_amount"]
        if journal_debit_amount < 0:
            raise serializers.ValidationError(
                {"message": "Debit amount cannot be less than 0"}
            )
        if journal_credit_amount < 0:
            raise serializers.ValidationError(
                {"message": "Credit amount cannot be less than 0"}
            )
        
        return values

class MultipleJournalSerializer(serializers.Serializer):
    journal_data = serializers.ListSerializer(child=AddJournalSerializer())
    def validate(self, values):
        if len(values["journal_data"]) < 2:
            raise serializers.ValidationError(
                {"error_code": "24", "journal_data": "journal_data cannot be empty"}
            )
        return values
    
class AllAccountTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountType
        fields = [
            "id",
            "account_type_name",
        ]

class AccountTypeMemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountType
        fields = [
            "id",
            "account_type_name",
        ]
class AllChartOfAccountSerializer(serializers.ModelSerializer):
    account_type = AccountTypeMemberSerializer(read_only=True)
    class Meta:
        model = AccountType
        fields = [
            "id",
            "chart_of_account_name",
            "account_type",
            "chart_of_account_description"
        ]

class AddChartOfAccountSerializer(serializers.Serializer):
    chart_of_account_name = serializers.CharField(required=True, allow_null=False)
    account_type_id = serializers.CharField(required=True, allow_null=False)
    chart_of_account_description = serializers.CharField(allow_blank=True, allow_null=True)
    
    def validate(self, values):
        chart_of_account_name = values["chart_of_account_name"]
        account_type_id = values["account_type_id"]
        chart_of_account_ins = ChartOfAccount.objects.filter(
            chart_of_account_name=chart_of_account_name,
            is_deleted=False
            ).first()
        if not chart_of_account_ins:
            raise serializers.ValidationError(
                {"message": f"{chart_of_account_name} does not exist"}
            )
        account_type_ins = AccountType.objects.filter(
            id=account_type_id, is_active=True
            ).first()
        if not account_type_ins:
            raise serializers.ValidationError(
                {"message": f"{account_type_id} does not exist"}
            )
        return values