from django.db import models
from django.contrib.auth import get_user_model


from core.models import BaseModel

from requisition.models import Company

User = get_user_model()
# Create your models here.


class CompanyManualBank(models.Model):
    company_owner = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                      related_name="manual_account_owner")
    company = models.ForeignKey("requisition.Company", on_delete=models.CASCADE, related_name="manual_account_company")
    account_number = models.CharField(max_length=200, blank=True, null=True)
    account_name = models.CharField(max_length=200, blank=True, null=True)
    balance = models.FloatField(null=True, blank=True, default=0.0)
    previous_balance = models.FloatField(null=True, blank=True, default=0.0)
    bank_name = models.CharField(max_length=200, blank=True, null=True)
    bank_code = models.CharField(max_length=200, blank=True, null=True)
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    added_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                      related_name="manual_account_added_by")
    edited_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                      related_name="manual_account_edited_by")
    is_deleted = models.BooleanField(default=False)

    class Meta:
        verbose_name = "COMPANY MANUAL BANK"
        verbose_name_plural = "COMPANY MANUAL BANKS"

    def __str__(self):
        return str(self.bank_name)
    
class Journal(models.Model):
    company_owner = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                      related_name="journal_owner")
    company = models.ForeignKey("requisition.Company", on_delete=models.CASCADE, related_name="journal_company")
    account_type = models.CharField(max_length=200, blank=True, null=True)
    journal_name = models.CharField(max_length=200, blank=True, null=True)
    journal_debit_amount = models.FloatField(null=True, blank=True, default=0.0)
    journal_credit_amount = models.FloatField(null=True, blank=True, default=0.0)
    journal_description = models.CharField(max_length=200, blank=True, null=True)
    journal_date = models.DateField(blank=True, null=True)
    journal_reference_no = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    added_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                      related_name="journal_added_by")
    edited_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                      related_name="journal_edited_by")
    is_deleted = models.BooleanField(default=False)

    class Meta:
        verbose_name = "COMPANY JOURNAL"
        verbose_name_plural = "COMPANY JOURNALS"

    def __str__(self):
        return str(self.journal_name)
    
class AccountType(models.Model):
    account_type_name = models.CharField(max_length=200, blank=True, null=True)
    account_type_description = models.CharField(max_length=200, blank=True, null=True)
    account_type_operation = models.CharField(max_length=200, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=False)

    class Meta:
        verbose_name = "ACCOUNT TYPE"
        verbose_name_plural = "ACCOUNT TYPES"

    def __str__(self):
        return str(self.account_type_name)
    
class ChartOfAccount(models.Model):
    chart_of_account_name = models.CharField(max_length=200, blank=True, null=True)
    account_type = models.ForeignKey("finance_system.AccountType", on_delete=models.CASCADE, related_name="account_type_module")
    chart_of_account_description = models.CharField(max_length=200, blank=True, null=True)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "CHART OF ACCOUNT"
        verbose_name_plural = "CHART OF ACCOUNTS"

    def __str__(self):
        return str(self.chart_of_account_name)