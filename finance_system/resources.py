from django.contrib.auth import get_user_model
from import_export import resources, fields
from import_export.widgets import ForeignKeyWidget

from finance_system.models import AccountType, ChartOfAccount, CompanyManualBank, Journal


User = get_user_model()



class CompanyManualBankResource(resources.ModelResource):
    class Meta:
        model = CompanyManualBank

class JournalResource(resources.ModelResource):
    class Meta:
        model = Journal

class AccountTypeResource(resources.ModelResource):
    class Meta:
        model = AccountType

class ChartOfAccountResource(resources.ModelResource):
    class Meta:
        model = ChartOfAccount