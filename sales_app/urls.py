from django.urls import include, path

from sales_app import views


# Create your url pattern(s) here.
branch_urls = [
    path("register/", views.RegisterSalesAPIView.as_view()),
    path("transaction/items", views.SalesTransactionItemAPIView.as_view()),
    path("confirm_sales/", views.ConfirmSalesAPIView.as_view()),
    path("branch_dashboard", views.BranchDashboardTodayAPIView.as_view()),
    path("branch_dashboard_history", views.BranchDashboardAPIView.as_view()),
    path("branch_history", views.BranchSalesTableAPIView.as_view()),
    path("branch_total_sales", views.BranchTotalSalesAPIView.as_view()),
    path("branch_average_sales", views.BranchAverageSalesAPIView.as_view()),
    path("branch_gross_profit", views.BranchGrossProfitAPIView.as_view()),
    path("branch_products_sold", views.BranchProductsSoldAPIView.as_view()),
    path("branch_top_products", views.BranchTopProductsAPIView.as_view()),
    path("branch_instant_account/", views.BranchInstantAccountAPIView.as_view()),
    path("branch_verify_transfer", views.BranchVerifyTransferAPIView.as_view()),
    path("branch_ai_converter/", views.AIConverterAPIView.as_view()),
    path("branch_register_rrn/", views.RegisterRRNAPIView.as_view()),
    path("branch_card_callbacks/", views.CardTransactionCallbackAPIView.as_view()),
    path("branch_payment_history", views.PaymentHistoryAPIView.as_view()),
    path("register_offline/", views.OfflineSalesAPIView.as_view()),
    path("offline_accounts", views.OfflineVirtualAccounts.as_view()),
    path("withdraw/", views.WalletWithdrawalAPIView.as_view()),
]

company_urls = [
    path("company", views.CompanyOverviewAPIView.as_view()),
    path("branches", views.CompanySalesBranchAPIView.as_view()),
    path("history", views.CompanySalesTableAPIView.as_view()),
    path("comparison", views.CompanyBranchComparisonAPIView.as_view()),
    path("refund_comparison", views.CompanyBranchRefundComparisonAPIView.as_view()),
    path("categories_above", views.CompanyCategoriesAboveAvgAPIView.as_view()),
    path("categories_below", views.CompanyCategoriesBelowAvgAPIView.as_view()),
    path("top_four_branches", views.TopFourBranchesAPIView.as_view()),
    path("top_four_customers", views.TopFourCustomersAPIView.as_view()),
    path("stock_and_sales", views.StockAndSalesAPIView.as_view()),
    path("cash_at_hand", views.CashAtHandAPIView.as_view()),
    path("profit_tracking", views.ProfitTrackingAPIView.as_view()),
    path("top_products", views.TopTenProductsAPIView.as_view()),
    path("active_clerks", views.ActiveClerksAPIView.as_view()),
    path("wallet", views.SalesWalletHistoryAPIView.as_view()),
]

customer_urls = [
    path("customers/", views.CustomerAPIView.as_view()),
    path("customers/add_location/", views.CustomerLocationAPIView.as_view()),
    path("customers/summary", views.CustomerSummaryAPIView.as_view()),
    path("customers/top_active", views.TopActiveCustomerAPIView.as_view()),
    path("customers/top_inactive", views.TopInactiveCustomerAPIView.as_view()),
    path("customers/transactions", views.CustomerTransactionAPIView.as_view()),
    path("customers/history", views.CustomerHistoryAPIView.as_view()),
    path("customers/bulk_upload/", views.CustomerFileUpload.as_view()),
    path("customers/download_sample", views.CustomerSampleSheetAPIView.as_view()),
]

payment_link_urls = [
    path("payment_link/", views.PaymentLinkAPIView.as_view()),
]

return_refund_urls = [
    path("transactions", views.SalesTransactionsAPIView.as_view()),
    path("return_refund/", views.ReturnRefundAPIView.as_view()),
    path("refund/approve_decline/", views.RefundApproveDeclineAPIView.as_view()),
    path("branch_refunds", views.BranchTopRefundsAPIView.as_view()),
    path("swap/", views.SwapItemsTransactionAPIView.as_view()),
]


user_urls = [
    path("get_user_details", views.UserDetailsAPIView.as_view()),
]

urlpatterns = [
    path("", include(customer_urls)),
    path("", include(branch_urls)),
    path("", include(company_urls)),
    path("", include(payment_link_urls)),
    path("", include(return_refund_urls)),
    path("", include(user_urls)),
]
