from datetime import datetime, timedelta
import pytz
import random

from django.conf import settings
from django.core.management.base import (
    BaseCommand,
    CommandError,
    CommandParser,
)

from core.models import User
from requisition.models import Company
from sales_app import models
from sales_app.helper.enums import CashBookChoices


def cash_book_type():
    return random.choice([CashBookChoices.CASH_IN_HAND, CashBookChoices.CASH_IN_BANK])


class Command(BaseCommand):
    help = "CREATES STOCKS RECORD(S) FOR THE SPECIFIED COMPANY."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("user_id")
        parser.add_argument("company_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        user = User.objects.filter(id=kwargs["user_id"]).first()
        if user is None:
            raise CommandError("USER RECORD NOT FOUND.")
        company = Company.objects.filter(id=kwargs["company_id"]).first()
        if company is None:
            raise CommandError("COMPANY RECORD NOT FOUND.")
        company_branches = models.Branch.objects.filter(company=company)
        if not company_branches.exists():
            raise CommandError("COMPANY HAS NO BRANCH RECORDs.")

        sales_transactions = models.SalesTransaction.objects.all()
        # select and create a customer.
        for sales_transaction in sales_transactions:
            cash_book_record = models.CashBook.objects.create(
                company=sales_transaction.company,
                branch=sales_transaction.branch,
                cash_book_type=cash_book_type(),
                amount=sales_transaction.total_cost,
                sales_batch_id=sales_transaction.batch_id,
            )
            cash_book_record.created_at = sales_transaction.created_at
            cash_book_record.save()

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        print(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
