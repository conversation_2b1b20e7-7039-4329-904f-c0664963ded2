import json
import requests

from django.core.management.base import BaseCommand, CommandParser

from account.helpers.core_banking import CoreBankingService
from sales_app.models import OfflineVirtualAccount
from stock_inventory.models import Branch


class Command(BaseCommand):
    help = "REGISTER OFFLINE VIRTUAL ACCOUNTS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("branch_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        branch_id = kwargs["branch_id"]
        branch = Branch.objects.filter(id=branch_id)
        if branch.exists():
            url = "https://banking.libertypayng.com/api/v1/wema/offline_accounts/"
            token = CoreBankingService().login()
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            payload = json.dumps({})
            response = requests.request(
                "GET",
                f"{url}?unique_id={str(branch.first().id)}",
                headers=headers,
                data=payload,
            )
            try:
                response_data = response.json()
            except requests.exceptions.JSONDecodeError:
                response_data = None
            if response.status_code == 200 and response_data is not None:
                data = response_data.get("data").get("offline_accounts")
                if len(data) > 0:
                    for data in data:
                        OfflineVirtualAccount.objects.create(
                            company=branch.first().company,
                            branch=branch.first(),
                            account_name=data.get("first_name"),
                            account_number=data.get("account_number"),
                            unique_code=data.get("unique_code"),
                            confirmation_code=data.get("confirmation_code"),
                            bank_name=data.get("bank_name"),
                            bank_code=data.get("bank_code"),
                        )
                    self.stdout.write(
                        self.style.SUCCESS("REQUEST SUCCESSFUL.")
                    )
            else:
                self.stdout.write(
                    self.style.ERROR(f"{response.text}.")
                )
