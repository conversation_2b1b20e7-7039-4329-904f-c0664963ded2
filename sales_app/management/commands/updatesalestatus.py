from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
import pytz

from sales_app.models import SalesTransactionItem


class Command(BaseCommand):
    help = "UPDATE EXISTING SALES TRANSACTION(S)."

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        sales_transactions = SalesTransactionItem.objects.all()
        for sales_transaction in sales_transactions:
            sales_transaction.gross_profit = float(sales_transaction.amount) - float(
                sales_transaction.item.product_price
            )
            sales_transaction.save()
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        print(f"TIME DIFFERENCE:  {END_TIME - START_TIME}")
