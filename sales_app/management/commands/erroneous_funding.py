import time

from django.core.management.base import BaseCommand

from account.enums import Acct<PERSON>rovider, TransactionStatus, TransactionType
from account.models import AccountSystem, Transaction, Wallet


class Command(BaseCommand):

    def handle(self, *args, **kwargs):

        transactions = Transaction.objects.filter(
            user_email="<EMAIL>",
            date_created__date="2025-03-10",
            transaction_type=TransactionType.DEPOSIT,
        )
        print(f"\n\nTOTAL TRANSACTIONS: {transactions.count()}\n\n")
        time.sleep(10)
        self.stdout.write(
            self.style.SUCCESS(
                "SYSTEM HAS STARTED A TRANSACTION UPDATE FOR RETRIEVAL!!!")
        )
        for transaction in transactions:
            account_details = AccountSystem.objects.filter(
                account_number=transaction.beneficiary_account_number
            ).last()
            wallet = Wallet.objects.filter(
                account=account_details
            ).last()
            transaction_instance = Transaction.objects.create(
                user=account_details.user,
                company_name=account_details.company.company_name,
                beneficiary_account_number=account_details.account_number,
                beneficiary_account_name=account_details.account_name,
                bank_code=account_details.bank_code,
                source_account_name=account_details.account_name,
                source_account_number=account_details.account_number,
                user_full_name=account_details.user.full_name,
                user_email=account_details.user.email,
                transaction_type=TransactionType.RETRIEVAL,
                amount=transaction.amount,
                total_amount_received=transaction.total_amount_received,
                status=TransactionStatus.PENDING,
                transfer_provider=AcctProvider.WEMA,
                narration="funds moved erroneously.",
                payout_type=account_details.account_type,
                company_id=account_details.company.id,
            )
            Wallet.charge_wallet(
                wallet_instance=wallet,
                amount=transaction.amount,
                transaction_instance=transaction_instance,
            )
            self.stdout.write(
                self.style.SUCCESS(
                    "SYSTEM HAS COMPLETED THE TRANSACTION UPDATE FOR RETRIEVAL!!!")
            )
