from datetime import date, datetime
from decimal import Decimal
import json
import math
from typing import Optional

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models, transaction
from django.db.models.functions import ExtractMonth
from django.db.models.query import QuerySet
import openpyxl
import pandas as pd
import pytz
from rest_framework.serializers import ValidationError

from account.enums import DebitCreditEntry
from core.models import BaseModel, DeleteHandler
from helpers.custom_filters import QuerysetCustomFilter
from helpers.enums import ChannelTypes, Currency, RequestMethodType
from requisition.models import Company
from sales_app.helper import enums
from sales_app.utils import validate_amount
from stock_inventory.models import (
    Branch,
    Category,
    Product,
)


User = settings.AUTH_USER_MODEL


# Create your model(s) here.
class ConstantVariable(BaseModel):
    paybox_wema_nuban = models.CharField(
        max_length=10,
        null=True,
        blank=True,
    )
    commission_wallet = models.Char<PERSON>ield(
        max_length=10,
        null=True,
        blank=True,
    )
    sales_charge = models.FloatField(default=0.0)
    sales_charge_cap = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    sales_withdrawal_charge = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CONSTANT VARIABLE"
        verbose_name_plural = "CONSTANT VARIABLES"


class Customer(BaseModel):
    """
    This model holds information about a company's 'Customer' or 'Client'.
    NOTE [LOGIC]:
    - customer registration requires that either the email or phone number
        is provided for uniqueness and identification purpose.
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField(
        max_length=255,
        null=True,
        blank=True,
    )
    phone = models.CharField(
        max_length=25,
        null=True,
        blank=True,
    )
    address = models.TextField(
        null=True,
        blank=True,
    )
    city = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    state = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    country = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    unique_identifier = models.CharField(
        max_length=25,
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=25,
        choices=enums.CustomerStatus.choices,
        default=enums.CustomerStatus.INACTIVE,
        editable=False,
    )
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_customer",
    )

    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CUSTOMER"
        verbose_name_plural = "CUSTOMERS"

    @classmethod
    def create_record(
        cls,
        company: Company,
        branch: Branch,
        created_by: User,
        name: str,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        address: Optional[str] = None,
        city: Optional[str] = None,
        state: Optional[str] = None,
        country: Optional[str] = None,
    ):
        customer = cls.objects.filter(company=company)
        if email and phone:
            customer = customer.filter(email=email, phone=phone).first()
        if email and not phone:
            customer = customer.filter(email=email).first()
        if phone and not email:
            customer = customer.filter(phone=phone).first()
        if customer is not None:
            return {
                "status": False,
                "details": "customer with this details already exists.",
                "customer": customer,
            }
        else:
            customer = cls.objects.create(
                company=company,
                branch=branch,
                created_by=created_by,
                name=name,
                phone=phone,
                email=email,
                address=address,
                city=city,
                state=state,
                country=country,
            )
            if address is not None:
                CustomerLocation.objects.create(
                    customer=customer,
                    location=address,
                    created_by=created_by,
                )
            return {
                "status": True,
                "details": "successfully created customer record.",
                "customer": customer,
            }

    @classmethod
    def update_profile(
        cls,
        updated_by: User,
        customer: object,
        name: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        address: Optional[str] = None,
        city: Optional[str] = None,
        state: Optional[str] = None,
        country: Optional[str] = None,
    ):
        customer.name = name if name is not None else customer.name
        customer.email = email if email is not None else customer.email
        customer.phone = phone if phone is not None else customer.phone
        customer.address = address if address is not None else customer.address
        customer.city = city if city is not None else customer.city
        customer.state = state if state is not None else customer.state
        customer.country = country if country is not None else customer.country
        customer.updated_by = updated_by
        customer.save()
        return customer

    @classmethod
    def get_top_five_active(cls, company: Company):
        active_customers = cls.objects.filter(
            company=company, status=enums.CustomerStatus.ACTIVE
        )
        top_five_active_customers = (
            SalesTransaction.objects.filter(
                customer__in=active_customers,
                status=enums.TransactionStatusChoices.SUCCESSFUL,
            )
            .values("customer")
            .annotate(
                created_at=models.Min("created_at"),
                customer_name=models.F("customer__name"),
                email=models.F("customer__email"),
                transactions=models.Sum("total_sales_amount"),
            )
            .order_by("-transactions")[:5]
        )
        return top_five_active_customers

    @classmethod
    def get_top_five_inactive(cls, company: Company):
        inactive_customers = cls.objects.filter(
            company=company, status=enums.CustomerStatus.INACTIVE
        )
        top_five_inactive_customers = (
            SalesTransaction.objects.filter(
                customer__in=inactive_customers,
                status=enums.TransactionStatusChoices.SUCCESSFUL,
            )
            .values("customer")
            .annotate(
                created_at=models.Min("created_at"),
                customer_name=models.F("customer__name"),
                email=models.F("customer__email"),
                transactions=models.Sum("total_sales_amount"),
            )
            .order_by("-transactions")[:5]
        )
        return top_five_inactive_customers

    @classmethod
    def fetch_customers(
        cls,
        company: Company,
        branch: Optional[Branch] = None,
        active: Optional[bool] = False,
        inactive: Optional[bool] = False,
    ):
        if branch is not None:
            if active:
                return cls.objects.filter(
                    company=company, branch=branch, status=enums.CustomerStatus.ACTIVE
                )
            if inactive:
                return cls.objects.filter(
                    company=company, branch=branch, status=enums.CustomerStatus.INACTIVE
                )
            return cls.objects.filter(company=company, branch=branch)
        else:
            if active:
                return cls.objects.filter(
                    company=company, status=enums.CustomerStatus.ACTIVE
                )
            if inactive:
                return cls.objects.filter(
                    company=company, status=enums.CustomerStatus.INACTIVE
                )
            return cls.objects.filter(company=company)

    @classmethod
    def fetch_customers_transactions(
        cls,
        company: Company,
        active: Optional[bool] = False,
        inactive: Optional[bool] = False,
    ):
        customers = cls.fetch_customers(
            company=company, active=active, inactive=inactive
        )
        customers_transactions = (
            SalesTransaction.objects.filter(
                customer__in=customers,
                status=enums.TransactionStatusChoices.SUCCESSFUL,
            )
            .values("customer")
            .annotate(
                created_at=models.Min("created_at"),
                name=models.F("customer__name"),
                email=models.F("customer__email"),
                phone=models.F("customer__phone"),
                branch=models.F("branch__name"),
                purchase_amount=models.Sum("total_sales_amount"),
                status=models.F("customer__status"),
            )
        )
        return customers_transactions

    @classmethod
    def fetch_customer_invoices(
        cls, company: Company, customer: "Customer", branch: Optional[Branch] = None
    ):
        if branch is not None:
            return (
                SalesTransaction.objects.filter(
                    company=company,
                    branch=branch,
                    customer=customer,
                    status=enums.TransactionStatusChoices.SUCCESSFUL,
                )
                .values("batch_id")
                .annotate(
                    created_at=models.Min("created_at"),
                    invoice=models.F("invoice__reference"),
                    payment=models.F("means_of_payment"),
                    total=models.Sum("total_sales_amount"),
                )
                .order_by("-total")
            )
        else:
            return (
                SalesTransaction.objects.filter(
                    company=company,
                    customer=customer,
                    status=enums.TransactionStatusChoices.SUCCESSFUL,
                )
                .values("batch_id")
                .annotate(
                    created_at=models.Min("created_at"),
                    invoice=models.F("invoice__reference"),
                    payment=models.F("means_of_payment"),
                    total=models.Sum("total_sales_amount"),
                )
                .order_by("-total")
            )

    @classmethod
    def upload_file_record(
        cls, company: Company, branch: Branch, created_by: User, file
    ):
        """
        This method processes and uploads customers' record(s) from an Excel sheet.
        NOTE [LOGIC]:
        - the headers of the uploaded file must conform to the sample sheet made available.
        """
        excel_Workbook = openpyxl.load_workbook(file, read_only=True, data_only=True)
        excel_sheet = excel_Workbook.active
        column_names = [cell.value for cell in excel_sheet[1]]
        data_rows = [row for row in excel_sheet.iter_rows(min_row=2, values_only=True)]
        df = pd.DataFrame(data_rows, columns=column_names)
        data = df.to_dict(orient="records")

        for row in data:
            first_name = row.get("First name")
            last_name = row.get("Last name")
            email = row.get("Email")
            phone = row.get("Phone")
            address = row.get("Address")

            if pd.isna(first_name):
                first_name = ""
            if pd.isna(last_name):
                last_name = ""
            if pd.isna(email):
                email = None
            if pd.isna(phone):
                phone = None
            if pd.isna(address):
                address = None

            cls.create_record(
                company=company,
                branch=branch,
                created_by=created_by,
                name=f"{first_name} {last_name}",
                email=email,
                phone=phone,
                address=address,
            )
        return True


class CustomerLocation(BaseModel):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    location = models.TextField()
    default = models.BooleanField(default=False)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CUSTOMER LOCATION"
        verbose_name_plural = "CUSTOMER LOCATIONS"

    @classmethod
    def add_location(
        cls,
        created_by: User,
        customer: Customer,
        location: str,
        default: Optional[bool] = False,
    ):
        customer.address = location
        customer.save()
        customer_location = cls.objects.create(
            created_by=created_by,
            customer=customer,
            location=location,
            default=default,
        )
        return customer_location


class CashBook(BaseModel):
    """
    This indicates all the SalesTransaction payment history.
    NOTE [USAGE]:
    - This particular object can only be modified programmatically.
    - The client can only read from it and are not allowed to write directly.
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE, editable=False)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, editable=False)
    sales_transaction = models.ForeignKey(
        "sales_app.SalesTransaction",
        on_delete=models.CASCADE,
        editable=False,
    )
    transaction_type = models.CharField(
        max_length=25,
        choices=DebitCreditEntry.choices,
        editable=False,
    )
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    currency = models.CharField(
        max_length=3,
        choices=Currency.choices,
        default=Currency.NAIRA,
        editable=False,
    )
    cash_book_type = models.CharField(
        max_length=12,
        choices=enums.CashBookChoices.choices,
        editable=False,
    )
    payment_channel = models.CharField(
        max_length=25,
        choices=enums.MeansOfPaymentChoices.choices,
        editable=False,
    )
    reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        editable=False,
        help_text="This represents the transaction local inflow ID.",
    )
    status = models.CharField(
        max_length=25,
        choices=enums.TransactionStatusChoices.choices,
        editable=False,
    )

    def __str__(self) -> str:
        return f"{self.amount} | {self.reference} | {self.transaction_type} | {self.payment_channel}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CASH BOOK / PAYMENT HISTORY"
        verbose_name_plural = "CASH BOOK / PAYMENT HISTORIES"


class SalesTransaction(BaseModel):
    """
    This model records all sales transactions with or without customer record(s).
    Due to the advent of 'split payment'; all transaction(s) now have a payment reference. 
    NOTE [ATTRIBUTE]:
    - payment reference equals the batch ID generated.
    - total sales amount is the value of goods sold for a particular transaction.
    - total cost is the value of the transaction which is inclusive of all other charges.
    """

    offline_date = models.DateTimeField(null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    invoice = models.ForeignKey(
        "invoicing.Invoice",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    offline_identifier = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    batch_id = models.CharField(
        max_length=25,
        unique=True,
        null=True,
        blank=True,
    )
    payment_reference = models.CharField(
        max_length=25,
        null=True,
        blank=True,
    )
    sales_tag = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="client transaction identifier (e.g. table 4).",
    )
    status = models.CharField(
        max_length=25,
        choices=enums.TransactionStatusChoices.choices,
    )
    means_of_payment = models.CharField(
        max_length=25,
        choices=enums.MeansOfPaymentChoices.choices,
        null=True,
        blank=True,
    )
    total_sales_amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    discount_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    delivery_fee = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    vat_amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    total_cost = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    charges = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    applicable_charges = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    amount_paid = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    paid = models.BooleanField(default=False)
    paid_at = models.DateTimeField(null=True, blank=True)
    card_rrn = models.CharField(
        max_length=225,
        null=True,
        blank=True,
        help_text="card transaction unique identifier from LibertyPay.",
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="cashier",
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="confirmed_sales",
    )
    device = models.CharField(max_length=25, choices=ChannelTypes.choices)
    rrn_register_payload = models.TextField(
        null=True,
        blank=True,
    )

    def __str__(self) -> str:
        batch_id = self.batch_id if self.batch_id is not None else ""
        return f"{self.branch.__str__()} => {batch_id}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES TRANSACTION"
        verbose_name_plural = "SALES TRANSACTIONS"

    @classmethod
    def register_rrn(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        batch_id: str,
        rrn: str,
        payload: json,
    ):
        try:
            sales_transaction = cls.objects.get(
                company=company,
                branch=branch,
                batch_id=batch_id,
                status=enums.TransactionStatusChoices.PENDING,
            )
        except cls.DoesNotExist:
            sales_transaction = None
        if sales_transaction is None:
            return {"status": False, "data": []}
        sales_transaction.card_rrn = rrn
        sales_transaction.updated_by = user
        sales_transaction.rrn_register_payload = json.dumps(payload)
        sales_transaction.save()
        return {"status": True, "data": sales_transaction}

    @classmethod
    def decline_order(
        cls,
        company: Company,
        branch: Branch,
        batch_id: str,
        action_type: str,
        user: User,
    ):
        sales_transaction = (
            cls.objects.filter(
                company=company,
                branch=branch,
                batch_id=batch_id,
            ).filter(
                models.Q(status=enums.TransactionStatusChoices.PENDING)
                | models.Q(status=enums.TransactionStatusChoices.SAVED)
            ).last()
        )
        if sales_transaction is None:
            return {"status": False, "data": []}
        sales_transaction.status = action_type
        sales_transaction.updated_by = user
        sales_transaction.save()
        return {"status": True, "data": sales_transaction}

    @classmethod
    @transaction.atomic
    def register(
        cls,
        company: Company,
        branch: Branch,
        sales: list,
        created_by: User,
        device: str,
        sales_tag: Optional[str] = None,
        customer: Optional[Customer] = None,
        discount_value: Optional[float] = 0.0,
        delivery_fee: Optional[float] = 0.0,
        save: Optional[bool] = False,
    ):
        """
        This method records a sales transaction and generates an invoice.
        """
        from helpers.reusable_functions import generate_unique_reference
        from invoicing.models import InvoiceItem

        # identify the applicable charges.
        constant_charges = ConstantVariable.objects.first()
        vat = branch.vat
        discount_value = math.floor(discount_value)
        delivery_fee = math.ceil(delivery_fee)
        if constant_charges is not None:
            sales_charge = float(constant_charges.sales_charge)
            sales_charge_cap = float(constant_charges.sales_charge_cap)
        else:
            sales_charge = 0.0
            sales_charge_cap = 0.0
        # Register the sales transaction.
        if save:
            status = enums.TransactionStatusChoices.SAVED
        else:
            status = enums.TransactionStatusChoices.PENDING
        sales_transaction = cls.objects.create(
            company=company,
            branch=branch,
            batch_id=generate_unique_reference(),
            sales_tag=sales_tag,
            customer=customer,
            status=status,
            discount_value=discount_value,
            delivery_fee=delivery_fee,
            created_by=created_by,
            device=device,
        )
        # Register the sales transaction item(s) and get the total value.
        transaction_items = SalesTransactionItem.register_items(
            company=company,
            branch=branch,
            sales_transaction=sales_transaction,
            sales_items=sales,
            created_by=created_by,
        )
        sub_total = transaction_items
        # generate the sales transaction invoice.
        items = [
            {
                "category": data.get("category").name,
                "item_description": data.get("item").name,
                "unit_price": data.get("amount"),
                "quantity": data.get("quantity"),
            }
            for data in sales
        ]
        if not save:
            invoice = InvoiceItem.generate_invoice(
                company=company,
                branch=branch,
                due_date=date.today(),
                created_by=created_by,
                customer=customer,
                tax_rate=vat,
                items=items,
                discount_value=discount_value,
                delivery_fee=delivery_fee,
                batch_id=sales_transaction.batch_id,
            )
        else:
            invoice = None
        # calculate the total cost.
        sub_total = float(sub_total)
        vat_amount = math.ceil((vat / 100) * float(sub_total))
        total_cost = (
            sum([float(sub_total), float(delivery_fee), float(vat_amount)]) - float(discount_value)
        )
        # calculate the applicable charges for CARD & TRANSFER only.
        applicable_charges = math.ceil(total_cost * sales_charge)
        if applicable_charges > sales_charge_cap:
            applicable_charges = sales_charge_cap
        # show applicable charges to customer when applicable.
        if branch.transfer_charges_to_customer:
            charges = applicable_charges
        else:
            charges = 0.0
        # Update the invoice record.
        if invoice is not None:
            invoice.amount += applicable_charges
            invoice.charges = applicable_charges
            invoice.save()
        # Update the sales transaction record.
        sales_transaction.invoice = invoice
        sales_transaction.vat_amount = vat_amount
        sales_transaction.total_sales_amount = sub_total
        sales_transaction.total_cost = total_cost + charges
        sales_transaction.charges = charges
        sales_transaction.applicable_charges = applicable_charges
        sales_transaction.save()
        return {
            "offline_identifier": sales_transaction.offline_identifier,
            "invoice_id": invoice.reference if invoice is not None else "",
            "batch_id": sales_transaction.batch_id,
            "status": sales_transaction.status,
            "sales_tag": sales_transaction.sales_tag,
            "sub_total": sub_total,
            "discount_value": discount_value,
            "delivery_fee": delivery_fee,
            "vat": vat,
            "charges": charges,
            "total": sales_transaction.total_cost,
            "items": items,
        }

    @classmethod
    @transaction.atomic
    def manage_saved_sales(
        cls,
        company: Company,
        branch: Branch,
        sales: list,
        batch_id: str,
        updated_by: User,
        customer: Optional[Customer] = None,
        discount_value: Optional[float] = 0.0,
        delivery_fee: Optional[float] = 0.0,
        save: Optional[bool] = False,
    ):
        """ """
        from invoicing.models import InvoiceItem

        sales_transaction = cls.objects.filter(
            company=company,
            branch=branch,
            batch_id=batch_id,
            status=enums.TransactionStatusChoices.SAVED,
        ).last()
        if sales_transaction is None:
            return None
        sales_transaction_items = SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction
        ).delete()
        # identify the applicable charges.
        constant_charges = ConstantVariable.objects.first()
        vat = branch.vat
        discount_value = math.floor(discount_value)
        delivery_fee = math.ceil(delivery_fee)
        if constant_charges is not None:
            sales_charge = float(constant_charges.sales_charge)
            sales_charge_cap = float(constant_charges.sales_charge_cap)
        else:
            sales_charge = 0.0
            sales_charge_cap = 0.0
        # Register the sales transaction item(s) and get the total value.
        transaction_items = SalesTransactionItem.register_items(
            company=company,
            branch=branch,
            sales_transaction=sales_transaction,
            sales_items=sales,
            created_by=updated_by,
        )
        sub_total = transaction_items
        items = [
            {
                "category": data.get("category").name,
                "item_description": data.get("item").name,
                "unit_price": data.get("amount"),
                "quantity": data.get("quantity"),
            }
            for data in sales
        ]
        invoice = None
        if not save:
            # Generate the sales transaction invoice.
            invoice = InvoiceItem.generate_invoice(
                company=company,
                branch=branch,
                due_date=date.today(),
                created_by=updated_by,
                customer=customer,
                tax_rate=vat,
                items=items,
                discount_value=discount_value,
                delivery_fee=delivery_fee,
                batch_id=sales_transaction.batch_id,
            )
        # Calculate the Branch vat amount and the total cost.
        sub_total = float(sub_total) - float(discount_value)
        vat_amount = (vat / 100) * float(sub_total)
        total_cost = sum([float(sub_total), float(delivery_fee), float(vat_amount)])
        charges = 0.0
        if branch.transfer_charges_to_customer:
            charges = math.ceil(total_cost * sales_charge)
            if charges > sales_charge_cap:
                charges = sales_charge_cap
        # Update the invoice record.
        if invoice is not None:
            invoice.amount += charges
            invoice.charges = charges
            invoice.save()
        # Update the sales transaction record.
        sales_transaction.invoice = invoice
        sales_transaction.vat_amount = vat_amount
        sales_transaction.total_sales_amount = sub_total
        sales_transaction.total_cost = total_cost
        sales_transaction.save()
        return {
            "offline_identifier": sales_transaction.offline_identifier,
            "invoice_id": invoice.reference if invoice is not None else "",
            "batch_id": sales_transaction.batch_id,
            "status": sales_transaction.status,
            "sales_tag": sales_transaction.sales_tag,
            "sub_total": sub_total,
            "discount_value": discount_value,
            "delivery_fee": delivery_fee,
            "vat": vat,
            "charges": charges,
            "total": total_cost,
            "items": items,
        }

    @classmethod
    @transaction.atomic
    def confirm_sales(
        cls,
        company: Company,
        branch: Branch,
        batch_id: str,
        means_of_payment: str,
        updated_by: User,
        customer: Optional[Customer] = None,
        split_method: Optional[dict] = None,
    ):
        """
        The following are the processes for sales confirmation.

        NOTE [LOGIC]:
        - CASH | OTHER_TRANSFER | OTHERS: it updates the status of the sales to 'SUCCESSFUL';
        sets payments to true; and updates the referenced invoice.

        - CARD: it updates the status of the sales to 'IN_PROGRESS' and awaits
        an event notification for the status of the 'CARD' payment from 'LibertyPay'.
        
        - TRANSFER: it updates the status of the sales to 'IN_PROGRESS' and awaits
        an event notification for the status of the 'TRANSFER' payment from 'CoreBanking (Wema)'.

        - SPLIT: all payment methods must indicate `SUCCESSFUL` for the actual sales transaction
        to be successful. The methods chosen adapts to the existing protocols.
        """
        from invoicing.models import Invoice

        sales_transaction = cls.objects.filter(
            company=company,
            branch=branch,
            batch_id=batch_id,
        ).last()
        if sales_transaction is None:
            return None
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        if customer is not None:
            sales_transaction.customer = customer
            if sales_transaction.invoice is not None:
                Invoice.confirm_payment(
                    reference=sales_transaction.invoice.reference,
                    datetime=TODAY,
                    customer=customer,
                )
        cash_book_type = (
            enums.CashBookChoices.CASH_IN_HAND
            if means_of_payment
            in [
                enums.MeansOfPaymentChoices.CASH,
                enums.MeansOfPaymentChoices.OTHER_TRANSFER,
                enums.MeansOfPaymentChoices.OTHERS,
            ]
            else enums.CashBookChoices.CASH_IN_BANK
        )
        if means_of_payment in [
            enums.MeansOfPaymentChoices.CASH,
            enums.MeansOfPaymentChoices.OTHER_TRANSFER,
            enums.MeansOfPaymentChoices.OTHERS,
        ]:
            # reset the applied charges.
            cost_of_sales = float(sales_transaction.total_cost) - float(sales_transaction.charges)
            # Update the sales transaction.
            sales_transaction.status = enums.TransactionStatusChoices.SUCCESSFUL
            sales_transaction.means_of_payment = means_of_payment
            sales_transaction.total_cost = cost_of_sales
            sales_transaction.amount_paid = cost_of_sales
            sales_transaction.charges = 0.0
            sales_transaction.paid = True
            sales_transaction.paid_at = TODAY
            sales_transaction.updated_by = updated_by
            sales_transaction.applicable_charges = 0.0
            # Register the sales transaction payment.
            CashBook.objects.create(
                company=company,
                branch=branch,
                sales_transaction=sales_transaction,
                transaction_type=DebitCreditEntry.CREDIT,
                amount=cost_of_sales,
                cash_book_type=cash_book_type,
                payment_channel=means_of_payment,
                status=enums.TransactionStatusChoices.SUCCESSFUL,
            )
            # Update the referenced invoice.
            if sales_transaction.invoice is not None:
                Invoice.confirm_payment(
                    reference=sales_transaction.invoice.reference,
                    datetime=TODAY,
                    is_paid=True,
                )
        if means_of_payment == enums.MeansOfPaymentChoices.CARD:
            if sales_transaction.status != enums.TransactionStatusChoices.SUCCESSFUL:
                sales_transaction.status = enums.TransactionStatusChoices.IN_PROGRESS
            sales_transaction.means_of_payment = means_of_payment
        if means_of_payment == enums.MeansOfPaymentChoices.TRANSFER:
            sales_transaction.status = enums.TransactionStatusChoices.IN_PROGRESS
            sales_transaction.means_of_payment = means_of_payment
        if means_of_payment == enums.MeansOfPaymentChoices.SPLIT:
            sales_transaction.means_of_payment = means_of_payment
            sales_transaction.status = enums.TransactionStatusChoices.IN_PROGRESS
            sales_transaction.updated_by = updated_by
            if not split_method.get("transfer"):
                # reset the applied charges.
                cost_of_sales = float(sales_transaction.total_cost) - float(sales_transaction.charges)
                # Update the sales transaction.
                sales_transaction.status = enums.TransactionStatusChoices.SUCCESSFUL
                sales_transaction.total_cost = cost_of_sales
                sales_transaction.amount_paid = cost_of_sales
                sales_transaction.charges = 0.0
                sales_transaction.paid = True
                sales_transaction.paid_at = TODAY
                sales_transaction.applicable_charges = 0.0
                # Register the sales transaction payment.
                CashBook.objects.create(
                    company=company,
                    branch=branch,
                    sales_transaction=sales_transaction,
                    transaction_type=DebitCreditEntry.CREDIT,
                    amount=cost_of_sales,
                    cash_book_type=cash_book_type,
                    payment_channel=means_of_payment,
                    status=enums.TransactionStatusChoices.SUCCESSFUL,
                )
            if split_method.get("cash"):
                amount = split_method.get("cash_amount")
                cls.objects.create(
                    company=company,
                    branch=branch,
                    customer=customer,
                    payment_reference=sales_transaction.batch_id,
                    means_of_payment = enums.MeansOfPaymentChoices.CASH,
                    status=enums.TransactionStatusChoices.SUCCESSFUL,
                    amount_paid=amount,
                    paid=True,
                    paid_at=TODAY,
                    created_by=updated_by,
                    updated_by=updated_by,
                    device=sales_transaction.device,
                )
            if split_method.get("others"):
                amount = split_method.get("others_amount")
                cls.objects.create(
                    company=company,
                    branch=branch,
                    customer=customer,
                    payment_reference=sales_transaction.batch_id,
                    means_of_payment = enums.MeansOfPaymentChoices.OTHERS,
                    status=enums.TransactionStatusChoices.SUCCESSFUL,
                    amount_paid=amount,
                    paid=True,
                    paid_at=TODAY,
                    created_by=updated_by,
                    updated_by=updated_by,
                    device=sales_transaction.device,
                )
            if split_method.get("transfer"):
                amount = split_method.get("transfer_amount")
                cls.objects.create(
                    company=company,
                    branch=branch,
                    customer=customer,
                    payment_reference=sales_transaction.batch_id,
                    means_of_payment = enums.MeansOfPaymentChoices.TRANSFER,
                    status=enums.TransactionStatusChoices.IN_PROGRESS,
                    created_by=updated_by,
                    updated_by=updated_by,
                    device=sales_transaction.device,
                )
            if split_method.get("other_transfer"):
                amount = split_method.get("other_transfer_amount")
                cls.objects.create(
                    company=company,
                    branch=branch,
                    customer=customer,
                    payment_reference=sales_transaction.batch_id,
                    means_of_payment = enums.MeansOfPaymentChoices.OTHER_TRANSFER,
                    status=enums.TransactionStatusChoices.SUCCESSFUL,
                    amount_paid=amount,
                    paid=True,
                    paid_at=TODAY,
                    created_by=updated_by,
                    updated_by=updated_by,
                    device=sales_transaction.device,
                )
        sales_transaction.save()
        return sales_transaction

    @classmethod
    def get_branches_comparison(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_sales = cls.objects.filter(
            company=company,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            batch_id__isnull=False,
        )
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=company_sales,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            global_transactions = custom_queryset.aggregate(
                amount=models.Sum("total_sales_amount")
            )["amount"] or 0.0
            comparison_result = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    branch_name=models.F("branch__name"),
                    sales_value=models.Sum("total_sales_amount"),
                )
                .values("month", "branch_name", "sales_value")
                .order_by("month")
            )
            return {
                "status": True,
                "details": comparison_result,
                "global_transactions": global_transactions,
            }
        return custom_queryset

    @classmethod
    def get_branch_total_sales(
        cls,
        company: Company,
        branch: Branch,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        TODAY = date.today()

        branch_sales = cls.objects.filter(
            company=company,
            branch=branch,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            batch_id__isnull=False,
        )
        # Fetch result type by date range.
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=branch_sales,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            branch_total_sales = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    branch_name=models.F("branch__name"),
                    sales_value=models.Sum("total_sales_amount"),
                )
                .values("month", "branch_name", "sales_value")
            )
            return {"status": True, "details": branch_total_sales}
        return custom_queryset

    @classmethod
    def get_branch_average_sales(
        cls,
        company: Company,
        branch: Branch,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        branch_sales = cls.objects.filter(
            company=company,
            branch=branch,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
        )
        # Fetch result type by date range.
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=branch_sales,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            branch_average_sales = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    branch_name=models.F("branch__name"),
                    average_sales=models.Avg("total_sales_amount"),
                )
                .values("month", "branch_name", "average_sales")
            )
            return {"status": True, "details": branch_average_sales}
        return custom_queryset

    @classmethod
    @transaction.atomic
    def register_offline(
        cls,
        company: Company,
        branch: Branch,
        device: str,
        offline_identifier: str,
        means_of_payment: str,
        date_and_time: datetime,
        sales: list,
        created_by: User,
        customer: Optional[Customer] = None,
        discount_value: Optional[float] = 0.0,
        delivery_fee: Optional[float] = 0.0,
        transaction_detail: Optional[dict] = None,
        sales_tag: Optional[str] = None,
        save: Optional[bool] = False,
    ):
        """
        This method records a sales transaction; depletes the stock; and generates an invoice.
        """
        from invoicing.models import Invoice, InvoiceItem

        items = [
            {
                "category": data.get("category").name,
                "item_description": data.get("item").name,
                "unit_price": data.get("amount"),
                "quantity": data.get("quantity"),
            }
            for data in sales
        ]
        # Check offline identifier.
        sales_transaction = cls.objects.filter(
            offline_identifier=offline_identifier
        ).first()
        if sales_transaction is not None:
             return {
                "offline_identifier": sales_transaction.offline_identifier,
                "invoice_id": sales_transaction.invoice.reference,
                "batch_id": sales_transaction.batch_id,
                "status": sales_transaction.status,
                "sales_tag": sales_transaction.sales_tag,
                "sub_total": sales_transaction.total_sales_amount,
                "discount_value": sales_transaction.discount_value,
                "delivery_fee": sales_transaction.delivery_fee,
                "vat": sales_transaction.vat_amount,
                "charges": sales_transaction.charges,
                "total": sales_transaction.total_cost,
                "items": items,
                "means_of_payment": sales_transaction.means_of_payment,
                "transaction_detail": transaction_detail,
            }
        # Register the sales transaction.
        sales_transaction = cls.objects.create(
            offline_date=date_and_time,
            company=company,
            branch=branch,
            customer=customer,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            discount_value=discount_value,
            delivery_fee=delivery_fee,
            created_by=created_by,
            device=device,
            offline_identifier=offline_identifier,
            means_of_payment=means_of_payment,
        )
        # Register the sales transaction item(s) and get the total value.
        transaction_items = SalesTransactionItem.register_items(
            company=company,
            branch=branch,
            sales_transaction=sales_transaction,
            sales_items=sales,
            created_by=created_by,
            is_offline=True,
        )
        sub_total = transaction_items
        vat = branch.vat
        # Generate the sales transaction invoice.
        invoice = InvoiceItem.generate_invoice(
            company=company,
            branch=branch,
            due_date=date.today(),
            created_by=created_by,
            customer=customer,
            tax_rate=vat,
            items=items,
            discount_value=discount_value,
            delivery_fee=delivery_fee,
            batch_id=sales_transaction.batch_id,
        )
        # Calculate the Branch vat amount and the total cost.
        sub_total = float(sub_total) - float(discount_value)
        vat_amount = (vat / 100) * float(sub_total)
        total_cost = sum([float(sub_total), float(delivery_fee), float(vat_amount)])
        # Update the sales transaction record.
        sales_transaction.invoice = invoice
        sales_transaction.vat_amount = vat_amount
        sales_transaction.total_sales_amount = sub_total
        sales_transaction.total_cost = total_cost
        sales_transaction.amount_paid = total_cost
        sales_transaction.paid = True
        sales_transaction.paid_at = date_and_time
        sales_transaction.save()

        cash_book_type = (
            enums.CashBookChoices.CASH_IN_HAND
            if means_of_payment
            in [
                enums.MeansOfPaymentChoices.CASH,
                enums.MeansOfPaymentChoices.OTHER_TRANSFER,
                enums.MeansOfPaymentChoices.OTHERS,
            ]
            else enums.CashBookChoices.CASH_IN_BANK
        )
        # Register the sales transaction payment.
        CashBook.objects.create(
            company=company,
            branch=branch,
            sales_transaction=sales_transaction,
            transaction_type=DebitCreditEntry.CREDIT,
            amount=sales_transaction.total_cost,
            cash_book_type=cash_book_type,
            payment_channel=means_of_payment,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
        )
        # Update the referenced invoice.
        Invoice.confirm_payment(
            reference=sales_transaction.invoice.reference,
            datetime=date_and_time,
            is_paid=True,
        )

        return {
            "offline_identifier": offline_identifier,
            "invoice_id": invoice.reference,
            "batch_id": sales_transaction.batch_id,
            "status": sales_transaction.status,
            "sales_tag": sales_transaction.sales_tag,
            "sub_total": sub_total,
            "discount_value": discount_value,
            "delivery_fee": delivery_fee,
            "vat": vat,
            "charges": sales_transaction.charges,
            "total": total_cost,
            "items": items,
            "means_of_payment": sales_transaction.means_of_payment,
            "transaction_detail": transaction_detail,
        }

    @classmethod
    def get_top_four_branches(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_branches = Branch.objects.filter(company=company)
        sales_transactions = cls.objects.filter(
            company=company,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            batch_id__isnull=False,
        )
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=sales_transactions,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            company_sales = custom_queryset.aggregate(
                amount=models.Sum("total_sales_amount")
            )["amount"] or 0.0
            top_four_branches = (
                custom_queryset.filter(branch__in=company_branches)
                .values("branch")
                .annotate(
                    branch_name=models.F("branch__name"),
                    amount=models.Sum("total_sales_amount"),
                    percentage=(
                        (models.Sum("total_sales_amount") / Decimal(company_sales)) * 100
                    ),
                )
                .order_by("-amount")[:4]
                .values("branch", "branch_name", "amount", "percentage")
            )
            return {"status": True, "details": top_four_branches}
        return custom_queryset

    @classmethod
    def get_top_four_customers(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_customers = Customer.objects.filter(company=company)
        sales_transactions = cls.objects.filter(
            company=company,
            status=enums.TransactionStatusChoices.SUCCESSFUL,
            batch_id__isnull=False,
        )
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=sales_transactions,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            top_four_customers = (
                custom_queryset.filter(customer__in=company_customers)
                .values("customer")
                .annotate(
                    customer_name=models.F("customer__name"),
                    orders=models.Count("batch_id"),
                    revenue=models.Sum("total_sales_amount"),
                    last_purchased=models.Max("created_at"),
                )
                .order_by("-revenue")[:4]
                .values("customer", "customer_name", "orders", "revenue", "last_purchased")
            )
            return {"status": True, "details": top_four_customers}
        return custom_queryset


class SalesTransactionItem(BaseModel):
    """
    NOTE [ATTRIBUTE]:
    - amount is the unit price of the goods.
    - total value is derived by multiplying the quantity against the amount (unit price).
    NOTE [LOGIC]:
    - swapped items will not be included in the total sales amount.
    - refunded items will not be included in the total sales amount.
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    sales_transaction = models.ForeignKey(
        SalesTransaction,
        on_delete=models.CASCADE,
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        editable=False,
    )
    item = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        editable=False,
    )
    quantity_before = models.IntegerField(default=0)
    quantity = models.PositiveIntegerField()
    quantity_after = models.IntegerField(default=0)
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    total_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    gross_profit = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )
    swapped = models.BooleanField(default=False)
    refunded = models.BooleanField(default=False)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="confirmed_payment",
    )

    def __str__(self) -> str:
        return self.sales_transaction.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES TRANSACTION ITEM"
        verbose_name_plural = "SALES TRANSACTION ITEMS"

    @classmethod
    @transaction.atomic
    def register_items(
        cls,
        company: Company,
        branch: Branch,
        sales_transaction: SalesTransaction,
        sales_items: list,
        created_by: User,
        is_offline: Optional[bool] = False,
    ):
        """
        This method registers the item(s) for a sales transaction.
        Returns:
            total_sales_amount (float): The total sales amount for the transaction.
        """
        from stock_inventory.utils import deplete_branch_quantity_available

        total_sales_amount = 0
        for data in sales_items:
            category = data.get("category")
            item = data.get("item")
            quantity = data.get("quantity")
            amount = data.get("amount")
            if (is_offline == False and branch.sell_without_inventory == False):
                check_product_availability = deplete_branch_quantity_available(
                    company=company,
                    branch=branch,
                    category=category,
                    item=item,
                    quantity=quantity,
                    check=True,
                )
                if not isinstance(check_product_availability, bool):
                    raise ValidationError(
                        {"message": f"insufficient quantity for {item.name}."}
                    )
            transaction_item = cls.objects.create(
                company=company,
                branch=branch,
                sales_transaction=sales_transaction,
                category=category,
                item=item,
                quantity=quantity,
                amount=amount,
                total_value=quantity * amount,
                gross_profit=float(amount) - float(item.product_price),
                created_by=created_by,
            )
            total_sales_amount += transaction_item.total_value
            if is_offline:
                deplete_response = deplete_branch_quantity_available(
                    company=company,
                    branch=branch,
                    category=category,
                    item=item,
                    quantity=quantity,
                    on_hold=False,
                )
                transaction_item.quantity_before = deplete_response.get("quantity_before")
                transaction_item.quantity_after = deplete_response.get("quantity_after")
                transaction_item.save()
                if not deplete_response.get("status"):
                    # register branch's backorder logs.
                    backorder = SalesTransactionBackorder.objects.create(
                        company=company,
                        branch=branch,
                        sales_transaction=sales_transaction,
                        category=category,
                        item=item,
                        quantity=quantity,
                        amount=amount,
                        total_value=quantity * amount,
                        created_by=created_by,
                    )
        return total_sales_amount

    @classmethod
    def categories_above_average_comparison(cls, company: Company):
        category_sales = (
            cls.objects.filter(
                sales_transaction__company=company,
                swapped=False,
                refunded=False,
            )
            .values("category__name")
            .annotate(total_sales=models.Sum("total_value"))
        )
        overall_average = cls.objects.aggregate(average_sales=models.Avg("total_value"))
        overall_average = round(overall_average.get("average_sales"), 2)
        total_sales_above_average = sum(
            category["total_sales"]
            for category in category_sales
            if category["total_sales"] > overall_average
        )
        categories_above_average = []
        for category in category_sales:
            if category["total_sales"] > overall_average:
                percentage = (category["total_sales"] / total_sales_above_average) * 100
                categories_above_average.append(
                    {"category": category["category__name"], "percentage": percentage}
                )
        return categories_above_average

    @classmethod
    def categories_below_average_comparison(cls, company: Company):
        category_sales = (
            cls.objects.filter(
                sales_transaction__company=company,
                swapped=False,
                refunded=False,
            )
            .values("category__name")
            .annotate(total_sales=models.Sum("total_value"))
        )
        overall_average = cls.objects.aggregate(average_sales=models.Avg("total_value"))
        overall_average = round(overall_average.get("average_sales"))
        total_sales_above_average = sum(
            category["total_sales"]
            for category in category_sales
            if category["total_sales"] < overall_average
        )
        categories_above_average = []
        for category in category_sales:
            if category["total_sales"] < overall_average:
                percentage = (category["total_sales"] / total_sales_above_average) * 100
                categories_above_average.append(
                    {"category": category["category__name"], "percentage": percentage}
                )
        return categories_above_average

    @classmethod
    def get_branch_analytics(
        cls,
        company: Company,
        branch: Branch,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        from account.models import AccountSystem, Wallet
        from account.enums import AccountType
        from invoicing.models import Invoice

        """
        NOTE [RESPONSE]:
        - total_sales: today's successful transaction(s).
        - cash_in_bank: current float balance (TRANSFER).
        - cash_in_hand: today's sales (CASH).
        - average_sales: filtered average successful transaction(s) value.
        - gross_profit: filtered gross profit successful transaction(s) value.
        - post_dated_invoice: filtered postdated transaction(s) value.
        - total_products_sold: filtered product(s) sold count successful transaction(s) value.
        """

        branch_sales = cls.objects.filter(
            sales_transaction__company=company,
            sales_transaction__branch=branch,
            sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL,
            swapped=False,
            refunded=False,
        ).order_by("item__id")
        branch_sales_value = (
            branch_sales.aggregate(total_amount=models.Sum("total_value"))[
                "total_amount"
            ]
            or 0.0
        )
        branch_cash_book = CashBook.objects.filter(company=company, branch=branch)
        branch_account = AccountSystem.objects.filter(
            user=company.user,
            company=company,
            branch=branch,
            account_type=AccountType.SALES,
        ).last()
        branch_wallet = Wallet.objects.filter(
            user=company.user,
            account=branch_account,
            wallet_type=AccountType.SALES,
        ).last()
        branch_cash_in_bank = (
            round(branch_wallet.balance, 2) if branch_wallet is not None else 0.0
        )
        branch_cash_in_hand = (
            branch_cash_book.filter(cash_book_type=enums.CashBookChoices.CASH_IN_HAND).aggregate(
                total_amount=models.Sum("amount")
            )["total_amount"]
            or 0.0
        )
        # Branch today's sales.
        branch_today_sales = (
            branch_sales.filter(created_at__date=date.today()).aggregate(
                total_amount=models.Sum("total_value")
            )["total_amount"]
            or 0.0
        )
        # Fetch result type by date range.
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=branch_sales,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not isinstance(custom_queryset, QuerySet):
            return custom_queryset
        filtered_values = custom_queryset.aggregate(
            average_sales=models.Avg("total_value"),
            gross_profit=models.Sum("gross_profit"),
            total_products=models.Count("item", distinct=True),
        )
        post_dated_invoice = (
            Invoice.objects.filter(postdated=True).aggregate(
                total_amount=models.Sum("amount")
            )["total_amount"]
            or 0.0
        )
        return {
            "status": True,
            "details": {
                "branch_total_sales": branch_sales_value,
                "branch_cash_in_bank": branch_cash_in_bank,
                "branch_cash_in_hand": branch_cash_in_hand,
                "branch_today_sales": branch_today_sales,
                "branch_average_sales": round(
                    filtered_values.get("average_sales") or 0.0, 2
                ),
                "branch_gross_profit": filtered_values.get("gross_profit") or 0.0,
                "branch_post_dated_invoice": post_dated_invoice,
                "branch_total_products_sold": filtered_values.get("total_products")
                or 0,
            },
        }

    @classmethod
    def profit_tracking(
        cls,
        company: Company,
        result_type: str,
        branch: Optional[Branch] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        sales = cls.objects.filter(
            sales_transaction__company=company,
            sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL,
            swapped=False,
            refunded=False,
        )
        if branch is not None:
            sales = sales.filter(sales_transaction__branch=branch)
        # Fetch result type by date range.
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=sales,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if not isinstance(custom_queryset, QuerySet):
            return custom_queryset
        profit_details = (
            custom_queryset.annotate(month=ExtractMonth("created_at"))
            .values("month")
            .annotate(gross_profit=models.Sum("gross_profit"))
            .values("month", "gross_profit")
        )
        if branch is not None:
            profit_details = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    branch_name=models.F("sales_transaction__branch__name"),
                    gross_profit=models.Sum("gross_profit"),
                )
                .values("month", "branch_name", "gross_profit")
            )
        return {"status": True, "details": profit_details}

    @classmethod
    def get_branch_products_sold(
        cls,
        company: Company,
        branch: Branch,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        branch_sales = cls.objects.filter(
            sales_transaction__company=company,
            sales_transaction__branch=branch,
            sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL,
        )
        # Fetch result type by date range.
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=branch_sales,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            branch_products_sold = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    branch_name=models.F("sales_transaction__branch__name"),
                    total_products_sold=models.Count("item__id", distinct=True),
                )
                .values("month", "branch_name", "total_products_sold")
            )
            return {"status": True, "details": branch_products_sold}
        return custom_queryset

    @classmethod
    def top_ten_products(
        cls,
        company: Company,
        branch: Optional[Branch] = None,
        result_type: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        sales = cls.objects.filter(
            sales_transaction__company=company,
            sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL,
        )
        if branch is not None:
            sales = sales.filter(sales_transaction__branch=branch)
            branch_sales_count = sales.count()
            top_products = (
                sales.values("item")
                .annotate(
                    product=models.F("item__name"),
                    count=models.Count("item"),
                    amount=models.Sum("total_value"),
                )
                .order_by("-count")[:10]
            )
            top_ten_products = [
                {
                    "product": item.get("product"),
                    "percentage": round((item.get("count") / branch_sales_count) * 100, 2),
                    "amount": item.get("amount"),
                }
                for item in top_products
            ]
        if result_type is not None:
            # Fetch result type by date range.
            custom_queryset = QuerysetCustomFilter.date_range_filter(
                queryset=sales,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(custom_queryset, QuerySet):
                return custom_queryset
            top_ten_products = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    count=models.Count("item"),
                    product=models.F("item__name"),
                    product_id=models.F("item__id"),
                    branch=models.F("sales_transaction__branch__name"),
                    total_quantity=models.Sum("quantity"),
                    price=models.F("amount"),
                    sales_value=models.Sum("total_value"),
                    profit=models.Sum("gross_profit"),
                )
                .order_by("-count", "-total_quantity")[:10]
                .values(
                    "count",
                    "product",
                    "product_id",
                    "branch",
                    "total_quantity",
                    "price",
                    "sales_value",
                    "profit",
                )
            )
        return top_ten_products


class ReturnRefund(BaseModel):
    """
    NOTE [ATTRIBUTE]:
    - additional info holds extra information about the swap or refund that
    is not captured by the available reasons.
    NOTE [LOGIC]:
    - only sales transaction completed by 'CASH' can be paid out immediately.
    - customer might not be associated to the initial sales transaction.
    - customer record is created and taken only for record purpose(s).
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    invoice = models.ForeignKey("invoicing.Invoice", on_delete=models.CASCADE)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    choice = models.CharField(
        max_length=25,
        choices=enums.ReturnRefundTypes.choices,
    )
    restock = models.CharField(
        max_length=25,
        choices=enums.RestockChoices.choices,
    )
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.00,
    )
    status = models.CharField(
        max_length=25,
        choices=enums.TransactionStatusChoices.choices,
        default=enums.TransactionStatusChoices.PENDING,
    )
    reason = models.CharField(
        max_length=255,
        choices=enums.ReturnChoices.choices,
    )
    payment_method = models.CharField(
        max_length=25,
        choices=enums.MeansOfPaymentChoices.choices,
        null=True,
        blank=True,
    )
    additional_info = models.TextField(null=True, blank=True)
    beneficiary_bank_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    beneficiary_account_number = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    beneficiary_account_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    decline_approval_reason = models.TextField(
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="attendant",
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="approver",
        null=True,
        blank=True,
    )

    def __str__(self) -> str:
        return self.invoice.reference

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES RETURN / REFUND"
        verbose_name_plural = "SALES RETURN / REFUND"

    @classmethod
    def approve_decline_refund(
        cls,
        approved_by: User,
        company: Company,
        branch: Branch,
        invoice_reference: str,
        status: str,
        decline_approval_reason: Optional[str] = None,
    ):
        refund_request = cls.objects.filter(
            company=company,
            branch=branch,
            invoice__reference=invoice_reference,
            status=enums.TransactionStatusChoices.PENDING,
        ).last()
        if refund_request is not None:
            refund_request.approved_by = approved_by
            refund_request.status = status
            refund_request.decline_approval_reason = decline_approval_reason
            refund_request.save()
            return True
        return None

    @classmethod
    def get_branches_comparison(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        company_refund = cls.objects.filter(company=company)
        # Fetch result type by date range.
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=company_refund,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            comparison_result = (
                custom_queryset.annotate(month=ExtractMonth("created_at"))
                .values("month")
                .annotate(
                    branch_name=models.F("branch__name"),
                    refund_amount=models.Sum("amount"),
                )
                .values("month", "branch_name", "refund_amount")
                .order_by("refund_amount")
            )
            return {"status": True, "details": comparison_result}
        return custom_queryset

    @classmethod
    def get_branch_top_products(cls, company: Company, branch: Branch):
        refund_ids = cls.objects.filter(company=company, branch=branch).values_list(
            "id"
        )
        items = ReturnRefundItem.get_top_refunds_item(return_refund_ids=refund_ids)
        return items


class ReturnRefundItem(BaseModel):
    from invoicing.models import Invoice

    """
    NOTE [ATTRIBUTE]:
    - unit price is the cost of goods sold or bought for a particular transaction.
    - amount is derived by multiplying the quantity against the cost of goods (unit price).
    """
    return_refund = models.ForeignKey(ReturnRefund, on_delete=models.CASCADE)
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField()
    unit_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )

    def __str__(self) -> str:
        return self.return_refund.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES RETURN/REFUND ITEM"
        verbose_name_plural = "SALES RETURN/REFUND ITEMS"

    @classmethod
    def get_top_refunds_item(cls, return_refund_ids: list):
        branch_refunds_item = cls.objects.filter(
            return_refund__in=return_refund_ids
        ).order_by("item__id")
        branch_top_refunds = (
            branch_refunds_item.values("item")
            .annotate(
                item_name=models.F("item__name"),
                count=models.Count("item"),
                amount=models.Sum("amount"),
            )
            .order_by("-count")[:10]
        )
        return branch_top_refunds

    @classmethod
    def refund_items(
        cls,
        company: Company,
        branch: Branch,
        invoice: Invoice,
        customer: Customer,
        choice: str,
        restock: str,
        reason: str,
        items: list,
        created_by: User,
        payment_method: Optional[str] = None,
        additional_info: Optional[str] = None,
        beneficiary_bank_name: Optional[str] = None,
        beneficiary_account_number: Optional[str] = None,
        beneficiary_account_name: Optional[str] = None,
    ):
        from stock_inventory.helper.enums import (
            StockHistoryChoices,
            StockHistoryStatusChoices,
        )
        from stock_inventory.utils import register_stock_history

        """ """

        refunded = ReturnRefund.objects.filter(invoice=invoice).last()
        if refunded:
            return False
        refund_record = ReturnRefund.objects.create(
            company=company,
            branch=branch,
            invoice=invoice,
            customer=customer,
            choice=choice,
            restock=restock,
            reason=reason,
            additional_info=additional_info,
            payment_method=payment_method,
            beneficiary_bank_name=beneficiary_bank_name,
            beneficiary_account_number=beneficiary_account_number,
            beneficiary_account_name=beneficiary_account_name,
            created_by=created_by,
        )
        total_refund_value = 0
        item_ids = []

        for item in items:
            refund_record_item = cls.objects.create(
                return_refund=refund_record,
                item=item.get("item"),
                quantity=item.get("quantity"),
                unit_price=item.get("unit_price"),
                amount=item.get("quantity") * item.get("unit_price"),
            )
            total_refund_value += refund_record_item.amount
            item_ids.append(item.get("item"))

            if restock == enums.RestockChoices.YES:
                pass

            if restock == enums.RestockChoices.NO:
                pass

        refund_record.amount = total_refund_value
        refund_record.save()

        sales_transaction = SalesTransaction.objects.filter(invoice=invoice).first()
        if sales_transaction is None:
            return None
        sales_transaction_items = SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction, swapped=False, refunded=False
        )
        sales_transaction_items.filter(item__in=item_ids).update(refunded=True)
        refresh_sales_transaction_items = SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction, swapped=False, refunded=False
        )
        sales_items_value = (
            refresh_sales_transaction_items.aggregate(amount=models.Sum("total_value"))[
                "amount"
            ]
            or 0.0
        )
        sales_transaction.total_sales_amount = sales_items_value
        sales_transaction.save()
        return {
            "sales_value": sales_transaction.total_sales_amount,
            "total_cost": sales_transaction.total_cost,
            "is_paid": sales_transaction.paid,
            "amount_paid": sales_transaction.amount_paid,
        }

    @classmethod
    def swap_items(
        cls,
        company: Company,
        branch: Branch,
        customer: Customer,
        invoice: Invoice,
        choice: str,
        reason: str,
        restock: str,
        old_items: list,
        new_items: list,
        created_by: User,
        additional_info: Optional[str] = None,
    ):
        from stock_inventory.helper.enums import (
            StockHistoryChoices,
            StockHistoryStatusChoices,
        )
        from stock_inventory.utils import register_stock_history

        """
        NOTE [BUSINESS LOGIC]:
        - create the swap record.
        - retrieve the sales transaction.
        - fetch the related sales transaction item(s) and initiate a swap.
        - add new items to the sales transaction.
        - update the sales transaction 'total sales amount'.
        - create the stock history.
        """
        swap_record = ReturnRefund.objects.create(
            company=company,
            branch=branch,
            invoice=invoice,
            customer=customer,
            choice=choice,
            restock=restock,
            reason=reason,
            additional_info=additional_info,
            created_by=created_by,
        )
        total_swap_value = 0
        item_ids = []

        for item in old_items:
            swap_record_item = cls.objects.create(
                return_refund=swap_record,
                item=item.get("item"),
                quantity=item.get("quantity"),
                unit_price=item.get("unit_price"),
                amount=item.get("quantity") * item.get("unit_price"),
            )
            total_swap_value += swap_record_item.amount
            item_ids.append(item.get("item"))

        swap_record.amount = total_swap_value
        swap_record.save()

        sales_transaction = SalesTransaction.objects.filter(invoice=invoice).first()
        if sales_transaction is None:
            return None
        sales_transaction_items = SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction, swapped=False, refunded=False
        )
        sales_transaction_items.filter(item__in=item_ids).update(swapped=True)
        SalesTransactionItem.register_items(
            company=company,
            branch=branch,
            sales_transaction=sales_transaction,
            sales_items=new_items,
        )
        refresh_sales_transaction_items = SalesTransactionItem.objects.filter(
            sales_transaction=sales_transaction, swapped=False, refunded=False
        )
        sales_items_value = (
            refresh_sales_transaction_items.aggregate(amount=models.Sum("total_value"))[
                "amount"
            ]
            or 0.0
        )
        sales_transaction.total_sales_amount = sales_items_value
        sales_transaction.save()
        return {
            "sales_value": sales_transaction.total_sales_amount,
            "total_cost": sales_transaction.total_cost,
            "is_paid": sales_transaction.paid,
            "amount_paid": sales_transaction.amount_paid,
        }


class POSDevice(BaseModel, DeleteHandler):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    serial = models.CharField(
        max_length=255,
        unique=True,
        null=True,
        blank=True,
    )
    terminal_id = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)

    def __str__(self) -> str:
        return self.terminal_id

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "POS DEVICES"
        verbose_name_plural = "POS DEVICES"


class CardTransaction(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    rrn = models.CharField(max_length=255, unique=True)
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    status = models.BooleanField(default=False)
    is_settled = models.BooleanField(default=False)
    transaction_status = models.CharField(
        max_length=1,
        null=True,
        blank=True,
    )
    payload = models.TextField()

    def __str__(self) -> str:
        return self.rrn

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CARD TRANSACTION"
        verbose_name_plural = "CARD TRANSACTIONS"

    @classmethod
    def register(cls, data):
        from helpers.emqx_mqtt import EMQXHandler
        from sales_app.tasks import update_sales_transaction

        rrn = data.get("rrn")
        status = data.get("status")
        transaction_status = data.get("transaction_status")
        if rrn:
            duplicate_transaction = cls.objects.filter(rrn=rrn).last()
            if duplicate_transaction is not None:
                return {"message": "duplicate card transaction."}
            else:
                sales_transaction = SalesTransaction.objects.filter(
                    card_rrn=rrn
                ).last()
                transaction_callback = cls.objects.create(
                    company=sales_transaction.company,
                    branch=sales_transaction.branch,
                    rrn=rrn,
                    amount=data.get("amount"),
                    status=status,
                    payload=json.dumps(data),
                    transaction_status=data.get("transaction_status"),
                )
                if (
                    (status != None and status == True)
                    and
                    (transaction_status != None and transaction_status == "S")
                ):
                    # # real-time notification handler.
                    # handler = EMQXHandler()
                    # handler.connect()
                    # message = {
                    #     "subject": "MONEY IN",
                    #     "body": {
                    #         "wallet_name": f"{sales_transaction.branch.__str__()}",
                    #         "payer_name": "",
                    #         "date": f"{str(transaction_callback.created_at)}",
                    #         "transaction_type": "CREDIT",
                    #         "amount": f"{float(transaction_callback.amount)}",
                    #         "balance_before": "0.0",
                    #         "balance_after": "0.0",
                    #         "payment_channel": "CARD",
                    #         "reference": rrn,
                    #         "narration": "card payment",
                    #         "status": "SUCCESSFUL",
                    #     }
                    # }
                    # handler.publish(
                    #     topic=f"branch/alert/{sales_transaction.branch.id}",
                    #     message=json.dumps(message),
                    # )
                    update_sales_transaction.delay(
                        amount=float(transaction_callback.amount),
                        inflow_id=transaction_callback.id,
                        rrn=rrn,
                    )
                else:
                    if sales_transaction is not None:
                        sales_transaction.status = enums.TransactionStatusChoices.FAILED
                        sales_transaction.save()
                return {"message": "event notification received."}
        return {"message": "rrn was not supplied."}


class APILog(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="sales_logger"
    )
    method = models.CharField(max_length=10, choices=RequestMethodType.choices)
    path = models.CharField(max_length=255)
    sales_batch_id = models.CharField(max_length=255, null=True, blank=True)
    request_headers = models.TextField(null=True, blank=True)
    request_body = models.TextField(null=True, blank=True)
    response_body = models.TextField(null=True, blank=True)
    items_depletion_history = models.TextField(null=True, blank=True)
    status_code = models.CharField(max_length=3, null=True, blank=True)

    def __str__(self):
        return f"{self.sales_batch_id} - {self.status_code}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "API LOG"
        verbose_name_plural = "API LOGS"


class SalesTransactionBackorder(BaseModel):
    """
    NOTE [ATTRIBUTE]:
    - amount is the unit price of the goods.
    - total value is derived by multiplying the quantity against the amount (unit price).
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    sales_transaction = models.ForeignKey(
        SalesTransaction,
        on_delete=models.CASCADE,
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        editable=False,
    )
    item = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        editable=False,
    )
    quantity = models.PositiveIntegerField()
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    total_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )

    def __str__(self) -> str:
        return self.sales_transaction.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES TRANSACTION BACKORDER"
        verbose_name_plural = "SALES TRANSACTION BACKORDERS"


class OfflineVirtualAccount(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    account_name = models.CharField(max_length=255)
    account_number = models.CharField(max_length=10, unique=True)
    unique_code = models.CharField(
        max_length=8,
        unique=True,
        help_text="offline ussd unique code.",
    )
    confirmation_code = models.CharField(
        max_length=8,
        unique=True,
        help_text="offline ussd confirmation code.",
    )
    bank_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.account_name} - {self.account_number}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "OFFLINE VIRTUAL ACCOUNTS"
        verbose_name_plural = "OFFLINE VIRTUAL ACCOUNTS"


class WalletWithdrawal(BaseModel):
    WITHDRAWAL_CHOICES = (
        ("MAIN", "MAIN"),
        ("EXTERNAL", "EXTERNAL")
    )

    refunded = models.BooleanField(default=False)
    status = models.CharField(
        max_length=25,
        choices=enums.TransactionStatusChoices.choices,
        editable=False,
    )
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0), validate_amount],
        editable=False,
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    account_name = models.CharField(max_length=255)
    account_number = models.CharField(max_length=10)
    bank_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=8)
    withdrawal_type = models.CharField(max_length=8, choices=WITHDRAWAL_CHOICES)
    narration = models.CharField(max_length=255)
    is_saved = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "WALLET WITHDRAWAL"
        verbose_name_plural = "WALLET WITHDRAWALS"
