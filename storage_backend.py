import os
import uuid
from dataclasses import dataclass
from storages.backends.s3boto3 import S3Boto3Storage


class MediaStorage(S3Boto3Storage):
    file_overwrite = False


@dataclass
class StorageManager:
    data_storage = MediaStorage()

    def upload_file_to_storage(self, file):
        # generate unique file name
        # base_filename, file_extension = os.path.splitext(file.name)
        # generated_unique_file_name = f"{base_filename}_{uuid.uuid4().hex}{file_extension}"
        generated_unique_file_name = file.name
        dt = self.data_storage
        dt.save(generated_unique_file_name, file)
        return dt.url(generated_unique_file_name)  # return url

    def upload_using_file_path(self, file_path: str) -> str:
        """
        Uploads a file to the configured storage system.
        Args:
            file_path (str): The path to the file to be uploaded.
        Returns:
            str: The URL of the uploaded file.
        Raises:
            Any: Any exceptions raised during the file upload process.
        """
        # Generate a unique file name
        base_filename, file_extension = os.path.splitext(os.path.basename(file_path))
        generated_unique_file_name = (
            f"{base_filename}_{uuid.uuid4().hex}{file_extension}"
        )
        # Open the file and upload it to the storage system
        with open(file_path, "rb") as file:
            dt = self.data_storage
            dt.save(generated_unique_file_name, file)
        # Return the URL of the uploaded file
        return dt.url(generated_unique_file_name)

    def upload_qr_code_to_storage(self, file, file_name):
        dt = self.data_storage
        dt.save(file_name, file)
        return dt.url(file_name)  # return url
