import csv
import io
import random
import string
import uuid
from datetime import date, datetime, timedelta

import pandas as pd
from django.conf import settings
from django.db import models
from django.utils.translation import gettext as _

from core.models import BaseModel, SalesAgents, User
from requisition.helpers.enums import UserRole, UserStatus
from requisition.models import Company, TeamMember
from subscription_and_invoicing import models as subscription
from .enums import (
    Deal_status,
    Engagement_status,
    Lead_onboarding_status,
    Lead_source,
    Lead_status,
    Priority_Status,
    Task_Status,
)


def generate_random_referral_code(length=6):
    return "".join(random.choices(string.ascii_uppercase + string.digits, k=length))


def read_csv_file_to_dict(file_name):
    with open(file_name, mode='r') as csv_file:
        dict_csv_reader = csv.DictReader(csv_file)
        list_of_json = list(dict_csv_reader)
    return list_of_json


def dit_csv_file(csv_file):
    decoded_file = csv_file.read().decode('utf-8').splitlines()
    dict_csv_reader = csv.DictReader(decoded_file)
    list_of_json = list(dict_csv_reader)
    return list_of_json


def lead_conversion_rate():
    total_prospects = Lead.objects.count()
    total_leads = Lead.objects.filter(status=Lead_status.ACTIVE)
    percentage_conversion_rate = (total_leads / total_prospects * 100)
    return percentage_conversion_rate


def pipeline_metrics(request):
    pipeline_id = request.data.get("pipeline_id")

    try:
        pipeline_instance = Pipeline.objects.get(id=pipeline_id)
    except Pipeline.DoesNotExist:
        pass


ACTIVITY_CHOICES = [
    ("COMPLETED", "COMPLETED"),
    ("CANCELLED", "CANCELLED"),
    ("ONGOING", "ONGOING"),
    ("RESCHEDULE", "RESCHEDULE"),
]


# Create your model(s) here.
class NewsLetterSubscription(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    full_name = models.CharField(max_length=255, blank=False, null=False)
    email = models.EmailField(blank=False, null=False, unique=True)
    is_consent_given = models.BooleanField(blank=False, null=False, default=False)
    is_subscribed = models.BooleanField(blank=False, null=False, default=False)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    def __str__(self):
        return self.full_name + " " + self.email

    @classmethod
    def create(cls, validated_data):
        new_subscriber = cls.objects.create(
            full_name=validated_data.get("full_name"),
            email=validated_data.get("email"),
            is_consent_given=validated_data.get("is_consent_given"),
            is_subscribed=True,
        )


class ProductVerticals(models.Model):
    company = models.ForeignKey(
        "requisition.Company",
        on_delete=models.CASCADE,
        to_field="id",
        db_column="company_id",
        null=True,
        blank=True,
    )
    team = models.ForeignKey(
        "requisition.Team",
        on_delete=models.CASCADE,
        to_field="id",
        db_column="team_id",
        related_name="team_product_vertical",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255, unique=True, null=True, blank=True)

    # def __str__(self):
    #     return self.name

    class Meta:
        verbose_name = "PRODUCT VERTICAL"
        verbose_name_plural = "PRODUCT VERTICALS"

    @classmethod
    def create(cls, validated_data):
        new_product_vertical = cls.objects.create(
            company=validated_data.get("company"),
            team=validated_data.get("team"),
            name=validated_data.get("name")
        )
        return new_product_vertical


class SalesExecutivesTargetConstantData(models.Model):
    sales_leads_expected_subscription_target_per_month = models.IntegerField(null=True, blank=True)
    sales_leads_expected_transaction_count_per_month = models.IntegerField(null=True, blank=True)
    sales_leads_expected_merchant_target_per_month = models.IntegerField(null=True, blank=True)
    sales_officer_expected_transaction_count_per_month = models.IntegerField(null=True, blank=True)
    sales_officer_expected_subscription_target_per_month = models.IntegerField(null=True, blank=True)
    sales_officer_expected_merchant_target_per_month = models.IntegerField(null=True, blank=True)

    class Meta:
        verbose_name = "SALES EXECUTIVES TARGET CONSTANT DATA"
        verbose_name_plural = "SALES EXECUTIVES TARGET CONSTANT DATA"


class RevenueLines(models.Model):
    merchant = models.ForeignKey("Merchants", on_delete=models.CASCADE, null=True, blank=True)
    vertical = models.ForeignKey("ProductVerticals", on_delete=models.CASCADE, null=True, blank=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, null=True, blank=True)

    # def __str__(self):
    #     return f"{self.merchant} - {self.vertical} - {self.amount}"

    class Meta:
        verbose_name = "REVENUE LINE"
        verbose_name_plural = "REVENUE LINES"


class SalesLead(BaseModel):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    company = models.ForeignKey(
        Company,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='sales_lead_mother_company',
    )
    team_member = models.ForeignKey(TeamMember, null=True, blank=True, on_delete=models.CASCADE)
    name = models.CharField(max_length=255, null=True, blank=True)
    is_sales_lead = models.BooleanField(default=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    date_hired = models.DateField(auto_now_add=True)
    product_verticals = models.ManyToManyField(ProductVerticals, max_length=200, blank=True)
    merchants_acquired = models.IntegerField(default=0)
    terminals_deployed = models.IntegerField(default=0)
    merchant_activation_rate = models.DecimalField(max_digits=15, decimal_places=2, default=0.0)
    overall_merchant_activation_rate = models.DecimalField(max_digits=15, decimal_places=2, default=0.0)
    revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    overall_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    average_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    overall_average_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_transaction_volume = models.IntegerField(default=0)
    total_subscription_volume = models.IntegerField(default=0)
    total_subscription_value = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    active_merchants_count = models.IntegerField(default=0)
    inactive_merchants_count = models.IntegerField(default=0)
    customer_satisfaction_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    sales_team_performance = models.JSONField(default=dict, null=True, blank=True)

    # def __str__(self):
    #     return self.name

    class Meta:
        verbose_name = "SALES LEAD"
        verbose_name_plural = "SALES LEADS"

    @property
    def active_merchants_percentage(self):
        total_merchants = self.active_merchants_count + self.inactive_merchants_count
        return (self.active_merchants_count / total_merchants) * 100 if total_merchants > 0 else 0

    @property
    def inactive_merchants_percentage(self):
        total_merchants = self.active_merchants_count + self.inactive_merchants_count
        return (self.inactive_merchants_count / total_merchants) * 100 if total_merchants > 0 else 0

    @property
    def active_vs_inactive_comparison_ratio(self):
        return f"{self.active_merchants_count}:{self.inactive_merchants_count}"

    @classmethod
    def create_sales_lead(cls, user: TeamMember, validated_data):
        new_sales_lead = cls.objects.create(
            user=user.member,
            team_member=validated_data.get("team_member"),
            name=user.member.username,
            company=validated_data.get("company"),
            phone_number=validated_data.get("phone_number"),
            email=validated_data.get("email"),
        )
        # new_sales_lead.product_verticals.add(
        #     validated_data.get("product_verticals")
        # )
        # new_sales_lead.save()
        return new_sales_lead


class SalesOfficer(BaseModel):
    team_member = models.ForeignKey(TeamMember, null=True, blank=True, on_delete=models.CASCADE)
    referral_code = models.CharField(
        max_length=20,
        unique=True,
        default=generate_random_referral_code,
        editable=False,
    )
    sales_lead = models.ForeignKey(
        SalesLead,
        on_delete=models.CASCADE,
        related_name="sales_officers",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255, null=True, blank=True)
    is_sales_officer = models.BooleanField(default=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    date_hired = models.DateField(auto_now_add=True)
    product_module = models.ForeignKey(ProductVerticals, on_delete=models.CASCADE, null=True, blank=True)
    merchants_acquired = models.IntegerField(default=0)
    terminals_deployed = models.IntegerField(default=0)
    merchant_activation_rate = models.DecimalField(max_digits=15, decimal_places=2, default=0.0)
    revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    overall_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    average_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    overall_average_revenue_generated = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_transaction_volume = models.IntegerField(default=0)
    total_subscription_volume = models.IntegerField(default=0)
    total_subscription_value = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    active_merchants_count = models.IntegerField(default=0)
    inactive_merchants_count = models.IntegerField(default=0)
    customer_satisfaction_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    individual_merchant_metrics = models.JSONField(default=list, null=True, blank=True)
    merchant_acquisition_status = models.CharField(max_length=50, default='', null=True, blank=True)
    merchant_acquisition_percentage_ratio = models.FloatField(default=0.0)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "SALES OFFICER"
        verbose_name_plural = "SALES OFFICERS"

    @property
    def active_merchants_percentage(self):
        total_merchants = self.active_merchants_count + self.inactive_merchants_count
        return (self.active_merchants_count / total_merchants) * 100 if total_merchants > 0 else 0

    @property
    def inactive_merchants_percentage(self):
        total_merchants = self.active_merchants_count + self.inactive_merchants_count
        return (self.inactive_merchants_count / total_merchants) * 100 if total_merchants > 0 else 0

    @property
    def active_vs_inactive_comparison_ratio(self):
        return f"{self.active_merchants_count}:{self.inactive_merchants_count}"

    @classmethod
    def create_sales_officer(cls, team_member: TeamMember, validated_data):
        new_sales_officer = cls.objects.create(
            team_member=team_member,
            name=team_member.member.username,
            phone_number=validated_data.get("phone_number"),
            email=validated_data.get("email"),
            sales_lead=validated_data.get("sales_lead"),
            product_module=validated_data.get("product_module"),
            referral_code=generate_random_referral_code(),
        )
        new_sales_officer.company = new_sales_officer.sales_lead.company
        new_sales_officer.save()
        return new_sales_officer


class Merchants(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.PROTECT, null=True, blank=True)
    sales_officer = models.ForeignKey(SalesOfficer, on_delete=models.CASCADE, null=True, blank=True)
    subscription_plan = models.ForeignKey(
        subscription.SubscriptionModule,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    subscription_status = models.BooleanField(default=False)
    transaction_volume = models.IntegerField(null=True, blank=True)
    transaction_volume_status = models.BooleanField(default=False)
    growth_rate = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    avg_order_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    transaction_percentage_of_target = models.FloatField(default=0.0, null=True, blank=True)
    active_longevity = models.IntegerField(null=True, blank=True)
    product_vertical = models.CharField(max_length=255, null=True, blank=True)
    referral_code = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = "MERCHANT"
        verbose_name_plural = "MERCHANTS"

    def create_revenue_lines(self):
        product_verticals = ProductVerticals.objects.all()
        for vertical in product_verticals:
            RevenueLines.objects.create(merchant=self, vertical=vertical, amount=0.00)

    def get_revenues(self):
        return RevenueLines.objects.filter(merchant=self)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if not self.get_revenues().exists():
            self.create_revenue_lines()


class Category(models.Model):
    company = models.ForeignKey(
        "requisition.Company",
        on_delete=models.CASCADE,
        to_field="id",
        db_column="company_id",
        related_name="team_company",
        null=True,
        blank=True,
    )
    team = models.ForeignKey(
        "requisition.Team",
        on_delete=models.CASCADE,
        to_field="id",
        db_column="team_id",
        related_name="team_category",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255, null=True, blank=True)
    description = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    # def __str__(self):
    #     return self.name

    class Meta:
        verbose_name = "CATEGORY"
        verbose_name_plural = "CATEGORIES"
        constraints = [
            models.UniqueConstraint(fields=['company', 'team', 'name'], name='unique_name_per_company_and_team')
        ]

    @classmethod
    def create(cls, validated_data):
        new_category = cls.objects.create(
            company=validated_data.get("company"),
            team=validated_data.get("team"),
            name=validated_data.get("name"),
            description=validated_data.get("description")
        )
        return new_category


class Lead(models.Model):
    contact_name = models.CharField(max_length=250, null=True, blank=True)
    contact_email = models.EmailField(null=True, blank=True)
    contact_phone_number = models.CharField(max_length=250, null=True, blank=True)
    contact_address = models.CharField(max_length=250, null=True, blank=True)
    company_name = models.CharField(max_length=250, null=False, blank=False, unique=True, default="Host Company")
    company_email = models.EmailField(null=True, blank=True)
    company_phone_number = models.CharField(max_length=250, null=True, blank=True)
    company_address = models.CharField(max_length=250, null=True, blank=True)
    source = models.CharField(
        max_length=100,
        choices=Lead_source.choices,
        default=Lead_source.PHYSICAL,
        blank=True,
        null=True,
    )
    sales_officer_in_charge = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="leads",
    )
    pipeline = models.ForeignKey("Pipeline", on_delete=models.CASCADE, null=True, blank=True)
    stage = models.ForeignKey("Stage", on_delete=models.CASCADE, null=True, blank=True)
    active_stage_start_date = models.DateField(null=True, blank=True)
    active_stage_expiry_countdown = models.PositiveIntegerField(default=0, null=True, blank=True)
    active_stage_expiry_date = models.DateField(null=True, blank=True)
    engagement_status = models.CharField(
        max_length=100,
        choices=Engagement_status.choices,
        default=Engagement_status.COLD,
        blank=True,
        null=True,
    )
    deal_status = models.CharField(
        max_length=100,
        choices=Deal_status.choices,
        default=Deal_status.ONGOING,
        blank=True,
        null=True,
    )
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    time_created = models.TimeField(auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)
    status = models.CharField(
        max_length=255,
        choices=Lead_status.choices,
        default=Lead_status.PENDING,
        blank=True,
        null=False,
    )
    onboard_status = models.CharField(
        max_length=255,
        choices=Lead_onboarding_status.choices,
        default=Lead_onboarding_status.AWAITING_ONBOARDING,
        blank=True,
        null=True,
    )
    country = models.CharField(max_length=255, null=True, blank=True)
    how_did_you_hear_about_us = models.TextField(null=True, blank=True)
    employee_head_count = models.CharField(max_length=255, null=True, blank=True)
    monthly_transaction_amount_estimate = models.CharField(max_length=255, null=True, blank=True)
    deal_value = models.FloatField(null=True, blank=True)
    product_vertical = models.ManyToManyField(
        ProductVerticals,
        related_name="product_module",
        blank=True,
    )
    expiry_date = models.DateTimeField(null=True, blank=True)

    # def __str__(self):
    #     return f"{self.company_name}"

    class Meta:
        verbose_name = "LEAD"
        verbose_name_plural = "LEADS"

    @classmethod
    def create_lead(cls, sales_officer, product_vertical, validated_data):
        new_prospect = cls.objects.create(
            sales_officer_in_charge=sales_officer,
            contact_name=validated_data.get("contact_name"),
            contact_email=validated_data.get("contact_email"),
            contact_phone_number=validated_data.get("contact_phone_number"),
            company_name=validated_data.get("company_name"),
            company_email=validated_data.get("company_email"),
            company_phone_number=validated_data.get("company_phone_number"),
            company_address=validated_data.get("company_address"),
            pipeline=validated_data.get("pipeline"),
            stage=validated_data.get("stage"),
            engagement_status=validated_data.get("engagement_status") or Engagement_status.COLD,
            deal_value=validated_data.get("deal_value"),
        )
        if product_vertical is not None:
            new_prospect.product_vertical.add(*product_vertical)
        if new_prospect.pipeline is not None:
            new_prospect.stage.update_lead_count()
            new_prospect.status = Lead_status.ACTIVE
        new_prospect.save()
        return new_prospect

    @classmethod
    def create_lead_online(cls, product_vertical, validated_data):
        new_prospect = cls.objects.create(
            company_name=validated_data.get("company_name"),
            company_email=validated_data.get("work_email"),
            contact_name=validated_data.get("full_name"),
            contact_phone_number=validated_data.get("phone_number"),
            country=validated_data.get("country"),
            how_did_you_hear_about_us=validated_data.get("how_did_you_hear_about_us"),
            employee_head_count=validated_data.get("employee_head_count"),
            monthly_transaction_amount_estimate=validated_data.get("monthly_transaction_amount_estimate"),
            source=Lead_source.ONLINE
        )
        if product_vertical is not None:
            new_prospect.product_vertical.add(*product_vertical)
        if new_prospect.pipeline is not None:
            new_prospect.stage.update_lead_count()
            new_prospect.status = Lead_status.ACTIVE
        new_prospect.save()
        return new_prospect

    @classmethod
    def create_lead_using_csv_data_upload(cls, csv_file, sales_officer, pipeline, stage, product_vertical):
        from .serializers import ReadLeadSerializer
        number_of_leads_created = 0
        list_of_leads_created = []
        number_of_leads_not_created = 0
        list_of_leads_not_created = []

        df = cls.validate_file_type(csv_file=csv_file)

        for index, row in df.iterrows():
            try:
                new_lead = cls.create_lead(
                    sales_officer=sales_officer,
                    product_vertical=product_vertical,
                    validated_data=dict(row)
                )
                if pipeline:
                    new_lead.pipeline = pipeline
                    new_lead.stage = stage
                    new_lead.status = Lead_status.ACTIVE
                    new_lead.set_active_stage_start_and_expiry_date()
                    new_lead.save()

                number_of_leads_created += 1
                serialize_new_lead = ReadLeadSerializer(new_lead)
                list_of_leads_created.append(serialize_new_lead.data)

            except:
                number_of_leads_not_created += 1
                # list_of_leads_not_created.append(row.data)
                list_of_leads_not_created.append(dict(row))
                # list_of_leads_not_created.append(row)

        return {
            "Number Of Leads Created": number_of_leads_created,
            "List of Leads Created": list_of_leads_created,
            "Number Of Leads Not Created": number_of_leads_not_created,
            "List of Leads Not Created": list_of_leads_not_created,
        }

    @classmethod
    def distribute_leads_to_all_sales_officers(cls, sales_officer_instances, list_of_leads):

        if not sales_officer_instances:
            raise ValueError("The list of sales officers cannot be empty.")

        for i, lead in enumerate(list_of_leads):
            sales_officer = sales_officer_instances[i % len(sales_officer_instances)]
            lead.sales_officer_in_charge = sales_officer
            lead.save()

    @classmethod
    def assign_leads_to_sales_officer_via_file_upload(cls, csv_file, sales_officers, product_vertical):
        from .serializers import ReadLeadSerializer
        list_of_leads_objects = []
        number_of_leads_created = 0
        list_of_leads_created = []
        number_of_leads_not_created = 0
        list_of_leads_not_created = []

        df = cls.validate_file_type(csv_file=csv_file)

        for index, row in df.iterrows():
            try:
                new_lead = cls.create_lead(
                    product_vertical=product_vertical,
                    validated_data=dict(row)
                )

                number_of_leads_created += 1
                serialize_new_lead = ReadLeadSerializer(new_lead)
                list_of_leads_objects.append(new_lead)
                list_of_leads_created.append(serialize_new_lead.data)

            except:
                number_of_leads_not_created += 1
                # list_of_leads_not_created.append(row.data)
                list_of_leads_not_created.append(dict(row))
                # list_of_leads_not_created.append(row)

        cls.distribute_leads_to_all_sales_officers(
            sales_officer_instances=sales_officers,
            list_of_lead=list_of_leads_objects)

        return {
            "number_of_leads_created": number_of_leads_created,
            "list_of_leads_created": list_of_leads_created,
            "number_of_leads_not_created": number_of_leads_not_created,
            "list_of_leads_not_created": list_of_leads_not_created,
        }

    @classmethod
    def validate_file_type(cls, csv_file):
        if csv_file.name.endswith(".csv"):
            decoded_file = csv_file.read().decode()
            io_string = io.StringIO(decoded_file)
            df = pd.read_csv(io_string)
        elif csv_file.name.endswith(".xlsx"):
            df = pd.read_excel(csv_file)
        else:
            return "Invalid File Type"
        return df

    def set_active_stage_start_and_expiry_date(self):
        start_date = date.today()
        expiry_date = date.today() + timedelta(days=self.stage.duration_in_days)

        self.active_stage_start_date = start_date
        self.active_stage_expiry_date = expiry_date
        self.update_countdown_and_lead_expiry_in_stage()
        self.save()

        return expiry_date

    def update_countdown_and_lead_expiry_in_stage(self):
        if self.stage is not None and self.active_stage_start_date is not None:
            number_of_days_used = (date.today() - self.active_stage_start_date).days

            if 0 <= number_of_days_used <= self.stage.duration_in_days:
                self.active_stage_expiry_countdown = self.stage.duration_in_days - number_of_days_used
            else:
                self.active_stage_expiry_countdown = number_of_days_used

        if self.active_stage_expiry_date is not None:
            if date.today() > self.active_stage_expiry_date:
                # self.pipeline = None
                self.stage = None
                self.status = Lead_status.DROP_OFF

        self.save()


class LeadTrail(models.Model):
    Lead = models.ForeignKey(Lead, null=True, blank=True, on_delete=models.CASCADE)
    stage = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    class Meta:
        verbose_name = "LEAD TRAIL"
        verbose_name_plural = "LEAD TRAILS"

    def create(self, validated_data, lead_id, new_stage):
        new_trail = LeadTrail.objects.create(
            Lead=lead_id,
            stage=self.stage.append(new_stage)
        )
        new_trail.save()

    @classmethod
    def deal_progression_count(cls, id):
        # Filter all deals for the given deal_id where current stage is not null
        all_merchants = cls.objects.filter(id=id, stage__isnull=False)

        # Get distinct current stages
        unique_stage = all_merchants.values("stage").distinct()

        data = []
        for stage in unique_stage:
            data.append(stage.get("stage"))

        all_count = {}
        total_count = cls.objects.filter(id=id).count() or 0

        for stage_id in data:
            # Get the count of deals for the current stage
            this_count = all_merchants.filter(stage__id=stage_id).count() or 0
            # Get the stage name from the Stage model
            stage_name = Stage.objects.filter(id=stage_id).values_list("name", flat=True).first()
            # Update the all_count dictionary with the stage name
            all_count.update({stage_name: this_count})
        this_data = {
            "total_count": total_count,
            "data": all_count
        }

        return this_data

    @classmethod
    def deal_count(cls, id):
        # Filter all deals for where current stage is not null
        all_deals = cls.objects.filter(merchant__id=id, stage__isnull=False)

        # Get distinct current stages
        unique_stage = all_deals.values("stage").distinct()

        data = []
        for stage in unique_stage:
            data.append(stage.get("stage"))

        all_count = {}
        total_count = cls.objects.filter(merchant__id=id).count() or 0

        for stage_id in data:
            # Get the count of all deals for the current stage
            this_count = all_deals.filter(stage__id=stage_id).count() or 0
            # Get the stage name from the Stage model
            stage_name = Stage.objects.filter(id=stage_id).values_list("name", flat=True).first()
            # Update the all_count dictionary with the stage name
            all_count.update({stage_name: this_count})

            this_data = {
                "total_count": total_count,
                "data": all_count
            }

            return this_data

    def update_deal_status(self):
        """
        Updates the deal status based on the deal's start date and expiry date.
        """
        now = datetime.now().date()

        if self.expiry_date and self.expiry_date < now:
            self.deal_status = "LOST"
        elif self.start_date and self.start_date <= now <= self.expiry_date:
            self.deal_status = "WON"
        elif self.start_date and now < self.start_date:
            self.deal_status = "ONGOING"
        self.save()


class Pipeline(models.Model):
    PIPELINE_TYPE_CHOICES = [
        ("DEFAULT", "DEFAULT"),
        ("CUSTOM", "CUSTOM"),
    ]

    sales_officer = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=100, null=True, blank=True)
    description = models.CharField(max_length=255, null=True, blank=True)
    type = models.CharField(max_length=100, choices=PIPELINE_TYPE_CHOICES, default="DEFAULT")
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    # def __str__(self):
    #     return self.name

    class Meta:
        verbose_name = "PIPELINE"
        verbose_name_plural = "PIPELINES"
        constraints = [
            models.UniqueConstraint(fields=['sales_officer', 'name'], name='unique_name_per_sales_officer')
        ]

    @classmethod
    def create(cls, sales_officer, validated_data):
        new_pipeline = cls.objects.create(
            sales_officer=sales_officer,
            name=validated_data.get("name"),
            description=validated_data.get("description"),
            type=validated_data.get("type"),
        )
        if new_pipeline.type == "DEFAULT":
            new_pipeline.is_default = True
        else:
            new_pipeline.is_default = False
        new_pipeline.save()
        return new_pipeline


class Stage(models.Model):
    pipeline = models.ForeignKey(Pipeline, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(default=0)
    lead_count = models.IntegerField(default=0)
    duration_in_days = models.PositiveIntegerField(default=60)
    email_subject = models.CharField(max_length=100, null=True, blank=True)
    email_body = models.TextField(null=True, blank=True)
    email_text = models.TextField(null=True, blank=True)
    email_notifications_enabled = models.BooleanField(default=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    # def __str__(self):
    #     return self.category.name

    class Meta:
        verbose_name = "STAGES"
        verbose_name_plural = "STAGES"

    def update_lead_count(self):
        number_of_leads_in_stage = Lead.objects.filter(stage=self.id).count()
        self.lead_count = number_of_leads_in_stage
        self.save()

    @classmethod
    def create(cls, validated_data):
        order_number = cls.objects.filter(pipeline=validated_data.get("pipeline")).count()

        new_stage = cls.objects.create(
            pipeline=validated_data.get("pipeline"),
            name=validated_data.get("name"),
            category=validated_data.get("category"),
            order=order_number + 1,
            lead_count=0,
            # email_subject=validated_data.get("email_subject"),
            # email_body=validated_data.get("email_body"),
            # email_text=validated_data.get("email_text"),
            # email_notifications_enabled=validated_data.get("email_notifications_enabled"),
        )
        return new_stage

    @classmethod
    def get_number_of_leads_in_stage(cls, stage_id):
        stage_instance = Stage.objects.get(id=stage_id)
        lead_stage = Lead.objects.filter(stage=stage_instance.id, status=Lead_status.ACTIVE)
        total_number = lead_stage.count()
        stage_instance.lead_count = total_number
        stage_instance.save()


class Activity(models.Model):
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE, null=True, blank=True)
    sales_officer = models.ForeignKey(TeamMember, on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    description = models.CharField(max_length=500, null=True, blank=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    # def __str__(self) -> str:
    #     return self.title

    class Meta:
        verbose_name = "ACTIVITY"
        verbose_name_plural = "ACTIVITIES"

    @classmethod
    def create(cls, sales_officer: SalesOfficer, validated_data):
        # sales_lead_id= sales_officer.sales_lead
        # try:
        #     sales_lead_instance = SalesLead.objects.get(user=sales_lead_id)
        # except SalesLead.DoesNotExist:
        #     return "Sales Lead does not exist."

        new_activity = cls.objects.create(
            # sales_officer=validated_data.get("sales_officer"),
            # lead=sales_lead_instance,
            lead=validated_data.get("lead"),
            sales_officer=sales_officer,
            title=validated_data.get("title"),
            description=validated_data.get("description"),
        )
        # sales_lead = sales_officer.sales_lead
        # cls.lead = sales_lead
        #
        # cls.save(new_activity)
        return new_activity


class Task(models.Model):
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    priority = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        choices=Priority_Status.choices,
        default=Priority_Status.LOW,
    )
    status = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        choices=Task_Status.choices,
        default=Task_Status.ONGOING,
    )
    sales_officer = models.ForeignKey(
        "requisition.TeamMember",
        on_delete=models.CASCADE,
        max_length=100,
        null=True,
        blank=True,
    )
    attendees = models.JSONField(null=True, blank=True, default=list)
    event = models.JSONField(null=True, blank=True, default=list)
    meeting = models.CharField(max_length=255, null=True, blank=True)
    deadline = models.DateTimeField(null=True, blank=True)
    is_reminder = models.BooleanField(null=True, blank=True, default=False)
    is_done = models.BooleanField(null=True, blank=True, default=False)
    deadline_reminder = models.BooleanField(null=True, blank=True, default=False)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    # def __str__(self):
    #     return f"ID - {self.id} Title{self.title}"

    class Meta:
        verbose_name = "TASK"
        verbose_name_plural = "TASKS"

    @classmethod
    def create(cls, validated_data):
        new_task = cls.objects.create(
            activity=validated_data.get("activity"),
            assigned=cls.activity.sales_officer.member,
            title=validated_data.get("title"),
            description=validated_data.get("description"),
            priority=validated_data.get("priority"),
            deadline=validated_data.get("deadline"),
            is_reminder=validated_data.get("is_reminder"),
            is_done=validated_data.get("is_done"),
            deadline_reminder=validated_data.get("deadline_reminder"),
        )
        return new_task

    @classmethod
    def create_event(
            cls, sales_officer_id, meeting, deadline,
            attendees: dict, *args, **kwargs
    ):
        new_event = cls.objects.create(
            sales_officer_id=sales_officer_id,
            meeting=meeting,
            attendees=attendees,
            deadline=deadline,
        )
        return new_event


class Notes(models.Model):
    sales_officer = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
    )
    title = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    # def __str__(self):
    #     return self.title

    class Meta:
        verbose_name = "NOTE"
        verbose_name_plural = "NOTES"

    @classmethod
    def create(cls, sales_officer, validated_data):
        new_note = cls.objects.create(
            # sales_officer=validated_data.get("sales_officer"),
            sales_officer=sales_officer,
            title=validated_data.get("title"),
            description=validated_data.get("description"),
        )
        return new_note


class EmailAccount(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    emails = models.EmailField(unique=True, null=True, blank=True)
    pop_server = models.CharField(
        default="mail.libertyng.com",
        max_length=100,
        null=True,
        blank=True,
    )
    pop_port = models.IntegerField(default=995, null=True, blank=True)
    smtp_server = models.CharField(max_length=100, null=True, blank=True)
    smtp_port = models.IntegerField(default=587, null=True, blank=True)
    username = models.CharField(max_length=100, null=True, blank=True)
    password = models.CharField(max_length=100, null=True, blank=True)
    last_sync = models.DateTimeField(null=True, blank=True)

    # def __str__(self):
    #     return f"{self.user.username} - {self.email}"

    class Meta:
        verbose_name = "EMAIL ACCOUNT"
        verbose_name_plural = "EMAIL ACCOUNTS"


class Emails(models.Model):
    sales_officer = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        null=True,
        blank=False,
    )
    subject = models.CharField(max_length=255, null=True, blank=True)
    sender = models.EmailField(null=True, blank=True)
    senders_name = models.CharField(max_length=255, null=True, blank=True)
    cc = models.JSONField(max_length=255, null=True, blank=True)
    bcc = models.JSONField(max_length=255, null=True, blank=True)
    recipients = models.JSONField(null=True, blank=True)
    date = models.DateTimeField(null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_read = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)
    is_inbox = models.BooleanField(default=False)
    is_draft = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_scheduled = models.BooleanField(default=False)

    def __str__(self):
        return f"Email address:{self.sender} | Sender's name:{self.senders_name} | Email subject:{self.subject} | Email date:{self.date}"

    class Meta:
        verbose_name = "EMAIL"
        verbose_name_plural = "EMAILS"

    @classmethod
    def save_as_read(
            cls,
            is_read,
            sales_officer,
            date,
            subject,
            sender,
            senders_name,
            cc,
            bcc,
            recipients,
            content,
    ):
        read_mail = cls.objects.create(
            sales_officer=sales_officer,
            is_read=is_read,
            date=date,
            subject=subject,
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients,
            content=content,
        )
        return read_mail

    @classmethod
    def save_as_sent_email(
            cls,
            is_sent,
            sales_officer,
            date,
            subject,
            sender,
            senders_name,
            cc,
            bcc,
            recipients,
            content,
    ):
        sent_mail = cls.objects.create(
            sales_officer=sales_officer,
            is_sent=is_sent,
            date=date,
            subject=subject,
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients,
            content=content,
        )
        return sent_mail

    @classmethod
    def save_as_inbox(
            cls,
            is_inbox,
            sales_officer,
            date,
            subject,
            sender,
            senders_name,
            cc,
            bcc,
            recipients,
            content,
    ):
        inbox = cls.objects.create(
            sales_officer=sales_officer,
            is_inbox=is_inbox,
            date=date,
            subject=subject,
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients,
            content=content,
        )
        return inbox

    @classmethod
    def save_as_draft(
            cls,
            is_draft,
            sales_officer,
            date,
            subject,
            sender,
            senders_name,
            cc,
            bcc,
            recipients,
            content,
    ):
        draft_mail = cls.objects.create(
            sales_officer=sales_officer,
            is_draft=is_draft,
            date=date,
            subject=subject,
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients,
            content=content,
        )
        return draft_mail

    @classmethod
    def deleted_mail(
            cls,
            is_deleted,
            sales_officer,
            date,
            subject,
            sender,
            senders_name,
            cc,
            bcc,
            recipients,
            content,
    ):
        deleted_email = cls.objects.create(
            sales_officer=sales_officer,
            is_deleted=is_deleted,
            date=date,
            subject=subject,
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients,
            content=content,
        )
        return deleted_email

    @classmethod
    def save_as_scheduled(
            cls,
            is_scheduled,
            sales_officer,
            date,
            subject,
            sender,
            senders_name,
            cc,
            bcc,
            recipients,
            content,
    ):
        scheduled_mail = cls.objects.create(
            sales_officer=sales_officer,
            is_scheduled=is_scheduled,
            date=date,
            subject=subject,
            sender=sender,
            senders_name=senders_name,
            cc=cc,
            bcc=bcc,
            recipients=recipients,
            content=content,
        )
        return scheduled_mail

    def soft_delete(self):
        """
        Soft deletes the object by setting the 'is_active' attribute to False
        and the 'is_deleted' attribute to True.
        NOTE:
        - It marks the object as inactive and deleted without removing it from the database.
        """
        self.is_active = False
        self.is_deleted = True
        self.save()


class Attachment(models.Model):
    email = models.ForeignKey(Emails, on_delete=models.CASCADE, null=True, blank=True)
    document = models.FileField(upload_to="email_attachments", null=True, blank=True)
    filename = models.CharField(max_length=255, null=True, blank=True)
    content_type = models.CharField(max_length=100, null=True, blank=True)

    # def __str__(self):
    #     return self.filename

    class Meta:
        verbose_name = "ATTACHMENT"
        verbose_name_plural = "ATTACHMENTS"


class BookADemo(models.Model):
    sales_officer = models.ForeignKey(
        SalesOfficer,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    team_member = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    lead = models.ForeignKey(
        Lead,
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    start_time = models.DateTimeField(null=True, blank=True)

    paybox_agent_name = models.CharField(max_length=255, null=True, blank=True)
    paybox_agent_phone_number = models.CharField(max_length=255, null=True, blank=True)
    is_assigned = models.BooleanField(default=False, null=True, blank=True)

    def __str__(self):
        return f"{self.lead} | {self.start_time}"

    class Meta:
        verbose_name = "BOOK A DEMO"
        verbose_name_plural = "BOOK A DEMO"


class WorkEmail(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="work_email")
    work_email = models.EmailField(unique=True)
    password = models.TextField()

    def __str__(self):
        return f"{self.user} | {self.work_email}"

    class Meta:
        verbose_name = "WORK EMAIL"
        verbose_name_plural = "WORK EMAILS"


class GmailAccount(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    gmail_address = models.EmailField(unique=True)
    access_token = models.CharField(max_length=500, unique=True, null=True, blank=True)
    refresh_token = models.CharField(max_length=255, unique=True, null=True, blank=True)
    authorization_code = models.CharField(max_length=255, unique=True, null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.gmail_address} | {self.user}"

    class Meta:
        verbose_name = "GMAIL ACCOUNT"
        verbose_name_plural = "GMAIL ACCOUNTS"
