from django.db import models


class Engagement_status(models.TextChoices):
    HOT = "HOT", "HOT"
    WARM = "WARM", "WARM"
    COLD = "COLD", "COLD"


class Deal_status(models.TextChoices):
    WON = "WON", "WON"
    LOST = "LOST", "LOST"
    ONGOING = "ONGOING", "ONGOING"


class Priority_Status(models.TextChoices):
    LOW = "LOW", "LOW"
    MEDIUM = "MEDIUM", "MEDIUM"
    HIGH = "HIGH", "HIGH"


class Product_verticals(models.TextChoices):
    STOCKS_INVENTORY = "STOCKS_INVENTORY", "STOCKS_INVENTORY"
    SALES = "SALES", "SALES"
    SPEND_MANAGEMENT = "SPEND_MANAGEMENT", "SPEND_MANAGEMENT"
    HR_MANAGEMENT = "HR_MANAGEMENT", "HR_MANAGEMENT"


class Created(models.TextChoices):
    MANUAL = "MANU<PERSON>", "MANU<PERSON>"
    IMPORTED = "IMPORTED", "IMPORTED"


class Lead_status(models.TextChoices):
    ACTIVE = "ACTIVE", "ACTIVE"
    PENDING = "PENDING", "PENDING"
    AWAITING = "AWAITING", "AWAITING"
    DROP_OFF = "DROP_OFF", "DROP_OFF"
    CLOSED = "CLOSED", "CLOSED"


class Lead_onboarding_status(models.TextChoices):
    AWAITING_ONBOARDING = "AWAITING_ONBOARDING", "AWAITING_ONBOARDING"
    ONBOARD = "ONBOARD", "ONBOARD"


class Lead_source(models.TextChoices):
    ONLINE = "ONLINE", "ONLINE"
    PHYSICAL = "PHYSICAL", "PHYSICAL"
    LINKEDIN = "LINKEDIN", "LINKEDIN"
    FACEBOOK = "FACEBOOK", "FACEBOOK"
    INSTAGRAM = "INSTAGRAM", "INSTAGRAM"
    TWITTER = "TWITTER", "TWITTER" 


class Task_Status(models.TextChoices):
    COMPLETED = "COMPLETED", "COMPLETED"
    CANCELLED = "CANCELLED", "CANCELLED"
    ONGOING = "ONGOING", "ONGOING"
    RESCHEDULE = "RESCHEDULE", "RESCHEDULE"


class Employee_Head_Count(models.TextChoices):
    SMALL = "1 - 50", "1 - 50"
    MEDIUM = "51 - 100", "51 - 100"
    LARGE = "101 - 150", "101 - 150"
    VERY_LARGE = "151 - above", "151 - above"


class Transaction_Estimate(models.TextChoices):
    SMALL = "50k - 100k", "50k - 100k"
    MEDIUM = "101k - 500k", "101k - 500k"
    LARGE = "500k - 1m", "500k - 1m"
    VERY_LARGE = "1m - above", "1m - above"
