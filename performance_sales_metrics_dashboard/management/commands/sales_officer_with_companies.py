from datetime import datetime
import random
from django.core.management.base import BaseCommand
import pytz
from django.conf import settings
from performance_sales_metrics_dashboard import models as perf
from subscription_and_invoicing import models as subscription
from requisition import models as req


class Command(BaseCommand):
    help = 'Populate the database with initial data for sales leads and sales officers.'

    def handle(self, *args, **kwargs):
        # Start timing the process
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # Get all sales officers
        sales_officers = perf.SalesOfficer.objects.all()

        # Fetch existing companies from the database
        companies = req.Company.objects.all()

        for officer in sales_officers:
            # Assign at least 9 existing companies to each sales officer using their referral_code
            for i, company in enumerate(companies[:9]):
                # Subscribe the company to the product_vertical or product_module the sales officer is managing
                subscription_type = subscription.SubscriptionType.objects.first()  # Assuming first available type for simplicity
                subscription_module = subscription.SubscriptionModule.objects.create(
                    subscription_type=subscription_type,
                    duration_in_months=12,  # Example duration
                    start_date=datetime.now().date()
                )

                # Create an invoice for each subscription
                invoice = subscription.Invoice.objects.create(
                    company=company,
                    module=subscription_module,
                    amount=subscription_type.price,
                    amount_due=subscription_type.price,
                    start_date=datetime.now(),
                    expiry_date=subscription_module.calculate_expiry_date(),
                    sales_officer=officer.referral_code
                )

                # Create a Merchant record linked to the company and sales officer
                merchant = perf.Merchants.objects.create(
                    company=company,
                    sales_officer=officer,
                    subscription_plan=subscription_module,
                    subscription_status=True,  # Assuming active subscription
                    product_vertical=officer.product_module.name,
                    referral_code=officer.referral_code
                )

                # Create revenue lines for each product vertical associated with the merchant
                merchant.create_revenue_lines()

        # End timing the process
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"Data population completed in {(END_TIME - START_TIME).total_seconds()} seconds"))
class Command(BaseCommand):
    help = 'Populate the database with initial data for sales leads and sales officers.'

    def handle(self, *args, **kwargs):
        # Start timing the process
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # Get all sales officers
        sales_officers = perf.SalesOfficer.objects.all()

        # Fetch existing companies from the database
        companies = req.Company.objects.all()

        for officer in sales_officers:
            # Assign at least 9 existing companies to each sales officer using their referral_code
            for i, company in enumerate(companies[:9]):
                # Get the product module the sales officer is managing
                product_module = officer.product_module.name

                # Find the subscription type that matches the product module
                subscription_type = subscription.SubscriptionType.objects.filter(
                    name__startswith=product_module[:4]  # Matching first four characters
                ).first()

                if subscription_type:
                    subscription_module = subscription.SubscriptionModule.objects.create(
                        subscription_type=subscription_type,
                        duration_in_months=12,  # Example duration
                        start_date=datetime.now().date()
                    )

                    # Create an invoice for each subscription
                    invoice = subscription.Invoice.objects.create(
                        company=company,
                        module=subscription_module,
                        amount=subscription_type.price,
                        amount_due=subscription_type.price,
                        start_date=datetime.now(),
                        expiry_date=subscription_module.calculate_expiry_date(),
                        sales_officer=officer.referral_code
                    )

                    # Create a Merchant record linked to the company and sales officer
                    merchant = perf.Merchants.objects.create(
                        company=company,
                        sales_officer=officer,
                        subscription_plan=subscription_module,
                        subscription_status=True,  # Assuming active subscription
                        product_vertical=officer.product_module.name,
                        referral_code=officer.referral_code
                    )

                    # Create revenue lines for each product vertical associated with the merchant
                    merchant.create_revenue_lines()

        # End timing the process
        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(
            self.style.SUCCESS(f"Data population completed in {(END_TIME - START_TIME).total_seconds()} seconds"))
