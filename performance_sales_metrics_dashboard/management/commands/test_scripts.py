from __future__ import print_function
import random
from django.core.management.base import BaseCommand
from celery import shared_task
from requests import Response, request 
from django.core.cache import cache
from decouple import config
from core.services import WhisperSms
from itertools import combinations
from core.tasks import send_email
from django.utils import timezone

class Command(BaseCommand):
    help = ''

    def handle(self, *args, **kwargs):
        url = "http://127.0.0.1:8000/?state=<EMAIL>&code=4/0AQSTgQEbRMyQGhGzJzjah79A1Bi6OOMejpcDF685gjdqBDg-FbgtVypnRwCQN3bnbBFnmA&scope=https://mail.google.com/"
        code = url.strip("http://127.0.0.1:8000/?state=<EMAIL>&code=")
        if code == "4/0AQSTgQEbRMyQGhGzJzjah79A1Bi6OOMejpcDF685gjdqBDg-FbgtVypnRwCQN3bnbBFnmA":

            print("TRUE:::::", code)