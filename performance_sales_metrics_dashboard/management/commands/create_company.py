# from django.core.management.base import BaseCommand
# from django.conf import settings
# import random
# from decimal import Decimal
# from datetime import datetime, timedelta
# import pytz
# import uuid
# from requisition import models as req
# from subscription_and_invoicing import models as sub
# from performance_sales_metrics_dashboard import models as perf
# from django.contrib.auth import get_user_model
# User = get_user_model()
#
#
# class Command(BaseCommand):
#     help = 'Populate the database with 60 companies for the specified user and onboard them using sales officer referral codes.'
#
#     def handle(self, *args, **kwargs):
#         START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
#
#         user_id = "8656c269-fcfa-4d3d-8b51-7ca04c15e83a"
#         user = User.objects.get(id=user_id)
#         sales_officers = list(perf.SalesOfficer.objects.all())
#         subscription_types = list(sub.SubscriptionType.objects.all())
#
#         for officer in sales_officers:
#             product_verticals = list(officer.product_module.all())
#             if not product_verticals:
#                 self.stdout.write(self.style.WARNING(f"Sales Officer {officer.name} has no associated product verticals. Skipping..."))
#                 continue
#
#             for i in range(8):  # Each officer gets 8 companies
#                 company_name = f"Company {officer.id}_{i + 1}"
#                 industry = random.choice(["Technology", "Finance", "Healthcare", "Retail", "Education"])
#                 size = random.randint(1, 500)
#                 selected_vertical = random.choice(product_verticals)
#
#                 # Create the company
#                 company = req.Company.objects.create(
#                     user=user,
#                     company_name=company_name,
#                     industry=industry,
#                     size=size,
#                     sales_officer=officer,
#                     product_vertical=selected_vertical,
#                     referral_code=officer.referral_code,
#                 )
#
#                 # Create a subscription module for the company
#                 subscription_type = random.choice(subscription_types)
#                 subscription_duration = random.choice([1, 3, 6, 12])  # in months
#                 start_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
#
#                 subscription = sub.SubscriptionModule.objects.create(
#                     subscritpion_type=subscription_type,
#                     duration_in_months=subscription_duration,
#                     start_date=start_date,
#                 )
#
#                 # Calculate the expiry date
#                 expiry_date = subscription.subscription_expiry_date()
#
#                 # Create an invoice for the subscription
#                 invoice = sub.Invoice.objects.create(
#                     company=company,
#                     module=subscription,
#                     amount=subscription_type.price,
#                     amount_due=subscription_type.price,
#                     start_date=start_date,
#                     expiry_date=expiry_date,
#                     settled_amount=Decimal(0),
#                     invoice_reference=str(uuid.uuid4()),
#                     is_active=True,
#                     payment_status='unpaid',
#                     sales_officer=officer.referral_code,
#                 )
#
#                 # Link the subscription to the invoice
#                 subscription.invoice = invoice
#                 subscription.save()
#
#         END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
#         self.stdout.write(self.style.SUCCESS(f"Created and onboarded companies with subscriptions and invoices for sales officers in {(END_TIME - START_TIME).total_seconds()} seconds"))


from django.core.management.base import BaseCommand
from django.conf import settings
import random
from decimal import Decimal
from datetime import datetime, timedelta
import pytz
import uuid
from requisition import models as req
from subscription_and_invoicing import models as sub
from performance_sales_metrics_dashboard import models as perf
from django.contrib.auth import get_user_model
User = get_user_model()


class Command(BaseCommand):
    help = 'Populate the database with 60 companies for the specified user and onboard them using sales officer referral codes.'

    def handle(self, *args, **kwargs):
        START_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        user_id = "c85b9c5d-6339-4f88-b722-3f1f19e284ba"
        user = User.objects.get(id=user_id)
        sales_officers = list(perf.SalesOfficer.objects.all())
        subscription_types = list(sub.SubscriptionType.objects.all())

        for officer in sales_officers:
            product_vertical = officer.product_module  # Accessing the single ProductVerticals instance
            if not product_vertical:
                self.stdout.write(self.style.WARNING(f"Sales Officer {officer.name} has no associated product vertical. Skipping..."))
                continue

            for i in range(8):  # Each officer gets 8 companies
                company_name = f"Company {officer.id}_{i + 1}"
                industry = random.choice(["Technology", "Finance", "Healthcare", "Retail", "Education"])
                size = random.randint(1, 500)

                # Create the company
                company = req.Company.objects.create(
                    user=user,
                    company_name=company_name,
                    industry=industry,
                    size=size,
                    # sales_officer=officer,
                    # product_vertical=product_vertical,
                    # referral_code=officer.referral_code,
                )

                # Create a subscription module for the company
                subscription_type = random.choice(subscription_types)
                subscription_duration = random.choice([1, 3, 6, 12])  # in months
                start_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

                subscription = sub.SubscriptionModule.objects.create(
                    subscription_type=subscription_type,
                    duration_in_months=subscription_duration,
                    start_date=start_date,
                )

                # Calculate the expiry date
                # expiry_date = subscription.subscription_expiry_date()
                expiry_date = subscription.calculate_expiry_date()

                # Create an invoice for the subscription
                invoice = sub.Invoice.objects.create(
                    company=company,
                    module=subscription,
                    amount=subscription_type.price,
                    amount_due=subscription_type.price,
                    start_date=start_date,
                    expiry_date=expiry_date,
                    settled_amount=Decimal(0),
                    invoice_reference=str(uuid.uuid4()),
                    is_active=True,
                    payment_status='unpaid',
                    sales_officer=officer.referral_code,
                )

                # Link the subscription to the invoice
                subscription.invoice = invoice
                subscription.save()

        END_TIME = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        self.stdout.write(self.style.SUCCESS(f"Created and onboarded companies with subscriptions and invoices for sales officers in {(END_TIME - START_TIME).total_seconds()} seconds"))

