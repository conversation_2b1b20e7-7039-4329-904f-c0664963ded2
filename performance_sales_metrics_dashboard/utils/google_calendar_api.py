import json
import os.path
import datetime
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from .generate_random_dates import generate_start_date, generate_end_date
from google.oauth2 import service_account
import threading
from django.core.cache import cache
from ..utils.parse_dates import get_time_max
# from .google_service import create_credentials

start_date = generate_start_date().strftime("%Y-%m-%dT%H:%M:%S%z")
end_date = generate_end_date(generate_start_date())
results_lock = threading.Lock()


def schedule_event_on_google_calendar(
        attendees:list, start_date:str, end_date:str,
        calendarIds, zoom_meeting_details,
        #summary:str, #description:str, #location:str,
    ):
    SCOPES = ["https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/calendar.events", "https://www.googleapis.com/auth/calendar.events.readonly", "https://www.googleapis.com/auth/calendar.readonly"]
      
    SERVICE_ACCOUNT_FILE = "service_account.json"
    if os.path.exists(SERVICE_ACCOUNT_FILE):
        creds = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES
        )
    
    try:
        service = build("calendar", "v3", credentials=creds)
        event = {
               'summary': f"Demo with Lead",
               'location': "Zoom",
               'description': f"PayBox 360 Demo {zoom_meeting_details}",
               'start': {
                 'dateTime': start_date,
                #  'dateTime': "2024-11-15T09:00:00-07:00",
                 'timeZone': 'Africa/Lagos',
               },
               'end': {
                 'dateTime': end_date,
                #  'dateTime': "2024-11-30T09:00:00-07:00",
                 'timeZone': 'Africa/Lagos',
               },
               'recurrence': [
                 'RRULE:FREQ=DAILY;COUNT=2'
               ],
               'attendees': attendees,
                #  "conferenceData": {"createRequest": {"requestId": "sample123", "conferenceSolutionKey": {"type": "hangoutsMeet"}}},
               'reminders': {
                 'useDefault': False,
                 'overrides': [
                   {'method': 'email', 'minutes': 24 * 60},
                   {'method': 'popup', 'minutes': 10},
                 ],
               },
            }
        # event = service.events().insert(calendarIds='primary', body=event).execute()
        print("CALENDAR_IDS:::", calendarIds, "\n\n\n")

        event = service.events().insert(calendarId=calendarIds, body=event).execute()
        print("EVENT:::", event, "\n\n\n")
        print('Event created: %s' % (event.get('htmlLink')))
        # print(res)
        # res = service.events().insert(calendarId=calendarIds, sendNotifications=True, body=event, conferenceDataVersion=1).execute()
    except HttpError as error:
      print(f"An error occurred: {error}")
      return f"An error occurred: {error}"
    meeting = event.get('htmlLink'),
    print(meeting)


def auto_schedule_event_on_google_calendar(
        attendees:list, summary:str, 
        description:str, location:str,
        calendarIds:str,
    ):
    # If modifying these scopes, delete the file token.json.
    SCOPES = ["https://www.googleapis.com/auth/calendar"]
    """Shows basic usage of the Google Calendar API.
    Prints the start and name of the next 10 events on the user's calendar.
    """
    creds = None
    # The file token.json stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first
    # time.
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)
        
    # If there are no (valid) credentials available, let the user log in.
    flow = InstalledAppFlow.from_client_secrets_file(
        "credentials.json", SCOPES
    )
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
          creds.refresh(Request())
        # else:
        #     flow = InstalledAppFlow.from_client_secrets_file(
        #     "client_secrets.json", SCOPES
        # )
        creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open("token.json", "w") as token:
          token.write(creds.to_json())

    try:
        """
        To create events, change the scope to 'https://www.googleapis.com/auth/calendar' and delete any
        stored credentials.
        """
        service = build("calendar", "v3", credentials=creds)

        # calendarIds = "<EMAIL>"

        event = {
               'summary': summary,
               'location': location,
               'description': description,
               'start': {
                 'dateTime': start_date,
                 'timeZone': 'Africa/Lagos',
               },
               
               'end': {
                 'dateTime': end_date,
                 'timeZone': 'Africa/Lagos',
               },
               'recurrence': [
                 'RRULE:FREQ=DAILY;COUNT=2'
               ],
               'attendees': attendees,
              #  "conferenceData": {"createRequest": {"requestId": "sample123", "conferenceSolutionKey": {"type": "hangoutsMeet"}}},
               'reminders': {
                 'useDefault': False,
                 'overrides': [
                   {'method': 'email', 'minutes': 24 * 60},
                   {'method': 'popup', 'minutes': 10},
                 ],
               },
            }
        
        # event = service.events().insert(calendarIds='primary', body=event).execute()
        event = service.events().insert(calendarIds=calendarIds, body=event).execute()
        print('Event created: %s' % (event.get('htmlLink')))
        # res = service.events().insert(calendarIds=calendarIds, sendNotifications=True, body=event, conferenceDataVersion=1).execute()
        # print(res)
    except HttpError as error:
        print(f"An error occurred: {error}")
        
    meeting = {
        "Event": event.get('htmlLink'), 
        # "Res": res,
       }
    return meeting


# def retrieve_event_from_google_calendar(calendarIds:str, num_of_events:int):
def retrieve_event_from_google_calendar(calendarIds:str):
  # If modifying these scopes, delete the file token.json.
  SCOPES = ["https://www.googleapis.com/auth/calendar.readonly"]
  """Shows basic usage of the Google Calendar API.
  Prints the start and name of the next 10 events on the user's calendar.
  """
  creds = None
  # The file token.json stores the user's access and refresh tokens, and is
  # created automatically when the authorization flow completes for the first
  # time.
  if os.path.exists("token.json"):
    creds = Credentials.from_authorized_user_file("token.json", SCOPES)
  # If there are no (valid) credentials available, let the user log in.
  if not creds or not creds.valid:
    if creds and creds.expired and creds.refresh_token:
      creds.refresh(Request())
    else:
      flow = InstalledAppFlow.from_client_secrets_file(
          "credentials.json", SCOPES
      )
      creds = flow.run_local_server(port=0)
    # Save the credentials for the next run
    with open("token.json", "w") as token:
      token.write(creds.to_json())

  try:
    service = build("calendar", "v3", credentials=creds)

    # Call the Calendar API
    now = datetime.datetime.utcnow().isoformat() + "Z"  # 'Z' indicates UTC time
    # print("Getting the upcoming 10 events")
    print("Getting the upcoming events for the week")
    events_result = (
        service.events()
        .list(
            # calendarIds="primary",
            calendarIds=calendarIds,
            timeMin=now,
            maxResults=50,
            # maxResults=num_of_events,
            singleEvents=True,
            orderBy="startTime",
        )
        .execute()
    )
    events = events_result.get("items", [])

    if not events:
      print("No upcoming events found.")
      return

    # events_summary = []
    # Prints the start and name of the next 10 events
    for event in events:
      start = event["start"].get("dateTime", event["start"].get("date"))
      print(start, event["summary"])
      summary = start, event["summary"]
      # events_summary.append(summary)

  except HttpError as error:
    print(f"An error occurred: {error}")

  # return events_summary
  return events


def create_calendar(calendar_title:str):
  #Supplying the required calendar info in request_body

  SCOPES = ["https://www.googleapis.com/auth/calendar"]
  """Shows basic usage of the Google Calendar API.
  Prints the start and name of the next 10 events on the user's calendar.
  """
  creds = None
  # The file token.json stores the user's access and refresh tokens, and is
  # created automatically when the authorization flow completes for the first
  # time.
  if os.path.exists("token.json"):
      creds = Credentials.from_authorized_user_file("token.json", SCOPES)
      
  # If there are no (valid) credentials available, let the user log in.
  flow = InstalledAppFlow.from_client_secrets_file(
      "credentials.json", SCOPES
  )
  if not creds or not creds.valid:
      if creds and creds.expired and creds.refresh_token:
          creds.refresh(Request())
      # else:
      #     flow = InstalledAppFlow.from_client_secrets_file(
      #     "client_secrets.json", SCOPES
      # )
      creds = flow.run_local_server(port=0)
      # Save the credentials for the next run
      with open("token.json", "w") as token:
        token.write(creds.to_json())  

  try:
      service = build("calendar", "v3", credentials=creds)
      request_body = {
      #  'summary': 'SalesOfficerCalendar', #Calendar Title
       'summary': calendar_title, #Calendar Title
       'timeZone': 'Africa/Lagos'
      }

      #returning the request_body in response
      response = service.calendars().insert(body=request_body).execute()
  except HttpError as error:
      print("An error occured:", str(error))

  print("Calendar Created:", str(response))

  return response

# create_calendar()


# def retrieve_calendar(calendar_title:str):
def retrieve_calendar(calendarIds:str):
  #Supplying the required calendar info in request_body

  SCOPES = ["https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/calendar.readonly"]
  """Shows basic usage of the Google Calendar API.
  Prints the start and name of the next 10 events on the user's calendar.
  """
  creds = None
  # The file token.json stores the user's access and refresh tokens, and is
  # created automatically when the authorization flow completes for the first
  # time.
  if os.path.exists("token.json"):
      creds = Credentials.from_authorized_user_file("token.json", SCOPES)
      
  # If there are no (valid) credentials available, let the user log in.
  flow = InstalledAppFlow.from_client_secrets_file(
      "credentials.json", SCOPES
  )
  if not creds or not creds.valid:
      if creds and creds.expired and creds.refresh_token:
          creds.refresh(Request())
      # else:
      #     flow = InstalledAppFlow.from_client_secrets_file(
      #     "client_secrets.json", SCOPES
      # )
      creds = flow.run_local_server(port=0)
      # Save the credentials for the next run
      with open("token.json", "w") as token:
        token.write(creds.to_json())  

  try:
      service = build("calendar", "v3", credentials=creds)

      # returning the request_body in response
      response = service.calendars().get(calendarIds=calendarIds).execute()
  except HttpError as error:
      print("An error occured:", str(error))

  print("Calendar Retrieved:", str(response))

  return response


# def check_free_busy_status(calendarIds, timeMax, timeMin):
def check_free_busy_status(calendarIds, timeMax, timeMin):
    SCOPES = ["https://www.googleapis.com/auth/calendar"]
    
    # Path to service account key JSON file
    SERVICE_ACCOUNT_FILE = "service_account.json"

    if os.path.exists(SERVICE_ACCOUNT_FILE):
      # Create a credentials object using the service account
      creds = service_account.Credentials.from_service_account_file(
          SERVICE_ACCOUNT_FILE, scopes=SCOPES)

    try:
        # Initialize the Calendar API service
        service = build("calendar", "v3", credentials=creds)

        request_body = {
            "timeMin": timeMin,
            "timeMax": timeMax,
            "timeZone": "Africa/Lagos",
            "groupExpansionMax": 100,
            "calendarExpansionMax": 42,
            "items": calendarIds,
        }

        response = service.freebusy().query(body=request_body).execute()
        cache.set(key="free_busy_status", value=response, timeout=60*45)
        
        # results = {}
        # with results_lock:
        #   results.update({"response": response})

        # print("RESPONSE::::", response, "\n\n\n")
        return response
        # return results
    except HttpError as error:
        print("An error occurred while trying to retrieve sales officers' availability.", str(error))
        return f"An error occurred while trying to retrieve sales officers' availability., {str(error)}"
        

# Hook up Google Calendar to Google Meet API 
# service = build("calendar", "v3", credentials=creds)
# calendarIds = "###"
# event = {
#     "start": {"dateTime": "2021-01-01T00:00:00.000+09:00"},
#     "end": {"dateTime": "2021-01-01T00:30:00.000+09:00"},
#     "attendees": [{"email": "###"}],
#     "conferenceData": {"createRequest": {"requestId": "sample123", "conferenceSolutionKey": {"type": "hangoutsMeet"}}},
#     "summary": "sample event with Meet link",
#     "description": "sample description"
# }
# res = service.events().insert(calendarIds=calendarIds, sendNotifications=True, body=event, conferenceDataVersion=1).execute()
# print(res)

# if __name__ == "__main__":
#   retrieve_event_from_google_calendar()

# if __name__ == "__main__":
#   schedule_event_on_google_calendar()