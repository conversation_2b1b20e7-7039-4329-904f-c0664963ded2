from core.tasks import send_email
from performance_sales_metrics_dashboard.utils.zoom_integration import ZoomAPIntegration
from performance_sales_metrics_dashboard.utils.parse_dates import date_parser
from django.core.cache import cache
from .parse_dates import get_time_max
from .google_calendar_api import check_free_busy_status, schedule_event_on_google_calendar
from dateutil import parser
from datetime import timezone
import random


def schedule_demo(timeMin, calendarIds):
    timeMax = get_time_max(timeMin=timeMin)
    start_time = date_parser(date_=timeMin, format_str="%Y-%m-%dT%H:%M:%SZ")

    try:
        free_busy_status = check_free_busy_status(
            calendarIds=calendarIds,
            timeMin=timeMin,
            timeMax=timeMax,
        )
    except Exception as e:
        print("An error occurred while trying to check sales officers' availability.")

    try:
        calendar_status = free_busy_status["calendars"]
        filtered_dict = {email: data for email, data in calendar_status.items() if
                         'errors' not in data and data['busy'] == []}
        dict_key = filtered_dict.keys()
        mail = [key for key in dict_key]

        if len(mail) == 1:
            free_sales_officer = mail[0]
            print("FROM_SINGLE_FREE_SALES_OFFICER::::", free_sales_officer, "\n\n\n")
            cache.set(value=free_sales_officer, key="sales_officer", timeout=60 * 45)
        else:
            free_sales_officers = []
            for i in mail:
                free_sales_officers.append(i)
                num_of_so = len(free_sales_officers)
                random_so = random.randint(1, num_of_so)
                free_sales_officer = free_sales_officers[random_so]
                print("FROM_MULTIPLE_FREE_SALES_OFFICER", free_sales_officer, "\n\n\n")
                cache.set(value=free_sales_officer, key="sales_officer", timeout=60 * 45)
    except Exception as e:
        print("An error occured while trying to schedule demo", str(e))
    
    try:
        cache.set(key="start_time", value=start_time, timeout=60 * 45)
        lead_email = cache.get("company_email")
        sales_officer = cache.get("sales_officer")
        cached_meeting_invitees = [lead_email, sales_officer]
        cache.set(key="attendees", value=cached_meeting_invitees, timeout=60 * 45)

        # meeting_invitees.append(lead_email)
        # meeting_invitees.append(sales_officer)
        meeting_invitees = []
        for meeting_invitee in cached_meeting_invitees:
            meeting_invitees.append({
                "email": meeting_invitee,
                # "email": sales_officer,
            })
        cache.set(key="meeting_invitees", value=meeting_invitees, timeout=60 * 45)
    except Exception as e:
        print("An error occurred while trying to get meeting invitees.")
        
    if not start_time:
        print("You must specify the start time to schedule a zoom meeting.")

    try:
        zoom_meeting_details = ZoomAPIntegration.create_zoom_meeting(
            start_time=start_time,
            meeting_invitees=meeting_invitees,
        )
        cache.set(value=zoom_meeting_details, key="zoom_meeting_details", timeout=60 * 45)
        return zoom_meeting_details
    except Exception as e:
        print("An error occurred while trying to schedule zoom meeting.", str(e))

    try:
        start_date = timeMin
        end_date = get_time_max(start_date)
    except Exception as e:
        print("An error occurred while trying to get end date.", str(e))

    attendees = cache.get("meeting_invitees")
    print("GOOGLE_ATTENDEES::::", attendees, "\n\n\n")
    sales_officer_calendar_id = cache.get("sales_officer")
    lead_email = cache.get("company_email")
    lead_name = cache.get("contact_name")
    zoom_meeting_details = cache.get("zoom_meeting_details")
    lead_zoom_meeting_details = {
        "meeting_topic": zoom_meeting_details["meeting_topic"],
        "meeting_start_time": zoom_meeting_details["meeting_start_time"],
        "meeting_duration": zoom_meeting_details["meeting_duration"],
        "meeting_password": zoom_meeting_details["meeting_password"],
        "meeting_join_url": zoom_meeting_details["meeting_join_url"],
    }
    try:
        zoom_meeting = schedule_event_on_google_calendar(
            attendees=attendees,
            start_date=start_date,
            end_date=end_date,
            calendarIds=sales_officer_calendar_id,
            zoom_meeting_details=zoom_meeting_details,
        )

        send_email.delay(
            recipient=lead_email,
            subject="PayBox360 Demo",
            body=lead_zoom_meeting_details,
            template_dir="zoom_meeting_notification.html",
            lead_name=lead_name,
        )
        
        send_email.delay(
            recipient=sales_officer,
            subject="PayBox360 Demo",
            body=zoom_meeting_details,
            template_dir="zoom_meeting_notification.html",
            lead_name=lead_name,
        )

        print(zoom_meeting)
    except Exception as e:
        print("An error occurred while trying to save meeting details to google calendar.", str(e))