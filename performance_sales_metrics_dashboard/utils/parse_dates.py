from datetime import timedelta, datetime, timezone
from performance_sales_metrics_dashboard.models import Emails
from dateutil import parser

def get_upper_date_limit(date):
    email_range = date - timedelta(days=20)
    return email_range

def get_emails_within_range(date1, date2):
    this_email = Emails.objects.filter(date__date__lte=date1, date__date__gte=date2)
    print("EMAILS::::::::::", this_email)
    return this_email

# timeMin = datetime.strptime(test_timeMin, "%Y-%m-%dT%H:%M:%S.%fZ")

def get_time_max(timeMin):
    current_timezone = datetime.now().astimezone().tzinfo
    timeMin = datetime.strptime(timeMin, "%Y-%m-%dT%H:%M:%S.000Z").replace(tzinfo=current_timezone)
    time_min = timeMin + timedelta(hours=24)
    str_time_min = time_min.strftime("%Y-%m-%dT%H:%M:%S.000Z")
    return str_time_min

def date_parser(date_:str, format_str:str):
    parsed_date = parser.parse(date_)
    utc_date = parsed_date.astimezone(timezone.utc)
    new_date = utc_date.strftime(format_str)
    return new_date