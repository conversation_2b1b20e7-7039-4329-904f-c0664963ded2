from datetime import datetime, timedelta
import random


def generate_start_date():
    now = datetime.now()
    delta = timedelta(days=random.randint(1, 4))
    start_date = now + delta
    formatted_start_date = start_date.strftime("%Y-%m-%dT%H:%M:%S%z")
    return start_date


start_date = generate_start_date()


def generate_end_date(start_date):

    end_date = start_date + timedelta(hours=6)
    formatted_end_date = end_date.strftime("%Y-%m-%dT%H:%M:%S%z")
    # formatted_end_date = datetime.datetime.strptime(formatted_end_date, "%Y-%m-%dT%H:%M:%S")
    return formatted_end_date

    

