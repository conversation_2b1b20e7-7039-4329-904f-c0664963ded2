# from __future__ import print_function
# import os.path
# from google.auth.transport.requests import Request
# from google.oauth2.credentials import Credentials
# from google_auth_oauthlib.flow import InstalledAppFlow, Flow
# from google.apps import meet_v2

# # If modifying these scopes, delete the file token.json.
# SCOPES = ['https://www.googleapis.com/auth/meetings.space.created', 'https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events', 'https://www.googleapis.com/auth/meetings.space.readonly', 'https://www.googleapis.com/auth/drive.readonly']

# # SCOPES = ['https://www.googleapis.com/auth/meetings.space.readonly'] # For GET requests to retrieve a created space


# def main():
#     """Shows basic usage of the Google Meet API.
#     """
#     creds = None
#     # The file token.json stores the user's access and refresh tokens, and is
#     # created automatically when the authorization flow completes for the first
#     # time.
#     if os.path.exists('token.json'):
#         creds = Credentials.from_authorized_user_file('token.json', SCOPES)
#     # If there are no (valid) credentials available, let the user log in.
#     if not creds or not creds.valid:
#         if creds and creds.expired and creds.refresh_token:
#             creds.refresh(Request())
#         else:
#             flow = InstalledAppFlow.from_client_secrets_file(
#                 'credentials.json', SCOPES)
#             creds = flow.run_local_server(port=0)
#         # Save the credentials for the next run
#         with open('token.json', 'w') as token:
#             token.write(creds.to_json())

#     try:
#         client = meet_v2.SpacesServiceClient(credentials=creds)
#         request = meet_v2.CreateSpaceRequest()
#         response = client.create_space(request=request)
#         print(f'Space created: {response.meeting_uri}')
#     except Exception as error:
#         # TODO(developer) - Handle errors from Meet API.
#         print(f'An error occurred: {error}')

#     # google_meet_uri = response.meeting_uri
#     # return google_meet_uri
        
# # main()


# # if __name__ == '__main__':
# #     main()


# # CRUD Ops on spaces
# # Create a meeting space

# async def sample_create_space():
#     # Create a client
#     client = meet_v2.SpacesServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.CreateSpaceRequest(
#     )

#     # Make the request
#     response = await client.create_space(request=request)

#     # Handle the response
#     print(response)


# # Get a meeting space

# async def sample_get_space():
#     # Create a client
#     client = meet_v2.SpacesServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetSpaceRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_space(request=request)

#     # Handle the response
#     print(response)


# # Update a meeting space

# async def sample_update_space():
#     # Create a client
#     client = meet_v2.SpacesServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.UpdateSpaceRequest(
#     )

#     # Make the request
#     response = await client.update_space(request=request)

#     # Handle the response
#     print(response)


# # End active conference
    
# async def sample_end_active_conference():
#     # Create a client
#     client = meet_v2.SpacesServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.EndActiveConferenceRequest(
#         name="name_value",
#     )

#     # Make the request
#     await client.end_active_conference(request=request)


# # Conferences

# # Search for all conferences

# async def sample_list_conference_records():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.ListConferenceRecordsRequest(
#     )

#     # Make the request
#     page_result = client.list_conference_records(request=request)

#     # Handle the response
#     async for response in page_result:
#         print(response)


# # Search for a specific conference

# async def sample_get_conference_record():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetConferenceRecordRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_conference_record(request=request)

#     # Handle the response
#     print(response)


# # Work with participants
    
# # Search for all participants

# async def sample_list_participants():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.ListParticipantsRequest(
#         parent="parent_value",
#     )

#     # Make the request
#     page_result = client.list_participants(request=request)

#     # Handle the response
#     async for response in page_result:
#         print(response)


# # Search for a specific participant

# async def sample_get_participant():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetParticipantRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_participant(request=request)

#     # Handle the response
#     print(response)


# # Participant sessions

# async def sample_list_participant_sessions():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.ListParticipantSessionsRequest(
#         parent="parent_value",
#     )

#     # Make the request
#     page_result = client.list_participant_sessions(request=request)

#     # Handle the response
#     async for response in page_result:
#         print(response)


# # Search for a specific participant session

# async def sample_get_participant_session():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetParticipantSessionRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_participant_session(request=request)

#     # Handle the response
#     print(response)


# # Working with artifacts

# async def sample_list_recordings():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.ListRecordingsRequest(
#         parent="parent_value",
#     )

#     # Make the request
#     page_result = client.list_recordings(request=request)

#     # Handle the response
#     async for response in page_result:
#         print(response)


# # Search for a specific recording

# async def sample_get_recording():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetRecordingRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_recording(request=request)

#     # Handle the response
#     print(response)


# # Transcripts

# # Search for all transcripts

# async def sample_list_transcripts():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.ListTranscriptsRequest(
#         parent="parent_value",
#     )

#     # Make the request
#     page_result = client.list_transcripts(request=request)

#     # Handle the response
#     async for response in page_result:
#         print(response)


# # Search for a specific transcript

# async def sample_get_transcript():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetTranscriptRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_transcript(request=request)

#     # Handle the response
#     print(response)


# # Transcript entries
    
# # Search for all transcript entries

# async def sample_list_transcript_entries():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.ListTranscriptEntriesRequest(
#         parent="parent_value",
#     )

#     # Make the request
#     page_result = client.list_transcript_entries(request=request)

#     # Handle the response
#     async for response in page_result:
#         print(response)


# # Search for a specific transcript entry

# async def sample_get_transcript_entry():
#     # Create a client
#     client = meet_v2.ConferenceRecordsServiceAsyncClient()

#     # Initialize request argument(s)
#     request = meet_v2.GetTranscriptEntryRequest(
#         name="name_value",
#     )

#     # Make the request
#     response = await client.get_transcript_entry(request=request)

#     # Handle the response
#     print(response)

    

