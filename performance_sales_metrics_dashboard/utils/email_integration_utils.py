# import imaplib
# import email
# from email.header import decode_header
# from django.utils import timezone
# from performance_sales_metrics_dashboard.models import Emails, Attachment

# def sync_imap(username:str, password:str):
#     try:
#         with imaplib.IMAP4_SSL("imap.outlook.com") as imap:
#             imap.login(username, password)

#             # Load emails
#             for i in imap.list()[1]:
#                 l = i.decode().split(' "/" ')
#                 print(l[0] + " = " + l[1])

#             imap.select("Inbox")
#             _, message_numbers = imap.search(None, "ALL")
#             # status, messages = imap.search(None, 'UNSEEN') *from retrieve_emails.py

#             # Iterating over the messages and retrieving their content
#             # for num in messages[0].split()[::-1]:*from retrieve_emails.py
#             for num in message_numbers[0].split():
#                 _, msg_data = imap.fetch(num, '(RFC822)')
#                 email_body = msg_data[0][1]
#                 email_message = email.message_from_bytes(email_body)

#                 subject, encoding = decode_header(email_message['Subject'])
#                 if isinstance(subject, bytes):
#                     subject = subject.decode(encoding or "utf-8")

#                 sender = email.utils.parseaddr(email_message["From"])[1]
#                 date = email.utils.parsedate_to_datetime(email_message["Date"])

#                 Emails.objects.get_or_create(
#                     # account=account,
#                     message_id=email_message['Message-ID'],
#                     defaults={
#                         "subject": subject,
#                         "sender": sender,
#                         "recipients": email_message['To'],
#                         "date": date,
#                         "body": get_email_body(email_message)
#                     }
#                 )
        
#         return True, 'IMAP sync successful'
#     except Exception as e:
#         return False, str(e)

# def get_email_body(email_message):
#     if email_message.is_multipart():
#         for part in email_message.walk():
#             return part.get_payload(decode=True).decode()
#     else:
#         return email_message.get_payload(decode=True).decode()


# def get_header_value(headers, name):
#     for header in headers:
#         if header['name'].lower() == name.lower():
#             return header['value']
#         return ''
    
