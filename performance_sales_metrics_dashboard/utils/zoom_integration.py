from decouple import config
import json
import requests
from django.core.cache import cache
import base64
from ..utils import google_calendar_api
# from zoomus import ZoomClient
# from time import time

ZOOM_ACCOUNT_ID = config("ZOOM_ACCOUNT_ID")
ZOOM_CLIENT_ID = config("ZOOM_CLIENT_ID")
ZOOM_CLIENT_SECRET = config("ZOOM_CLIENT_SECRET")
ZOOM_SDK_CLIENT_ID = config("ZOOM_SDK_CLIENT_ID")
ZOOM_SDK_CLIENT_SECRET = config("ZOOM_SDK_CLIENT_SECRET")
TOKEN_URL = config("TOKEN_URL")
ZOOM_URL = config("ZOOM_URL")
EMPTY_STR = ""

class ZoomAPIntegration:
    STATUS_CODE = 200
    ZOOM_CLIENT_ID = config("ZOOM_CLIENT_ID")
    ZOOM_CLIENT_SECRET = config("ZOOM_CLIENT_SECRET")
    TOKEN_URL = config("TOKEN_URL")
    ZOOM_URL = config("ZOOM_URL")

    @staticmethod
    def internal_server_error(error):
        """
        Returns an error response for internal server errors.
        """
        return {
            "error": {
                "code": 500, 
                "message": "Internal Server Error",
                "details": str(error), 
            }
        }
    
    @classmethod
    def generate_bearer_token(self, *args, **kwargs):
        credentials = base64.b64encode(f"{ZOOM_CLIENT_ID}:{ZOOM_CLIENT_SECRET}".encode()).decode()
        token_url = f"https://zoom.us/oauth/token?grant_type=account_credentials&account_id={ZOOM_ACCOUNT_ID}"
        headers = {
            'Authorization': f'Basic {credentials}',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        response = requests.post(token_url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            bearer_access_token = data["access_token"]
            cache.set(key="bearer_token", value=bearer_access_token, timeout=60*45)
            cached_token = cache.get("bearer_token")

        else:
            raise Exception(f"Error fetching bearer token: {response.text}")

        return {
            "bearer_token": bearer_access_token,
            "cached_bearer_token": cached_token
        }

    
    @classmethod
    def generate_access_token(self, *args, **kwargs):
        string_value = f"{ZOOM_CLIENT_ID}:{ZOOM_CLIENT_SECRET}"
        auth_token = base64.b64encode(string_value.encode("ascii")).decode("utf-8")

        headers = {
            "Authorization": f"Basic {auth_token}",
            "Content-Type": "application/x-www-form-urlencoded",
        }

        # data = f"https://zoom.us/oauth/token?grant_type=account_credentials&account_id=[{ZOOM_ACCOUNT_ID}]"

        body = {
            "grant_type": "account_credentials",
            "account_id": ZOOM_ACCOUNT_ID,
            }
        
        response = requests.post('https://zoom.us/oauth/token', headers=headers, data=body)

        data = response.json()

        access_token = data["access_token"]
        # token_type = data["token_type"]
        # expires_in = data["expires_in"]
        # scope = data["scope"]
        # api_url = data["api_url"]

        if response.status_code == 200 or 201:
            cache.set(key="access_token", value=access_token, timeout=60*45)
            cached_token = cache.get("access_token")
            # print(response.text)
            return {
                "access_token": access_token,
                "cached_access_token": cached_token,
                # "token_type": token_type,
                # "expires_in": expires_in,
                # "scope" : scope,
                # "api_url": api_url,
            }
        else: 
            raise Exception(f"Error generating access token {response.text}")
    

    @classmethod
    def get_cached_access_token(cls):
        cls.generate_access_token()
        cached_token = cache.get("access_token")

        if cached_token:
            return cached_token
        else: 
            return "\n\n\nCached access token is required!\n\n\n"
        

    @classmethod
    def get_cached_bearer_token(cls):
        cls.generate_bearer_token()
        cached_token = cache.get("bearer_token")

        if cached_token:
            return cached_token      
        else:
            print("\n\n\nCached bearer token is required!\n\n\n")
    
    @classmethod
    def create_zoom_meeting(
        self,
        start_time:str,
        meeting_invitees:list,
        duration=60,
        timezone="Africa/Lagos",
        password="12345678",
        topic="PayBox360 Demo",
        agenda="Product demo with client",
        default_password:bool=False,
        pre_schedule:bool=False,
    ):
        url = f"{self.ZOOM_URL}/users/me/meetings"
        token = self.get_cached_access_token()
        # token = self.generate_access_token()

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }

        payload = json.dumps(
            {
                "agenda": agenda,
                "default_password": default_password,
                "duration": duration,
                "password": password,
                "pre_schedule": pre_schedule,
                "topic": topic,
                "start_time": start_time,
                "timezone": timezone,
                "meeting_invitees": meeting_invitees,
                # "schedule_for": "<EMAIL>",
                # "settings": {
                #     "authentication_exception": [
                #             {
                #                 "email": "<EMAIL>",
                #                 "name": "Jill Chill"
                #             }
                #         ],
                #     },
                # "contact_email": "<EMAIL>",
                # "contact_name": "Jill Chill",
            }
        )

        response = requests.post(url, headers=headers, data=payload)
        json_response = response.json()

        meeting_uuid = json_response["uuid"]
        meeting_id = json_response["id"]
        meeting_topic = json_response["topic"]
        meeting_start_time = json_response["start_time"]
        meeting_duration = json_response["duration"]
        meeting_agenda = json_response["agenda"]
        meeting_password = json_response["password"]
        meeting_start_url = json_response["start_url"]
        meeting_join_url = json_response["join_url"]

        zoom_meeting_details = {
            "meeting_uuid": meeting_uuid,
            "meeting_id": meeting_id,
            "meeting_topic": meeting_topic,
            "meeting_start_time": meeting_start_time,
            "meeting_duration": meeting_duration,
            "meeting_agenda": meeting_agenda,
            "meeting_password": meeting_password,
            "meeting_start_url": meeting_start_url,
            "meeting_join_url": meeting_join_url,
        }
        
        dumped_zoom_meeting = json.dumps(zoom_meeting_details)
        dumped_zoom_meeting = json.loads(dumped_zoom_meeting)
        
        try:
            if response.status_code == 200 or response.status_code == 201:
                # return response.json()
                return dumped_zoom_meeting
        except Exception as e:
            return ("An error occured", str(e))
    

    @classmethod
    def get_zoom_meeting(cls, meetingId):
        url = f"{cls.ZOOM_URL}/meetings/{meetingId}"
        payload = EMPTY_STR
        token = cls.get_cached_bearer_token()
        
        headers = {"Authorization": f"Bearer {token}"}

        response = requests.request("GET", url, headers=headers, data=payload)
        response.raise_for_status()
        data = response.json()
        meeting_uuid = data["uuid"]
        meeting_id = data["id"]
        meeting_topic = data["topic"]
        meeting_start_time = data["start_time"]
        meeting_duration = data["duration"]
        meeting_agenda = data["agenda"]
        meeting_password = data["password"]
        meeting_start_url = data["start_url"]
        meeting_join_url = data["join_url"]

        zoom_meeting_details = {
            "meeting_uuid": meeting_uuid,
            "meeting_id": meeting_id,
            "meeting_topic": meeting_topic,
            "meeting_start_time": meeting_start_time,
            "meeting_duration": meeting_duration,
            "meeting_agenda": meeting_agenda,
            "meeting_password": meeting_password,
            "meeting_start_url": meeting_start_url,
            "meeting_join_url": meeting_join_url,
        }
        
        dumped_zoom_meeting = json.dumps(zoom_meeting_details)
        dumped_zoom_meeting = json.loads(dumped_zoom_meeting)

        try:
            if response.status_code == 200:
                # return response.json()
                return dumped_zoom_meeting
        except Exception as e:
            return ("An error occured", str(e))
        # except (requests.exceptions.RequestException, ValueError) as err:
        #     return cls.internal_server_error(error=err)
        
    @classmethod
    def check_schedule(cls, timeMin, timeMax, calendars, ):

        token = cls.get_cached_bearer_token()
        url = "https://api.zoom.us/v2/calendars/freeBusy"

        payload = {
            "timeMin": timeMin,
            "timeMax": timeMax,
            "calendarExpansionMax": 50,
            "groupExpansionMax": 100,
            "items": [calendars],
            # "items": [{ "id": "<EMAIL>" }]
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }

        response = requests.post(url, json=payload, headers=headers)
        try:
            if response.status_code == 200:
                return response.json()
            return response.json()
        except Exception as e:
            return ("An error occured", str(e))
        
   
# def create_zoom_meeting(payload):
#     """https://developers.zoom.us/docs/meeting-sdk/apis/#operation/meetingCreate"""
#     client = ZoomClient(client_id=ZOOM_CLIENT_ID, client_secret=ZOOM_CLIENT_SECRET, api_account_id=ZOOM_ACCOUNT_ID)
#     response = client.meeting.create(**payload)
#     return response.json()


# def get_zoom_meeting(meetingId):
#     client = ZoomClient(ZOOM_CLIENT_ID, ZOOM_CLIENT_SECRET, api_account_id=ZOOM_ACCOUNT_ID)

#     url = f"https://api.zoom.us/v2/meetings/{meetingId}"

#     cached_token = cache.get("access_token")
    
#     headers = {"Authorization": f"Bearer {cached_token}"}

#     response = requests.get(url, headers=headers)

#     print(response.json())