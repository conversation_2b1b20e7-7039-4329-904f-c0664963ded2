import codecs
import getpass, poplib
from bs4 import BeautifulSoup
from email import message_from_bytes
from email.utils import parsedate_to_datetime
import quopri, gzip
import getpass, poplib
from email.parser import Parser
from email.header import decode_header
import chardet
from io import BytesIO
from decouple import config
import logging

logger = logging.getLogger(__name__)

# Define email server and credentials
POP_SERVER = "mail.libertyng.com"

def get_emails_from_server(username:str, password:str):
# def get_emails_from_server():
    # Connect to the mail server
    try:
        Mail = poplib.POP3_SSL(POP_SERVER)
    except poplib.error_proto as e:
        logger.error("AN ERROR OCCURRED WHILE TRYING TO CONNECT TO THE POP3 MAIL SERVER::::::", str(e))
    except Exception as e:
        logger.exception("AN ERROR OCCURRED WHILE TRYING TO CONNECT TO THE POP3 MAIL SERVER::::::")

    # Get message header
    # message_header = Mail.top()

    # Log in with credentials
    try:
        Mail.user(username)
        Mail.pass_(password)
    except poplib.error_proto as e:
        logger.error("AN ERROR OCCURRED WHILE TRYING TO LOGIN WITH CREDENTIALS::::::", str(e))
    except Exception as e:
        logger.exception("AN ERROR OCCURRED WHILE TRYING TO LOGIN WITH CREDENTIALS::::::")

    # Get the number of messages
    try:
        num_messages = len(Mail.list()[1])
    except poplib.error_proto as e:
        logger.error("AN ERROR OCCURRED WHILE TRYING TO GET THE NUMBER OF MESSAGES::::::", str(e))
    except Exception as e:
        logger.exception("AN ERROR OCCURRED WHILE TRYING TO GET THE NUMBER OF MESSAGES::::::")

    # List to hold the structured emails
    emails = []

    # Loop through the messages
    for i in range(num_messages):
        try:
            # Retrieve the message
            response, lines, octets = Mail.retr(i + 1)
        except poplib.error_proto as e:
            logger.error("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE THE EMAIL MESSAGE::::::", str(e))
        except Exception as e:
            logger.exception("AN ERROR OCCURRED WHILE TRYING TO RETRIEVE THE EMAIL MESSAGE::::::")

        # Get the UID for the specific email (use i+1 to get UID for that email)
        uid_response = Mail.uidl(i + 1)
        email_uid = uid_response.decode('utf-8').split()[2]  # Extract the UID only (2nd item)

        # Join the lines and convert to a single byte string
        msg_content = b'\n'.join(lines)

        # Parse the email content into an email message object
        email_message = message_from_bytes(msg_content)

        email_date = email_message['Date']

        email_sender = email_message['From']

        if '<' in email_sender:
            sender_name = email_sender.split('<')[0].strip()
        else:
            sender_name = email_sender 

        email_subject = email_message['Subject']

        receiver_email = email_message['To']

        cc = email_message.get('Cc')
        
        bcc = email_message.get('Bcc')

        # Convert the date string into a datetime object
        try:
            email_datetime = parsedate_to_datetime(email_date)
        except Exception as e:
            email_datetime = None  # Handle emails without proper date headers

        # Initialize a variable for storing the decoded content
        text_content = ""

        # Get the payload (content of the email)
        if email_message.is_multipart():
            # If the message is multipart, get the payload
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_transfer_encoding = part.get('Content-Transfer-Encoding', '').lower()

                # Check if the part is HTML or plain text
                if content_type == 'text/html' or content_type == 'text/plain':
                    payload = part.get_payload(decode=True)

                    # Decode content based on transfer encoding
                    if content_transfer_encoding == 'quoted-printable':
                        byte_payload = bytes(payload)
                        payload = byte_payload.decode("latin-1")
                        # codecs_payload = codecs.encode(str_payload)
                        # codecs_payload = codecs.decode(codecs_payload)
                        # payload = quopri.decodestring(str_payload).decode('utf-8')
                    elif content_transfer_encoding == 'base64':
                        # Convert payload to bytes
                        byte_payload = bytes(payload)
                        payload = codecs.decode(byte_payload, "utf-8")
                        # payload = payload.decode('utf-8')  # Already decoded as base64 by default
                        # payload = decoded_byte_payload.decode('utf-8')  # Already decoded as base64 by default
                    else:
                        payload = payload.decode('utf-8')

                    # Parse HTML if it's an HTML part
                    if content_type == 'text/html':
                        soup = BeautifulSoup(payload, 'html.parser')
                        text_content = soup.get_text()
                    else:
                        text_content = payload
                    break
        else:
            # For non-multipart emails, decode the payload directly
            payload = email_message.get_payload(decode=True)
            content_transfer_encoding = email_message.get('Content-Transfer-Encoding', '').lower()

            if content_transfer_encoding == 'quoted-printable':
                try:
                    payload = quopri.decodestring(payload).decode('utf-8')
                except UnicodeDecodeError:
                    continue
            elif content_transfer_encoding == 'base64':
                payload = payload.decode('utf-8')
            else:
                continue

            text_content = payload

        # Store the email date, content, and ID (UID)
        emails.append({
            'ID': email_uid,
            'date': email_datetime,
            'subject': email_subject,
            'sender': email_sender,
            'senders_name': sender_name,
            'cc': cc,
            'bcc': bcc,
            'receiver': receiver_email,
            'content': text_content,
        })

    # Sort emails by date (ascending)
    emails_sorted = sorted(emails, key=lambda x: x['date'] if x['date'] else '')

    mails_box = []
    for email in emails_sorted:

        line = '-' * 40
        mail_box = {
            "ID": {email["ID"]},
            "Date": {email["date"]},
            "Subject": {email["subject"]},
            "Sender": {email["sender"]},
            "Senders_Name": {email["senders_name"]},
            "Cc": {email["cc"]},
            "Bcc": {email["bcc"]},
            "Receiver": {email["receiver"]},
            "Content": {email["content"]},
            "-": line
        }

        mails_box.append(mail_box)

    # Close the connection
    Mail.quit()
    
    return mails_box

# get_emails_from_server()

# Exception
# exception poplib.error_proto
# SET_DEBUG_LEVEL=Mail.set_debuglevel(2)
# QUERY_SERVER_CAP=Mail.capa()
# GET_MAILBOX_STATUS=Mail.stat()
# GET_MSG_LIST=Mail.list([which])
# GET_MSG_NUM=Mail.retr(which)
# RMV_DEL_MARKS=Mail.rset()
# STAY_IDLE=Mail.noop()
# TOP=Mail.top(which, howmuch)
# Mail.user(getpass.getuser())
# Mail.pass_(getpass.getpass())
# UID=Mail.uidl(which=None)
    
