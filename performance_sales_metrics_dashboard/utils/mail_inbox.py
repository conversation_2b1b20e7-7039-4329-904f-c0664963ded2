from requisition.helpers.enums import User<PERSON><PERSON>
from requisition.models import TeamMember
from ..models import Emails

def save_to_inbox(
        is_inbox, 
        sales_officer, 
        date, 
        subject,
        sender, 
        senders_name,
        receiver, 
        content, 
    ):
    try:
        date_sent = Emails.objects.get(date=date)
        Emails.objects.get(date=date_sent.date)
            # continue
    # except Emails.DoesNotExist:
    except:
        sales_officer_instance = TeamMember.objects.get(
                id=sales_officer, role=UserRole.SALES_OFFICER
            )
        Emails.save_as_inbox(
            is_inbox=is_inbox,
            sales_officer=sales_officer_instance, 
            date=date, 
            subject=subject, 
            sender=sender,
            senders_name=senders_name,
            recipients=receiver, 
            content=content, 
        )
