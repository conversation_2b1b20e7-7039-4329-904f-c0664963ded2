from django.contrib.auth import get_user_model
from django.urls import resolve, reverse
from performance_sales_metrics_dashboard.views import LeadAPIView, PipelineAPIView, StagesAPIView, \
    ActivityAPIView, RecentAndUpcomingTasksView, TaskAPIView, GetStagesByPipeline, \
    QualifyLeadAPIView, MoveLeadToPipelineAPIView, MoveLeadToNextStageInPipelineAPIView, \
    LeadProgression, CategoryAPIView, GetCategoryAPIView, GetLeadsNotInAPipeline, \
    SalesOfficerUploadLeadsViaFileAPIView, LeadStatusAPIView, GetLeadsByStage \
    # ProspectAPIView, GetPipelineBySalesOfficer,, SumLeadsInStage
from rest_framework.test import APITestCase

# Create your tests here.

User = get_user_model()


class TestUrls(APITestCase):

    # def test_metric_urls(self):
    # url = reverse('calculate_sales_officer_metrics')
    #
    # url = reverse('calculate_sales_lead_metrics')
    #
    # url = reverse('calculate_merchant_metrics')
    #
    # url = reverse('calculate_system_usage_metrics')
    #
    # url = reverse('sales_lead_onboard')
    #
    # url = reverse('sales_officer_onboard')
    #
    # url = reverse('sales_officer_onboard_company')
    #

    def test_lead_urls(self):
        url = reverse('create_lead')
        self.assertEquals(resolve(url).func.view_class, LeadAPIView)

        url = reverse('edit_lead', args=(1,))
        self.assertEquals(resolve(url).func.view_class, LeadAPIView)

        url = reverse('get_a_lead')
        self.assertEquals(resolve(url).func.view_class, LeadAPIView)

        url = reverse('get_leads_not_in_a_pipeline')
        self.assertEquals(resolve(url).func.view_class, GetLeadsNotInAPipeline)

        url = reverse('get_stage_leads')
        self.assertEquals(resolve(url).func.view_class, GetLeadsByStage)

        url = reverse('get_multiple_leads')
        self.assertEquals(resolve(url).func.view_class, LeadAPIView)

        url = reverse('lead_status_stats')
        self.assertEquals(resolve(url).func.view_class, LeadStatusAPIView)

        url = reverse('lead_upload_by_so')
        self.assertEquals(resolve(url).func.view_class, SalesOfficerUploadLeadsViaFileAPIView)

        url = reverse('lead_upload_by_sl')
        self.assertEquals(resolve(url).func.view_class, SalesOfficerUploadLeadsViaFileAPIView)

    def test_pipeline_urls(self):
        url = reverse('create_pipeline')
        self.assertEquals(resolve(url).func.view_class, PipelineAPIView)

        url = reverse('edit_pipeline', args=(1,))
        self.assertEquals(resolve(url).func.view_class, PipelineAPIView)

        url = reverse('get_a_pipeline')
        self.assertEquals(resolve(url).func.view_class, PipelineAPIView)

        url = reverse('get_multiple_pipelines')
        self.assertEquals(resolve(url).func.view_class, PipelineAPIView)

        url = reverse('delete_pipeline')
        self.assertEquals(resolve(url).func.view_class, PipelineAPIView)

    def test_stage_urls(self):
        url = reverse('create_stage')
        self.assertEquals(resolve(url).func.view_class, StagesAPIView)

        url = reverse('edit_stage', args=(1,))
        self.assertEquals(resolve(url).func.view_class, StagesAPIView)

        url = reverse('get_a_stage')
        self.assertEquals(resolve(url).func.view_class, StagesAPIView)

        url = reverse('pipeline_stages')
        self.assertEquals(resolve(url).func.view_class, GetStagesByPipeline)

        url = reverse('get_multiple_stages')
        self.assertEquals(resolve(url).func.view_class, StagesAPIView)

        url = reverse('delete_stage')
        self.assertEquals(resolve(url).func.view_class, StagesAPIView)

    def test_activity_urls(self):
        url = reverse('create_activity')
        self.assertEquals(resolve(url).func.view_class, ActivityAPIView)

        url = reverse('edit_activity', args=(1,))
        self.assertEquals(resolve(url).func.view_class, ActivityAPIView)

        url = reverse('get_activity')
        self.assertEquals(resolve(url).func.view_class, ActivityAPIView)

        url = reverse('get_multiple_activity')
        self.assertEquals(resolve(url).func.view_class, ActivityAPIView)

        url = reverse('delete_activity')
        self.assertEquals(resolve(url).func.view_class, ActivityAPIView)

    def test_task_urls(self):
        url = reverse('create_task')
        self.assertEquals(resolve(url).func.view_class, TaskAPIView)

        url = reverse('edit_task', args=(1,))
        self.assertEquals(resolve(url).func.view_class, TaskAPIView)

        url = reverse('get_a_task')
        self.assertEquals(resolve(url).func.view_class, TaskAPIView)

        url = reverse('activity_for_last_seven_days')
        self.assertEquals(resolve(url).func.view_class, RecentAndUpcomingTasksView)

        url = reverse('get_multiple_tasks')
        self.assertEquals(resolve(url).func.view_class, TaskAPIView)

        url = reverse('delete_task')
        self.assertEquals(resolve(url).func.view_class, TaskAPIView)

    def test_extra_urls(self):
        url = reverse('qualify_lead')
        self.assertEquals(resolve(url).func.view_class, QualifyLeadAPIView)

        url = reverse('move_lead_to_pipeline')
        self.assertEquals(resolve(url).func.view_class, MoveLeadToPipelineAPIView)

        url = reverse('move_lead_to_next_stage_in_pipeline')
        self.assertEquals(resolve(url).func.view_class, MoveLeadToNextStageInPipelineAPIView)

        url = reverse('move_deal')
        self.assertEquals(resolve(url).func.view_class, LeadProgression)

    def test_category_urls(self):
        url = reverse('create_category')
        self.assertEquals(resolve(url).func.view_class, CategoryAPIView)

        url = reverse('edit_category', args=(1,))
        self.assertEquals(resolve(url).func.view_class, CategoryAPIView)

        url = reverse('get_category')
        self.assertEquals(resolve(url).func.view_class, GetCategoryAPIView)

        url = reverse('get_multiple_categories')
        self.assertEquals(resolve(url).func.view_class, GetCategoryAPIView)

        url = reverse('delete_category')
        self.assertEquals(resolve(url).func.view_class, CategoryAPIView)
