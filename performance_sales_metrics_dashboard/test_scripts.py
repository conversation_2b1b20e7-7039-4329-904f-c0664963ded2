from celery import shared_task
from django.utils import timezone
from core.tasks import send_email

recipient="<EMAIL>"
subject="Paybox360 demo reminder"
message="Testing testing"
template_dir="meeting_reminder.html"
scheduled_time="2025-02-03 15:00:00"

@shared_task
# def send_reminder_emails(subject, recipient, message, scheduled_time:str, template_dir="meeting_reminder.html", **kwargs):
def send_reminder_emails(**kwargs):
    import datetime as dt
    from datetime import datetime as dt_time
    current_timezone = dt.datetime.now().astimezone().tzinfo
    current_time = timezone.now()
    updated_scheduled_time = dt_time.strptime(scheduled_time, "%Y-%m-%d %H:%M:%S").replace(tzinfo=current_timezone)
    thirty_minutes = dt.timedelta(minutes=3)
    ten_minutes = dt.timedelta(minutes=1)
    thirty_minutes_timer = updated_scheduled_time - thirty_minutes
    print("THIRTY_MINUTES_TIMER:::::::::", thirty_minutes_timer, "\n\n\n\n")
    ten_minutes_timer = updated_scheduled_time - ten_minutes
    print("TEN_MINUTES_TIMER:::::::::", ten_minutes_timer, "\n\n\n\n")
    # delay = (updated_scheduled_time - current_time)
    # new_scheduled_time = delay.total_seconds()
    thirty_minutes_countdown = (thirty_minutes_timer - current_time).total_seconds()
    ten_minutes_countdown = (ten_minutes_timer - current_time).total_seconds()
    try:
        send_email.apply_async(
            args=[
                recipient,
                subject,
                message,
                template_dir,
                scheduled_time,
            ],
            # **kwargs,
            countdown=thirty_minutes_countdown
        )
    except Exception as e:
        print(f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND REMINDER EMAIL, {str(e)}")
    try:
        send_email.apply_async(
            args=[
                recipient,
                subject,
                message,
                template_dir,
                scheduled_time,
            ],
            **kwargs,
            countdown=ten_minutes_countdown
        )
    except Exception as e:
        print(f"AN ERROR OCCURRED WHILE ATTEMPTING TO SEND REMINDER EMAIL, {str(e)}")