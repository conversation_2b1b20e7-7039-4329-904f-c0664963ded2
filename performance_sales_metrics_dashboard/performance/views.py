from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from performance_sales_metrics_dashboard.performance import utils
from requisition.models import Company


# from .utils import calculate_merchant_metrics, calculate_sales_officer_metrics, calculate_sales_lead_metrics, calculate_system_metrics


class SalesOfficerMetricsAPIView(APIView):
    def get(self, request, *args, **kwargs):
        filters = request.query_params
        metrics = utils.calculate_sales_officer_metrics(filters)
        return Response(metrics, status=status.HTTP_200_OK)


class SalesLeadMetricsAPIView(APIView):
    def get(self, request, *args, **kwargs):
        metrics = utils.calculate_sales_lead_metrics()
        return Response(metrics, status=status.HTTP_200_OK)


class MerchantMetricsAPIView(APIView):
    def get(self, request, *args, **kwargs):
        merchants = Company.objects.all()
        metrics = utils.calculate_merchant_metrics(merchants)
        return Response(metrics, status=status.HTTP_200_OK)



class SystemUsageMetricsView(APIView):
    def get(self, request, *args, **kwargs):
        metrics = utils.calculate_system_metrics()
        return Response(metrics, status=status.HTTP_200_OK)
