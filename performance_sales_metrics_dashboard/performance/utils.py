from performance_sales_metrics_dashboard import models
from decimal import Decimal
from django.conf import settings
# from datetime import date
# import json
# from django.http import JsonResponse
from django.db.models import F, Q, Sum, FloatField, DecimalField, Avg, Case, Count, When, Value, Subquery, OuterRef
from django.db.models.functions import Coalesce, Cast
from collections import defaultdict
from django.core.exceptions import FieldError
from datetime import date, timedelta
import datetime
from django.conf import settings
from requisition import models as req
from account import models as acc
from performance_sales_metrics_dashboard import models as psmd
from subscription_and_invoicing import models as subscription
from django.utils import timezone


def get_merchants_by_referral_code(referral_code):
    try:
        return models.Company.objects.filter(sales_officer__referral_code=referral_code)
    except models.Company.DoesNotExist:
        return None


def update_revenue_lines_from_transactions():
    transactions = models.Transaction.objects.filter(status='SUCCESS')

    for txn in transactions:
        # Assume RevenueLine model exists
        revenue_line, created = models.RevenueLines.objects.get_or_create(
            company=txn.user.company,  # or whichever field you need to filter by
            transaction=txn,
            defaults={
                'revenue': txn.amount,
                'commission': txn.commission,
                'payout_type': txn.payout_type,
                'date': txn.date_created,
            }
        )

        if not created:
            # Update the existing record if necessary
            revenue_line.revenue += txn.amount
            revenue_line.commission += txn.commission
            revenue_line.save()


def calculate_sales_officer_metrics(filters=None):
    if filters is None:
        filters = {}

    sales_officers = models.SalesOfficer.objects.all()

    # Apply filters to SalesOfficers if any
    if 'name__icontains' in filters:
        sales_officers = sales_officers.filter(name__icontains=filters['name__icontains'])
    if 'revenue_generated__gte' in filters:
        sales_officers = sales_officers.filter(revenue_generated__gte=filters['revenue_generated__gte'])
    if 'revenue_generated__lte' in filters:
        sales_officers = sales_officers.filter(revenue_generated__lte=filters['revenue_generated__lte'])
    if 'total_transaction_volume__gte' in filters:
        sales_officers = sales_officers.filter(total_transaction_volume__gte=filters['total_transaction_volume__gte'])
    if 'total_transaction_volume__lte' in filters:
        sales_officers = sales_officers.filter(total_transaction_volume__lte=filters['total_transaction_volume__lte'])
    if 'customer_satisfaction_score__gte' in filters:
        sales_officers = sales_officers.filter(
            customer_satisfaction_score__gte=filters['customer_satisfaction_score__gte'])
    if 'customer_satisfaction_score__lte' in filters:
        sales_officers = sales_officers.filter(
            customer_satisfaction_score__lte=filters['customer_satisfaction_score__lte'])

    results = []

    for officer in sales_officers:
        merchants = get_merchants_by_referral_code(officer.referral_code)
        if merchants is None:
            continue

        # Calculate aggregated metrics for merchants
        merchants_aggregated = merchants.aggregate(
            total_merchants_acquired=Count('id'),
            total_revenue=Coalesce(Sum('revenue', output_field=DecimalField()), Decimal('0.0')),
            total_active_merchants=Count('id', filter=Q(subscription_status=True)),
            total_transaction_volume=Coalesce(Sum('transaction_volume', output_field=FloatField()), 0.0),
            total_subscription_value=Coalesce(
                Sum(
                    Case(
                        When(subscription_plan='AN', then=Value(150000)),
                        When(subscription_plan='BI', then=Value(75000)),
                        When(subscription_plan='QU', then=Value(37500)),
                        default=Value(0),
                        output_field=DecimalField()
                    )
                ),
                Decimal('0.0')
            ),
            total_subscription_volume=Count('id'),
            total_customer_satisfaction=Coalesce(
                Avg('sales_officer__customer_satisfaction_score', output_field=FloatField()), 0.0)
        )

        # Extract individual merchant metrics
        merchant_metrics = calculate_merchant_metrics(merchants)

        # Sales officer metrics
        total_merchants_acquired = merchants_aggregated['total_merchants_acquired']
        total_revenue = merchants_aggregated['total_revenue']
        total_active_merchants = merchants_aggregated['total_active_merchants']
        total_transaction_volume = merchants_aggregated['total_transaction_volume']
        total_subscription_value = merchants_aggregated['total_subscription_value']
        total_subscription_volume = merchants_aggregated['total_subscription_volume']
        total_customer_satisfaction = merchants_aggregated['total_customer_satisfaction']

        total_inactive_merchants = total_merchants_acquired - total_active_merchants
        active_merchants_percentage = (
                    total_active_merchants / total_merchants_acquired * 100) if total_merchants_acquired else 0.0
        inactive_merchants_percentage = (
                    total_inactive_merchants / total_merchants_acquired * 100) if total_merchants_acquired else 0.0
        active_vs_inactive_comparison = f"{total_active_merchants}:{total_inactive_merchants}"
        average_customer_satisfaction_score = total_customer_satisfaction
        average_revenue_generated = total_revenue / total_merchants_acquired if total_merchants_acquired else 0.0
        overall_average_revenue_generated = total_revenue / total_merchants_acquired if total_merchants_acquired > 0 else 0.0

        if total_merchants_acquired < 7:
            merchant_acquisition_status = 'Below Average'
        elif total_merchants_acquired < 10:
            merchant_acquisition_status = 'Average'
        else:
            merchant_acquisition_status = 'Above Average'

        merchant_acquisition_percentage_ratio = (total_merchants_acquired / 10) * 100

        officer.revenue_generated = float(total_revenue)
        officer.overall_revenue_generated = float(total_revenue)
        officer.average_revenue_generated = float(average_revenue_generated)
        officer.overall_average_revenue_generated = float(overall_average_revenue_generated)
        officer.total_transaction_volume = total_transaction_volume
        officer.total_subscription_volume = total_subscription_volume
        officer.total_subscription_value = float(total_subscription_value)
        officer.active_merchants_count = total_active_merchants
        officer.inactive_merchants_count = total_inactive_merchants
        officer.customer_satisfaction_score = average_customer_satisfaction_score
        officer.merchant_acquisition_status = merchant_acquisition_status
        officer.merchant_acquisition_percentage_ratio = merchant_acquisition_percentage_ratio
        officer.save()

        results.append({
            'sales_officer_name': officer.name,
            'total_merchants_acquired': total_merchants_acquired,
            'total_revenue_generated': float(total_revenue),
            'total_transaction_volume': total_transaction_volume,
            'merchant_retention_rate': {
                'active_merchants_count': total_active_merchants,
                'inactive_merchants_count': total_inactive_merchants,
                'active_merchants_percentage': round(active_merchants_percentage, 2),
                'inactive_merchants_percentage': round(inactive_merchants_percentage, 2),
                'active_vs_inactive_comparison': active_vs_inactive_comparison
            },
            'customer_satisfaction_score': round(average_customer_satisfaction_score, 2),
            'merchant_acquisition_status': merchant_acquisition_status,
            'merchant_acquisition_percentage_ratio': merchant_acquisition_percentage_ratio,
            'merchant_metrics': merchant_metrics
        })

    return results


def calculate_sales_lead_metrics():
    # Precompute metrics for each SalesOfficer
    sales_officer_metrics = calculate_sales_officer_metrics()

    sales_leads = models.SalesLead.objects.all()
    results = []

    for lead in sales_leads:
        officers = lead.sales_officers.all()

        total_merchants_acquired = officers.aggregate(total_merchants=Coalesce(Sum('merchants_acquired'), 0))[
            'total_merchants']
        total_revenue = officers.aggregate(total_revenue=Coalesce(Sum('revenue_generated'), Decimal(0.0)))[
            'total_revenue']
        total_active_merchants = officers.aggregate(total_active_merchants=Coalesce(Sum('active_merchants_count'), 0))[
            'total_active_merchants']
        total_transaction_volume = \
        officers.aggregate(total_transaction_volume=Coalesce(Sum('total_transaction_volume'), 0))[
            'total_transaction_volume']
        total_subscription_volume = \
        officers.aggregate(total_subscription_volume=Coalesce(Sum('total_subscription_volume'), 0))[
            'total_subscription_volume']
        total_subscription_value = \
        officers.aggregate(total_subscription_value=Coalesce(Sum('total_subscription_value'), Decimal(0.0)))[
            'total_subscription_value']
        total_inactive_merchants = total_merchants_acquired - total_active_merchants
        active_merchants_percentage = (
                    total_active_merchants / total_merchants_acquired * 100) if total_merchants_acquired else 0.0
        inactive_merchants_percentage = (
                    total_inactive_merchants / total_merchants_acquired * 100) if total_merchants_acquired else 0.0
        active_vs_inactive_comparison = f"{total_active_merchants}:{total_inactive_merchants}"

        officers_performance = [officer_metrics for officer_metrics in sales_officer_metrics if
                                officer_metrics['sales_officer_name'] in officers.values_list('name', flat=True)]

        results.append({
            'sales_lead_name': lead.name,
            'product_verticals': lead.product_verticals,
            'total_merchants_acquired': total_merchants_acquired,
            'merchants_acquired_against_target': total_merchants_acquired / settings.SALES_LEAD_MERCHANT_TARGET_PER_MONTH * 100,
            'total_terminals_deployed': officers.aggregate(total_terminals=Coalesce(Sum('terminals_deployed'), 0))[
                'total_terminals'],
            'total_revenue_generated': float(total_revenue),
            'overall_revenue_generated': float(total_revenue),
            'average_revenue_generated': float(
                (total_revenue / officers.count()).quantize(Decimal('0.01'))) if officers.count() else 0.0,
            'overall_average_revenue_generated': float(
                (total_revenue / officers.count()).quantize(Decimal('0.01'))) if officers.count() else 0.0,
            'total_transaction_volume': total_transaction_volume,
            'total_subscription_volume': total_subscription_volume,
            'total_subscription_value': float(total_subscription_value),
            'merchant_retention_rate': {
                'active_merchants_count': total_active_merchants,
                'inactive_merchants_count': total_inactive_merchants,
                'active_merchants_percentage': round(active_merchants_percentage, 2),
                'inactive_merchants_percentage': round(inactive_merchants_percentage, 2),
                'active_vs_inactive_comparison': active_vs_inactive_comparison
            },
            'sales_team_performance': officers_performance
        })

    return results


def calculate_merchant_metrics(merchants):
    # Define targets from settings
    subscription_target_annualy = settings.MERCHANT_SUBSCRIPTION_TARGET_ANNUALY
    transaction_target_per_month = settings.MERCHANT_TRANSACTION_TARGET_PER_MONTH_FOR_ALL_PRODUCT_VERTICALS
    stock_inventory_sales_transaction_target = settings.STOCK_INVENTORY_AND_SALES_TRANSACTION_TARGET_IN_VALUE_PER_MONTH
    stock_inventory_sales_revenue_target = settings.STOCK_INVENTORY_AND_SALES_REVENUE_TARGET_IN_VALUE_PER_MONTH
    spend_management_transaction_target = settings.SPEND_MANAGEMENT_TRANSACTION_TARGET_IN_VALUE_PER_MONTH
    spend_management_revenue_target = settings.SPEND_MANAGEMENT_REVENUE_TARGET_IN_VALUE_PER_MONTH

    merchants = merchants.annotate(
        # Average order value
        calculated_avg_order_value=Coalesce(
            Case(
                When(transaction_volume__gt=0, then=Sum('revenuelines__amount') / F('transaction_volume')),
                default=Value(0.0),
                output_field=FloatField()
            ), Value(0.0)
        ),
        # Total transaction value
        calculated_transaction_value=Coalesce(
            Case(
                When(transaction_volume__gt=0, then=F('transaction_volume') * F('calculated_avg_order_value')),
                default=Value(0.0),
                output_field=FloatField()
            ), Value(0.0)
        ),
        # Transaction percentage of target
        calculated_transaction_percentage_of_target=Coalesce(
            Case(
                When(transaction_volume__gt=0, then=F('transaction_volume') / transaction_target_per_month * 100),
                default=Value(0.0),
                output_field=FloatField()
            ), Value(0.0)
        ),
        # Revenue metrics
        revenue_weekly=Coalesce(Sum('revenuelines__amount') / Value(52), Value(0.0), output_field=DecimalField()),
        revenue_monthly=Coalesce(Sum('revenuelines__amount') / Value(12), Value(0.0), output_field=DecimalField()),
        revenue_quarterly=Coalesce(Sum('revenuelines__amount') / Value(4), Value(0.0), output_field=DecimalField()),
        revenue_biannual=Coalesce(Sum('revenuelines__amount') / Value(2), Value(0.0), output_field=DecimalField()),
        revenue_yearly=Coalesce(Sum('revenuelines__amount'), Value(0.0), output_field=DecimalField())
    )

    # Fetch previous month's revenue for growth calculation
    one_month_ago = datetime.date.today() - datetime.timedelta(days=30)
    one_week_ago = datetime.date.today() - datetime.timedelta(days=7)

    previous_month_revenue = merchants.filter(created_at__lt=one_month_ago).aggregate(
        previous_month_revenue=Coalesce(Sum('revenuelines__amount'), Value(0.0), output_field=DecimalField())
    )['previous_month_revenue']

    previous_week_revenue = merchants.filter(created_at__lt=one_week_ago).aggregate(
        previous_week_revenue=Coalesce(Sum('revenuelines__amount'), Value(0.0), output_field=DecimalField())
    )['previous_week_revenue']

    merchant_details = merchants.values(
        'id',
        'product_verticals__name',
        'subscription_plan',
        'subscription_status',
        'revenue_weekly',
        'revenue_monthly',
        'revenue_quarterly',
        'revenue_biannual',
        'revenue_yearly',
        'transaction_volume',
        'calculated_transaction_value',
        'transaction_volume_status',
        'growth_rate',
        'calculated_avg_order_value',
        'calculated_transaction_percentage_of_target',
        'active_longevity',
    )

    # Calculate revenue growth
    for merchant in merchant_details:
        current_month_revenue = merchant['revenue_monthly']
        current_week_revenue = merchant['revenue_weekly']

        # Monthly revenue growth
        merchant['monthly_revenue_growth'] = ((
                                                          current_month_revenue - previous_month_revenue) / previous_month_revenue * 100) if previous_month_revenue > 0 else 0

        # Weekly revenue growth
        merchant['weekly_revenue_growth'] = ((
                                                         current_week_revenue - previous_week_revenue) / previous_week_revenue * 100) if previous_week_revenue > 0 else 0

    return list(merchant_details)

from decimal import Decimal


def calculate_system_metrics():
    # Initialize the metrics dictionary with the given structure
    metrics = {
        'processed_transaction': Decimal('0.0'),
        'subscription_income': Decimal('0.0'),
        'gross_revenue': Decimal('0.0'),
        'net_revenue': Decimal('0.0'),
        'active_merchants': 0,
        'active_subscription_rate': 0.0,
        'sales_officers': 0,
        'transactions_performance': {
            'success_rate': 0.0,
            'failure_rate': 0.0,
            'error_rate': 0.0,
            'transaction_speed': 0.0,
            'latency': 0.0,
            'system_uptime': 0.0,
            'authorization_rate': 0.0,
            'refund_rate': 0.0,
            'gateway_response': 0.0
        },
        'avg_sales_per_officer': Decimal('0.0'),
        'top_revenue_module': '',
        'overall_metrics': {},
        'product_metrics': defaultdict(dict)
    }

    # Transaction-related metrics
    transactions = acc.Transaction.objects.annotate(
        cleaned_amount=Case(
            When(amount="none", then=Value(0.0)),
            When(amount__isnull=True, then=Value(0.0)),
            default=Cast(F('amount'), FloatField())
        ),
        cleaned_commission=Case(
            When(commission="none", then=Value(0.0)),
            When(commission__isnull=True, then=Value(0.0)),
            default=Cast(F('commission'), FloatField())
        )
    ).aggregate(
        total_transactions=Count('id'),
        successful_transactions=Count('id', filter=Q(status='SUCCESS')),
        failed_transactions=Count('id', filter=Q(status='FAILED')),
        error_transactions=Count('id', filter=Q(status='ERROR')),
        total_processed_transaction=Coalesce(Sum('cleaned_amount'), 0.0),
        total_commission=Coalesce(Sum('cleaned_commission'), 0.0)
    )
    print("TRANSACTIONS::::::::::::::::", transactions)

    metrics['processed_transaction'] = transactions['total_processed_transaction']
    print(f"Processed Transaction: {metrics['processed_transaction']}")

    # Subscription income
    subscription_income = subscription.SubscriptionModule.objects.annotate(
        cleaned_price=Case(
            When(subscription_type__price="none", then=Value(0.0)),
            When(subscription_type__price__isnull=True, then=Value(0.0)),
            default=Cast(F('subscription_type__price'), FloatField())
        )
    ).aggregate(
        total=Coalesce(Sum('cleaned_price'), 0.0)
    )['total']

    metrics['subscription_income'] = subscription_income
    print(f"Subscription Income: {metrics['subscription_income']}")

    # Gross and net revenue
    metrics['gross_revenue'] = metrics['processed_transaction'] + metrics['subscription_income']
    metrics['net_revenue'] = metrics['gross_revenue'] - transactions['total_commission']  # Adjust based on real deductions
    print(f"Net Revenue: {metrics['net_revenue']}")

    # Merchant-related metrics
    total_merchants = psmd.Merchants.objects.count()
    active_merchants = psmd.Merchants.objects.filter(subscription_status=True).count()
    metrics['active_merchants'] = active_merchants

    metrics['active_subscription_rate'] = (
        (active_merchants / total_merchants) * 100 if total_merchants > 0 else 0.0
    )
    print(f"Active Subscription Rate: {metrics['active_subscription_rate']}%")

    # Sales officer metrics
    metrics['sales_officers'] = psmd.SalesOfficer.objects.count()

    # Calculate average sales per officer
    total_sales = psmd.Merchants.objects.annotate(
        cleaned_transaction_volume=Case(
            When(transaction_volume__isnull=True, then=Value(0.0)),
            default=Cast(F('transaction_volume'), FloatField())
        )
    ).aggregate(
        total_sales=Coalesce(Sum('cleaned_transaction_volume'), Value(0.0))
    )['total_sales']

    metrics['avg_sales_per_officer'] = (
        total_sales / metrics['sales_officers'] if metrics['sales_officers'] > 0 else 0.0
    )
    print(f"Average Sales Per Officer: {metrics['avg_sales_per_officer']}")

    # Top revenue module
    top_module = psmd.RevenueLines.objects.annotate(
        cleaned_amount=Case(
            When(amount="none", then=Value(0.0)),
            When(amount__isnull=True, then=Value(0.0)),
            default=Cast(F('amount'), FloatField())
        )
    ).values('vertical__name').annotate(
        total_revenue=Coalesce(Sum('cleaned_amount'), 0.0)
    ).order_by('-total_revenue').first()

    metrics['top_revenue_module'] = top_module['vertical__name'] if top_module else 'N/A'
    print(f"Top Revenue Module: {metrics['top_revenue_module']}")

    # Overall and product-specific metrics
    companies = req.Company.objects.prefetch_related('merchants')
    overall_metrics = companies.aggregate(
        total_active_merchants=Count('merchants', filter=Q(merchants__subscription_status=True)),
        total_inactive_merchants=Count('merchants', filter=Q(merchants__subscription_status=False)),
    )
    metrics['overall_metrics'] = overall_metrics

    # For product verticals
    product_verticals = companies.values_list('merchants__product_vertical', flat=True).distinct()
    for vertical in product_verticals:
        vertical_companies = companies.filter(merchants__product_vertical=vertical)
        vertical_metrics = vertical_companies.aggregate(
            total_merchants=Count('merchants'),
            total_active_merchants=Count('merchants', filter=Q(merchants__subscription_status=True)),
        )
        metrics['product_metrics'][vertical] = vertical_metrics

    return metrics