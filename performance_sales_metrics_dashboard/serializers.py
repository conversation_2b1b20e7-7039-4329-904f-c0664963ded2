from rest_framework import serializers

from requisition.models import Company
from .models import Gmail<PERSON><PERSON>unt, SalesLead, SalesOfficer, Lead, Pipeline, Stage, Activity, Task, Notes, Category, \
    ProductVerticals, \
    EmailAccount, Emails, BookADemo, WorkEmail, NewsLetterSubscription


class SuperAdminCategory(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['name']


class SuperAdminStage(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Stage
        fields = ['name', 'category_name']


class SalesOfficerSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesOfficer
        fields = ['user', 'name', 'phone_number', 'email', 'sales_lead', 'product_module', ]


class ReadSalesOfficerSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesOfficer
        fields = '__all__'
        # fields = [
        #     'id', 'user', 'sales_lead', 'name', 'email', 'phone_number', 'date_hired', 'company',
        #     'merchants_acquired', 'terminals_deployed', 'referral_code', 'product_module'
        # ]
        # read_only_fields = ['user', 'sales_lead', 'name', 'email', 'referral_code', 'product_module']


class ProductVerticalsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVerticals
        exclude = ['team']


class ReadProductVerticalsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVerticals
        exclude = []


# class SalesLeadSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = SalesLead
#         fields = ['product_verticals']
#
#     def validate_product_verticals(self, value):
#         """
#         Check that the product verticals are valid.
#         """
#         if value not in enums.Product_verticals:
#             raise serializers.ValidationError("Invalid product vertical choice.")
#         return value


class SalesLeadSerializer(serializers.ModelSerializer):
    product_verticals = serializers.PrimaryKeyRelatedField(
        queryset=ProductVerticals.objects.all(),
        many=True
    )

    class Meta:
        model = SalesLead
        fields = [
            'phone_number',
            'date_hired',
            'product_verticals',
        ]


class ReadSalesLeadSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesLead
        fields = '__all__'


class OnboardCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = [
            'id',
            'sales_officer',
            'referral_code',
            'product_vertical',
            'created_at',
            'updated_at',
        ]
        read_only_fields = ['created_at', 'updated_at']


class CreateLeadSerializer(serializers.ModelSerializer):
    # NOTE: Create Lead and Edit
    class Meta:
        model = Lead
        fields = ['contact_name', 'contact_email', 'contact_phone_number', 'company_name',
                  'company_email', 'company_phone_number', 'company_address', 'pipeline', 'stage', 'engagement_status',
                  'deal_value']
        # 'product_vertical']
        # fields = ['contact_name', 'contact_email', 'contact_phone_number', 'company_name',
        #           'company_email', 'company_phone_number', 'company_address', 'engagement_status', 'deal_value']


class CreateLeadViaCSVSerializer(serializers.Serializer):
    csv_file = serializers.FileField()


class ReadLeadSerializer(serializers.ModelSerializer):
    pipeline_name = serializers.CharField(source='pipeline.name', read_only=True)
    stage_name = serializers.CharField(source='stage.category.name', read_only=True)
    sales_officer_name = serializers.CharField(source='sales_officer_in_charge.name', read_only=True)
    product_vertical_names = serializers.SerializerMethodField()

    def get_product_vertical_names(self, obj):
        return [vertical.name for vertical in obj.product_vertical.all()]

    class Meta:
        model = Lead
        fields = ["id", "contact_name", "contact_email", "contact_phone_number", "company_name", "company_email",
                  "company_phone_number", "company_address", "source", "sales_officer_in_charge", "sales_officer_name",
                  "pipeline", "pipeline_name", "stage", "stage_name", "engagement_status", "deal_status", "created_at",
                  "time_created", "updated_at", "status", "onboard_status", "product_vertical", "country",
                  "product_vertical_names", "deal_value", "how_did_you_hear_about_us", "employee_head_count",
                  "monthly_transaction_amount_estimate", "deal_value", "expiry_date"]

        # "product_vertical",

        # fields = ['id', 'contact_name', 'contact_email', 'contact_phone_number', 'company_name', 'company_email',
        #           'company_phone_number', 'engagement_status', 'deal_value']  # , 'duration']


class CreateStageSerializer(serializers.ModelSerializer):
    def validate(self, data):
        if data.get("email_notification_enabled") and (
                not data.get("email_subject") or not data.get("email_body")
        ):
            raise serializers.ValidationError(
                "Both email subject and body are required if email notification is enabled."
            )
        return data

    class Meta:
        model = Stage
        fields = ('pipeline', 'name', 'category')
        # fields = ('pipeline', 'category', 'email_subject', 'email_body', 'email_text',
        #           'email_notifications_enabled')


class ReadStageSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Stage
        fields = ["id", "pipeline", "name", "category", "category_name", "order", "email_subject", "email_body",
                  "email_text",
                  "email_notifications_enabled", "lead_count", "created_at", "updated_at"]

        # fields = ('id', 'pipeline', 'category', 'order', 'lead_count')


class ReadStageAndLeadSerializer(serializers.ModelSerializer):
    leads = ReadLeadSerializer(many=True, read_only=True)

    class Meta:
        model = Stage
        fields = ['id', 'name', 'category', 'pipeline', 'order', 'created_at', 'updated_at', 'leads']


class PipelineSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pipeline

        fields = ['name', 'description', 'type']

    def validate(self, attrs):
        name = attrs.get('name')
        sales_officer = attrs.get('sales_officer')
        if Pipeline.objects.filter(name=name, sales_officer=sales_officer).exists():
            raise serializers.ValidationError("This name is already used for this instance of Sales Officer.")
        return attrs


class ReadPipelineSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pipeline
        fields = "__all__"


class ActivitySerializer(serializers.ModelSerializer):
    # description = serializers.CharField(required=False, allow_null=True)
    class Meta:
        model = Activity
        fields = ['lead', 'title', 'description']
        # fields = ['lead', 'sales_officer', 'title', 'description']


class ReadActivitySerializer(serializers.ModelSerializer):
    sales_officer_name = serializers.CharField(source='sales_officer.name', read_only=True)

    class Meta:
        model = Activity
        fields = ["id", "lead", "sales_officer", "sales_officer_name", "title", "description", "created_at",
                  "updated_at"]
        # fields = ['id', 'lead', 'title', 'description']


class TaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = [
            'activity', 'assigned', 'title', 'description', 'priority', 'deadline', 'is_reminder', 'is_done',
            'deadline_reminder']


class ReadTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ["id", "activity", "activity_title", "title", "description", "assigned", "priority", "status",
                  "deadline",
                  "is_reminder", "is_done", "deadline_reminder", "created_at", "updated_at"]
        # fields = [
        #     'id', 'activity', 'assigned', 'title', 'description', 'priority', 'deadline', 'is_reminder', 'is_done',
        #     'deadline_reminder']


class NotesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notes
        fields = ['title', 'description']
        # fields = ['sales_officer', 'title', 'description']


class ReadNotesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notes
        fields = "__all__"
        # fields = ['id', 'title', 'description']


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['company', 'team', 'name', 'description']

    def validate(self, attrs):
        name = attrs.get('name')
        company = attrs.get('company')
        team = attrs.get('team')
        if Category.objects.filter(name=name, company=company, team=team).exists():
            raise serializers.ValidationError("A category with this name already exists.")
        return attrs


class ReadCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = "__all__"


class EmailSerializer(serializers.Serializer):
    recipient = serializers.EmailField()
    subject = serializers.CharField()
    body = serializers.CharField()
    # contact_name = serializers.CharField()
    sales_officer_id = serializers.CharField()
    sales_lead_id = serializers.CharField()
    user_id = serializers.CharField()
    lead_id = serializers.CharField()


class SendEmailSerializer(serializers.Serializer):
    subject = serializers.CharField()
    message = serializers.CharField()
    cc = serializers.ListField()
    bcc = serializers.ListField()
    recipient_email = serializers.ListField()
    company_id = serializers.CharField()


class SendBulkEmailSerializer(serializers.Serializer):
    subject = serializers.CharField()
    message = serializers.CharField()
    cc = serializers.ListField()
    bcc = serializers.ListField()
    recipient_emails = serializers.ListField()
    scheduled_time = serializers.CharField()
    company_id = serializers.CharField()


class RefreshEmailSerializer(serializers.Serializer):
    company_id = serializers.CharField()


class EmailAddressSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, allow_null=False)


class EventsSerializer(serializers.Serializer):
    attendees = serializers.ListField(child=EmailAddressSerializer())
    summary = serializers.CharField()
    description = serializers.CharField()
    location = serializers.CharField()
    start_date = serializers.CharField()
    end_date = serializers.CharField()
    calendarId = serializers.CharField()
    company_id = serializers.CharField()


class ScheduleZoomToGoogleSerializer(serializers.Serializer):
    start_date = serializers.CharField()


class EmailAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailAccount
        fields = ['id', 'email', 'provider', 'is_oauth', 'last_sync']
        read_only_fields = ['is_oauth', 'last_sync']


class EmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = ['id', 'subject', 'sender', 'senders_name', 'recipients', 'date', 'is_read']


class GoogleMeetSerializer(serializers.Serializer):
    sales_officer_id = serializers.CharField()
    # scopes = serializers.CharField()


class CreateCalendarSerializer(serializers.Serializer):
    calendar_title = serializers.CharField()


class CheckFreeBusySerializer(serializers.Serializer):
    timeMin = serializers.CharField()
    # calendarIds = serializers.ListField()
    # timeMax = serializers.CharField()
    # company_id = serializers.CharField()


class BookDemoSerializer(serializers.Serializer):
    company_name = serializers.CharField()
    company_email = serializers.EmailField()
    contact_name = serializers.CharField()
    contact_phone_number = serializers.CharField()
    country = serializers.CharField()
    how_did_you_hear_about_us = serializers.CharField()
    employee_head_count = serializers.CharField()
    monthly_transaction_amount_estimate = serializers.CharField()


class BookADemoSerializer(serializers.ModelSerializer):
    class Meta:
        model = BookADemo
        fields = [
            "lead",
            "team_member",
            "start_time",
        ]


class InboxSerializer(serializers.Serializer):
    password = serializers.CharField()
    username = serializers.CharField()
    company_id = serializers.CharField()


class GetEmailDraftsSerializer(serializers.Serializer):
    company_id = serializers.CharField()


class SaveEmailAsDraftSerializer(serializers.Serializer):
    subject = serializers.CharField()
    sender = serializers.CharField()
    senders_name = serializers.CharField()
    recipients = serializers.CharField()
    date = serializers.CharField()
    is_read = serializers.CharField()


class CompanyIdSerializer(serializers.Serializer):
    company_id = serializers.CharField()


class ReadEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = [
            "id",
            "is_read",
            "date",
            "subject",
            "sender",
            "senders_name",
            "cc",
            "bcc",
            "recipients",
            "content",
        ]


class SentEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = [
            "id",
            "is_sent",
            "date",
            "subject",
            "sender",
            "senders_name",
            "cc",
            "bcc",
            "recipients",
            "content",
        ]


class MailInboxSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = [
            "id",
            "is_inbox",
            "date",
            "subject",
            "sender",
            "senders_name",
            "cc",
            "bcc",
            "recipients",
            "content",
        ]


class SaveDraftSerializer(serializers.Serializer):
    subject = serializers.CharField()
    content = serializers.CharField()
    cc = serializers.ListField()
    bcc = serializers.ListField()
    recipients = serializers.ListField()
    company_id = serializers.CharField()


class DraftEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = [
            "id",
            "is_draft",
            "date",
            "subject",
            "sender",
            "senders_name",
            "cc",
            "bcc",
            "recipients",
            "content",
        ]


class DeletedEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = [
            "id",
            "is_deleted",
            "date",
            "subject",
            "sender",
            "senders_name",
            "cc",
            "bcc",
            "recipients",
            "content",
        ]


class ScheduledEmailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Emails
        fields = [
            "id",
            "is_scheduled",
            "date",
            "subject",
            "sender",
            "senders_name",
            "cc",
            "bcc",
            "recipients",
            "content",
        ]


class WorkEmailSerializer(serializers.Serializer):
    work_email = serializers.EmailField()
    password = serializers.CharField()
    company_id = serializers.CharField()


class GmailSerializer(serializers.Serializer):
    gmail_address = serializers.EmailField()
    company_id = serializers.CharField()
    token_data = serializers.JSONField()


class GoogleAuthSerializer(serializers.Serializer):
    gmail_address = serializers.EmailField()
    company_id = serializers.CharField()


class GoogleMailSerializer(serializers.ModelSerializer):
    class Meta:
        model = GmailAccount
        fields = "__all__"


class NewsLetterSubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewsLetterSubscription
        fields = ['full_name', 'email', 'is_consent_given']

# Todo: Filter all by prospects and leads
# Todo: Filter by week, month, 3months, year
# Todo: Upload Prospects and Leads via CSV and Excel
# Todo: Calculate worth of a pipeline
# Todo:
# Todo:


# class LeadSerializer(serializers.Serializer):
#     class Meta:
#         model = Lead
#         fields = ['contact_name', 'contact_phone_number', 'company_name', 'company_email', 'company_phone_number',]
#                   'company_address', 'industry', 'sales_officer', 'pipeline', 'stage', 'creation_method', 'label',
#                   'deal_status', 'created_at', 'time_created', 'updated_at', 'entity', 'open_deal_amount', ]


# class CreateProspectSerializer(serializers.ModelSerializer):
#     # TODO: Create Lead
#     class Meta:
#         model = Lead
#         fields = ['contact_name', 'contact_phone_number', 'company_name', 'company_email', 'company_phone_number',
#                   'company_address']


# class ReadProspectSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Lead
#         fields = ['contact_name', 'contact_phone_number', 'company_name', 'company_email', 'company_phone_number',
#                   'company_address', 'sales_officer'


# class CreateLeadSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Lead
#         fields = ['contact_name', 'contact_phone_number', 'company_name', 'company_email', 'company_phone_number',
#                   'company_address', 'industry', 'sales_officer', 'stage', 'open_deal_amount', 'entity']


# class ReadLeadSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Lead
#         fields = ['contact_name', 'label', 'creation_method', 'time_created', 'created_at', 'open_deal_amount']


# class DealSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Deal
#         fields = ('stage', 'title', 'description', 'merchant')
