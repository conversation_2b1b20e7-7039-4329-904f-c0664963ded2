from performance_sales_metrics_dashboard.models import SalesOfficer, SalesLead
from rest_framework.permissions import BasePermission

from requisition.helpers.enums import UserRole
from requisition.models import TeamMember


class IsAdmin(BasePermission):
    """
    Allows access only to sales leads users.
    """

    def has_permission(self, request, view):
        team_super_roles  = [UserRole.OWNER, UserRole.ADMIN, UserRole.SALES_SUPER_ADMIN, UserRole.SUB_ADMIN]
        user = request.user

        if not user.is_authenticated:
            return False

        return TeamMember.objects.filter(member=user, role__in=team_super_roles).exists()


class IsSalesSuperAdmin(BasePermission):
    """
    Allows access only to sales leads users.
    """

    def has_permission(self, request, view):
        user = request.user

        if not user.is_authenticated:
            return False

        return TeamMember.objects.filter(member=user, role=UserRole.SALES_SUPER_ADMIN).exists()


class IsSalesOfficer(BasePermission):
    """
    Allows access only to sales officers users.
    """

    def has_permission(self, request, view):
        user = request.user

        if not user.is_authenticated:
            return False

        # return SalesOfficer.objects.filter(user=user).exists()
        return TeamMember.objects.filter(member=user, role=UserRole.SALES_OFFICER).exists()


class IsSalesLead(BasePermission):
    """
    Allows access only to sales leads users.
    """

    def has_permission(self, request, view):
        user = request.user

        if not user.is_authenticated:
            return False

        # return SalesLead.objects.filter(user=user).exists()
        return TeamMember.objects.filter(member=user, role=UserRole.SALES_LEAD).exists()
