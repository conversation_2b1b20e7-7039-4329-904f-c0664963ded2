from import_export import resources, fields
from performance_management.models import ActivityLog, CheckInFrequency, CompanyGoals, CompanyKeyResult, KPICategory, KPICheckIn, Review, Tasks, StatusUpdate

class CompanyGoalsResource(resources.ModelResource):
    class Meta:
        model = CompanyGoals

class CompanyKeyResultResource(resources.ModelResource):
    class Meta:
        model = CompanyKeyResult

class KPICheckInResource(resources.ModelResource):
    class Meta:
        model = KPICheckIn

class TasksResource(resources.ModelResource):
    class Meta:
        model = Tasks

class CheckInFrequencyResource(resources.ModelResource):
    class Meta:
        model = CheckInFrequency

class KPICategoryResource(resources.ModelResource):
    class Meta:
        model = KPICategory

class ReviewResource(resources.ModelResource):
    class Meta:
        model = Review

class StatusUpdateResource(resources.ModelResource):
    class Meta:
        model = StatusUpdate

class ActivityLogResource(resources.ModelResource):
    class Meta:
        model = ActivityLog