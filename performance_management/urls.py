from django.urls import path

from performance_management import views

company_goal_key = [
    path("create_company_goal/", views.CreateCompanyGoalAPIView.as_view()),
    path("create_goal/", views.CreateGoalAPIView.as_view()),
    path("create_individual_goal/", views.CreateIndividualGoalAPIView.as_view()),
    path("edit_company_goal/", views.EditCompanyGoalAPIView.as_view()),
    path("edit_individual_goal/", views.EditIndividualGoalAPIView.as_view()),
    path("company_goal_data/", views.CompanyGoalOverviewAPIView.as_view()),
    path("single_company_goal_data/", views.SingleCompanyGoalOverviewAPIView.as_view()),
    path("single_company_goal_updates/", views.SingleGoalStatusUpdateHistory.as_view()),
    path("company_goal_list/", views.CompanyGoalsAPIView.as_view()),
    path("individual_goal_list/", views.IndividualGoalsAPIView.as_view()),
    path("all_data_overview/", views.CompanyGoalDataAPIView.as_view()),
    path("individual_goal_data/", views.IndividualGoalDataAPIView.as_view()),
    path("individual_dashboard_overview/", views.IndividualDashboardOverviewAPIView.as_view()),
    path("company_dashboard_overview/", views.CompanyDashboardOverviewAPIView.as_view()),
    path("clone_company_goal/", views.CloneCompanyGoalAPIView.as_view()),
    path("clone_individual_goal/", views.CloneIndividualGoalAPIView.as_view()),
]

key_result = [
    path("create_company_key_result/", views.CreateCompanyKeyResultAPIView.as_view()),
    path("create_individual_key_result/", views.CreateIndividualKeyResultAPIView.as_view()),
    path("edit_company_key_result/", views.EditCompanyKeyResultAPIView.as_view()),
    path("edit_individual_key_result/", views.EditIndividualKeyResultAPIView.as_view()),
    path("company_key_result_list/", views.CompanyKeyResultsAPIView.as_view()),
    path("individual_key_result_list/", views.IndividualKeyResultsAPIView.as_view()),
]

company_task = [
    path("create_key_result_task/", views.CreateKeyResultTaskAPIView.as_view()),
    path("create_individual_key_result_task/", views.CreateIndividualKeyResultTaskAPIView.as_view()),
    path("create_task/", views.CreateTaskAPIView.as_view()),
    path("create_individual_task/", views.CreateIndividualTaskAPIView.as_view()),
    path("key_result_task/", views.KeyResultTask.as_view()),
    path("task_list/", views.TaskList.as_view()),
    path("edit_task/", views.EditTaskAPIView.as_view()),
    path("edit_individual_task/", views.EditIndividualTaskAPIView.as_view()),
    path("individual_task_list/", views.IndividualTaskList.as_view()),
]

check_in = [
    path("check_in/", views.CreateCheckInAPIView.as_view()),
    path("key_result_check_in_history/", views.KeyResultCheckInHistory.as_view()),
    path("activity_log_history/", views.ActivityLogHistory.as_view()),

]

fetch_module = [
    path("period_module/", views.PeriodModule.as_view()),
    path("kpi_category_module/", views.KPICategoryList.as_view()),
    path("check_in_frequency_module/", views.CheckInFrequencyList.as_view()),
    path("create_kpi_category/", views.KPICategoryAPIView.as_view()),
    path("edit_kpi_category/", views.EditKPICategoryAPIView.as_view()),
    path("check_in_date_module/", views.CheckInDateAPIView.as_view()),
]

alignment = [
    path("align_goal/", views.AlignGoalAPIView.as_view()),
    path("align_key_result/", views.AlignKeyResultAPIView.as_view()),
]

reviews = [
    path("create_company_review/", views.CreateCompanyReview.as_view()),
    path("create_individual_review/", views.CreateIndividualReview.as_view()),
    path("company_review_list/", views.CompanyReviewList.as_view()),
    path("individual_review_list/", views.IndividualReviewList.as_view()),
    path("edit_company_review/", views.EditCompanyReviewAPIView.as_view()),
    path("edit_individual_review/", views.EditIndividualReviewAPIView.as_view()),
]

overview = [
    path("overview_dashboard/", views.OverviewDashboard.as_view()),
    path("overall_goal_progress/", views.OverallGoalProgressAPIView.as_view()),
    path("overall_goal_achieved_remaining/", views.OverallGoalAchievedRemainingAPIView.as_view()),
    path("overall_goal_by_department/", views.OverallGoalByDepartmentAPIView.as_view()),
    path("overall_achieved_goal_remaining_goal/", views.OverallAchievedGoalRemainingGoalAPIView.as_view()),
]

department = [
    path("department_goals/", views.DepartmentGoalAPIView.as_view()),
]


urlpatterns = [
    *company_goal_key,
    *company_task,
    *key_result,
    *check_in,
    *fetch_module,
    *alignment,
    *reviews,
    *overview,
    *department,
]