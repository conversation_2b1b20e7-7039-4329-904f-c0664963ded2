from datetime import date, timedelta, datetime
import calendar

# Helper function to calculate quarter start and end dates
def get_quarter_start_end(month, year):
    quarter = (month - 1) // 3 + 1
    start_month = (quarter - 1) * 3 + 1
    start_date = date(year=year, month=start_month, day=1)

    # Correctly set the end date for each quarter
    if quarter == 1:  # Q1
        end_date = date(year=year, month=3, day=31)
    elif quarter == 2:  # Q2
        end_date = date(year=year, month=6, day=30)
    elif quarter == 3:  # Q3
        end_date = date(year=year, month=9, day=30)
    else:  # Q4
        end_date = date(year=year, month=12, day=31)

    return start_date, end_date, quarter

def period_module():
    today = date.today()
    # Add the next 4 quarters including the current quarter
    # Generate Current Month, Next Month, and Current Year
    periods = [
        {
            "name": "Current Month",
            "start_date": today.replace(day=1),
            "end_date": (today.replace(day=1) + timedelta(days=31)).replace(day=1) - timedelta(days=1)
        },
        {
            "name": "Next Month",
            "start_date": (today.replace(day=1) + timedelta(days=31)).replace(day=1),
            "end_date": (today.replace(day=1) + timedelta(days=62)).replace(day=1) - timedelta(days=1)
        },
        {
            "name": "Current Year",
            "start_date": today.replace(month=1, day=1),
            "end_date": today.replace(month=12, day=31)
        },
    ]
    current_month = today.month
    current_year = today.year
    for i in range(12):
        # Calculate the adjusted month and year of the quarter
        new_month = current_month + i * 3
        new_year = current_year + (new_month - 1) // 12
        new_month = (new_month - 1) % 12 + 1

        # Get the start and end dates, and quarter number
        start_date, end_date, quarter_num = get_quarter_start_end(new_month, new_year)
        period_name = f"Q{quarter_num} {start_date.year}"

        periods.append({
            "name": period_name,
            "start_date": start_date,
            "end_date": end_date
        })
    return periods

def get_weekdays(start_date, end_date, weekday):
    """
    Get specific weekdays (e.g., Mondays or Tuesdays) from a start date.

    Parameters:
        start_date (datetime or str): The starting date.
        weekday (int): The target weekday (0=Monday, 1=Tuesday, ..., 6=Sunday).
        count (int): Optional. Number of weekdays to fetch.
        end_date (datetime or str): Optional. End date for fetching weekdays.
    
    Returns:
        List[datetime]: List of specific weekdays.
    """
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d')

    # Find the first target weekday on or after the start_date
    days_ahead = (weekday - start_date.weekday() + 7) % 7
    first_target_day = start_date + timedelta(days=days_ahead)

    weekdays = []
    current_day = first_target_day

    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
    while current_day <= end_date:
        weekdays.append(current_day)
        current_day += timedelta(days=7)
 
    return weekdays

def get_daily_dates(start_date, end_date):
    """
    Get every day's date starting from a specified start date.

    Parameters:
        start_date (datetime or str): The starting date.
        count (int): Optional. Number of days to fetch.
        end_date (datetime or str): Optional. End date for fetching days.

    Returns:
        List[datetime]: List of daily dates.
    """
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d')

    daily_dates = []
    current_date = start_date

    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
    while current_date <= end_date:
        daily_dates.append(current_date)
        current_date += timedelta(days=1)

    return daily_dates

def get_last_days_of_month(start_date, end_date):
    """
    Get the last day of each month starting from a specified start date.

    Parameters:
        start_date (datetime or str): The starting date.
        count (int): Optional. Number of months to fetch.
        end_date (datetime or str): Optional. End date for fetching months.
    
    Returns:
        List[datetime]: List of the last days of each month.
    """
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d')

    last_days = []
    current_date = start_date

    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
    while current_date <= end_date:
        # Get the last day of the current month
        last_day = calendar.monthrange(current_date.year, current_date.month)[1]
        last_day_date = datetime(current_date.year, current_date.month, last_day)
        last_days.append(last_day_date)
        # Move to the first day of the next month
        next_month = current_date.replace(day=28) + timedelta(days=4)
        current_date = next_month.replace(day=1)

    return last_days

# Function to ensure the input is a `datetime.date` object
def ensure_date(d):
    if isinstance(d, datetime):
        return d.date()
    elif isinstance(d, date):
        return d

def fetch_all_check_in_dates(all_check_in_dates, start_date, end_date):
    if not all_check_in_dates:
        return []
    else:
        this_check_in_dates = []
        for check_in_dates in all_check_in_dates:
            if check_in_dates.name == "Mondays":
                check_in = get_weekdays(start_date, end_date, 0)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Tuesdays":
                check_in = get_weekdays(start_date, end_date, 1)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Wednesdays":
                check_in = get_weekdays(start_date, end_date, 2)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Thursdays":
                check_in = get_weekdays(start_date, end_date, 3)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Fridays":
                check_in = get_weekdays(start_date, end_date, 4)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Saturdays":
                check_in = get_weekdays(start_date, end_date, 5)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Sundays":
                check_in = get_weekdays(start_date, end_date, 6)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Everyday":
                check_in = get_daily_dates(start_date, end_date)
                this_check_in_dates+=check_in
            elif check_in_dates.name == "Last day of the month":
                check_in = get_last_days_of_month(start_date, end_date)
                this_check_in_dates+=check_in

        # Convert all elements in the list to `datetime.date`
        this_check_in_dates = [ensure_date(d) for d in this_check_in_dates]

        unique_sorted_dates = sorted(set(this_check_in_dates))
        return unique_sorted_dates