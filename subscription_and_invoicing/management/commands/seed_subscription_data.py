# management/commands/seed_subscription_data.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta
import random
from faker import Faker
from decimal import Decimal

from requisition.models import Company
from sales_app.models import Customer
from stock_inventory.models import Branch
from subscription_and_invoicing.models import CompanySubscription, Module, ModuleSubscription, SubscriptionAudit, SubscriptionPlan

User = get_user_model()
fake = Faker()

class Command(BaseCommand):
    help = 'Populates the subscription system with test data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--companies',
            type=int,
            default=5,
            help='Number of test companies to create'
        )

    def handle(self, *args, **options):
        self.stdout.write('Starting to populate subscription test data...')
        
        # Create test user if not exists
        admin_user = self._create_admin_user()
        
        # Create subscription plans
        plans = self._create_subscription_plans()
        
        # Create modules
        modules = self._create_modules()
        
        # Create companies and their subscriptions
        num_companies = options['companies']
        companies = self._create_companies(num_companies, admin_user)
        
        # Create subscriptions and related data
        self._create_subscriptions(companies, plans, modules, admin_user)
        
        self.stdout.write(self.style.SUCCESS('Successfully populated test data!'))

    def _create_admin_user(self):
        try:
            admin_user = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            admin_user = User.objects.create_superuser(
                email='<EMAIL>',
                password='admin123',
                first_name='Admin',
                last_name='User'
            )
        return admin_user

    def _create_subscription_plans(self):
        plans_data = [
            {
                'name': 'Basic',
                'description': 'Essential features for small businesses',
                'price': Decimal('99.99'),
                'duration_months': 1,
                'features': {'max_users': 5, 'storage': '5GB'}
            },
            {
                'name': 'Professional',
                'description': 'Advanced features for growing businesses',
                'price': Decimal('199.99'),
                'duration_months': 1,
                'features': {'max_users': 15, 'storage': '15GB'}
            },
            {
                'name': 'Enterprise',
                'description': 'Complete solution for large enterprises',
                'price': Decimal('499.99'),
                'duration_months': 1,
                'features': {'max_users': 'Unlimited', 'storage': '50GB'}
            }
        ]

        plans = []
        for plan_data in plans_data:
            plan, _ = SubscriptionPlan.objects.get_or_create(
                name=plan_data['name'],
                defaults=plan_data
            )
            plans.append(plan)
        
        self.stdout.write(f'Created {len(plans)} subscription plans')
        return plans

    def _create_modules(self):
        modules_data = [
            {
                'name': 'Stock and Inventory',
                'code': 'INVENTORY',
                'description': 'Complete inventory management system',
                'is_premium': True
            },
            {
                'name': 'HR Management',
                'code': 'HR',
                'description': 'Employee and HR management tools',
                'is_premium': True
            },
            {
                'name': 'Spend Management',
                'code': 'SPEND',
                'description': 'Budget and expense tracking',
                'is_premium': True
            },
            {
                'name': 'Sales',
                'code': 'SALES',
                'description': 'Sales and order management',
                'is_premium': True
            }
        ]

        modules = []
        for module_data in modules_data:
            module, _ = Module.objects.get_or_create(
                code=module_data['code'],
                defaults=module_data
            )
            modules.append(module)
        
        self.stdout.write(f'Created {len(modules)} modules')
        return modules

    def _create_companies(self, num_companies, admin_user):
        companies = []
        industries = ['Retail', 'Technology', 'Manufacturing', 'Healthcare', 'Education']
        
        for i in range(num_companies):
            company_name = fake.company()
            company = Company.objects.create(
                user=admin_user,
                company_name=company_name,
                industry=random.choice(industries),
                size=random.randint(5, 200),
                cac_num=f'CAC{fake.random_number(digits=8)}',
                is_active=True
            )
            
            # Create a default branch for the company
            default_branch = Branch.objects.create(
                company=company,
                created_by=admin_user,
                name=f"{company_name} - Main Branch",
                address=fake.address(),
                vat=0.0,  # Set appropriate default VAT
                is_super_branch=True
            )
            
            companies.append(company)
            
            # Create some customers for each company
            self._create_customers(company, default_branch, admin_user)
        
        self.stdout.write(f'Created {len(companies)} companies with branches and customers')
        return companies

    def _create_customers(self, company, branch, admin_user):
        num_customers = random.randint(3, 8)
        for _ in range(num_customers):
            Customer.objects.create(
                company=company,
                branch=branch,  # Associate customer with the branch
                name=fake.company(),
                email=fake.email(),
                phone=fake.phone_number(),
                address=fake.address(),
                created_by=admin_user
            )

    def _create_subscriptions(self, companies, plans, modules, admin_user):
        subscription_types = ['paid', 'trial', 'bypass']
        status_types = ['active', 'trial', 'expired']

        for company in companies:
            # Randomly assign subscription type and plan
            access_type = random.choice(subscription_types)
            plan = random.choice(plans)
            
            # Create subscription
            start_date = timezone.now() - timedelta(days=random.randint(0, 60))
            subscription = CompanySubscription.objects.create(
                company=company,
                plan=plan,
                access_type=access_type,
                status=random.choice(status_types),
                created_by=admin_user
            )

            # Add random modules to subscription
            num_modules = random.randint(1, len(modules))
            selected_modules = random.sample(modules, num_modules)
            
            for module in selected_modules:
                ModuleSubscription.objects.create(
                    company_subscription=subscription,
                    module=module,
                    start_date=start_date,
                    end_date=start_date + timedelta(days=30 * plan.duration_months),
                )

            # Create audit entries
            SubscriptionAudit.objects.create(
                company=company,
                subscription=subscription,
                action='create_subscription',
                action_by=admin_user,
                details={
                    'plan': plan.id,
                    'access_type': access_type,
                    'modules': [m.id for m in selected_modules]
                }
            )

            self.stdout.write(f'Created subscription and modules for {company.company_name}')