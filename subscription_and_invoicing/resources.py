from import_export import resources

from subscription_and_invoicing import models

class SubscriptionTypesResource(resources.ModelResource):
    class Meta:
        model = models.SubscriptionType

class SubscriptionModuleResource(resources.ModelResource):
    class Meta:
        model = models.SubscriptionModule

class InvoiceResource(resources.ModelResource):

    class Meta:
        model = models.Invoice

class InvoiceTransactionResource(resources.ModelResource):
    class Meta:
        model = models.InvoiceTransaction

class CommissionResource(resources.ModelResource):
    class Meta:
        model = models.Commission

class CommissionStructureResource(resources.ModelResource):
    class Meta:
        model = models.CommissionStructure

class UserCompanyDetailsResource(resources.ModelResource):
    class Meta:
        model = models.UserCompanyDetails
