import base64
import json

import requests
from django.conf import settings


class LibertyPayOnboardingMgr:
    def __init__(self):
        self.environment = settings.ENVIRONMENT
        if self.environment == "dev":
            self.base_url = "https://dev.libertypayng.com/agency"
        else:
            self.base_url = "https://backend.libertypayng.com/agency"

    def request_result_handler(self, payload, response, url):
        """
        Handles the response from API requests, returning a structured result.

        Args:
            payload (dict): The data sent with the request.
            response (requests.Response): The response object returned by the API.
            url (str): The URL that was called.

        Returns:
            dict: A dictionary containing the request result, including status, response,
                  and other related information.
        """
        try:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "success",
                "response": response.json(),
                "method": response.request.method,
                "payload": payload,
            }
        except (
            requests.exceptions.RequestException,
            Exception,
            json.JSONDecodeError,
        ) as err:
            response = {
                "url": url,
                "status_code": response.status_code,
                "status": "failed",
                "method": response.request.method,
                "response": response.text,
                "payload": payload,
                "err_msg": str(err),
            }
        return response

    def sign_up(
        self,
        first_name,
        last_name,
        email,
        phone_number,
        username,
        state,
        lga,
        nearest_landmark,
        street,
        password,
        pin,
    ):
        url = f"{self.base_url}/user/create_user_detail/"
        data = {
            "first_name": first_name,
            "last_name": last_name,
            "email": email,
            "phone_number": phone_number,
            "username": username,
            "state": state,
            "lga": lga,
            "nearest_landmark": nearest_landmark,
            "street": street,
            "password": password,
            "pin": pin,
        }
        response = requests.post(url, data=data)
        return self.request_result_handler(payload=data, response=response, url=url)

    def verify_user(self, email, passcode):

        data = {"email": email, "passcode": passcode}

        url = f"{self.base_url}/user/confirm_registration_email/"
        response = requests.post(url, data=data)
        return self.request_result_handler(payload=data, response=response, url=url)

    def create_transaction_pin(self, transaction_pin, token):
        url = f"{self.base_url}/user/create_transaction_pin/"
        data = {
            "transaction_pin": transaction_pin,
            "transaction_pin_retry": transaction_pin,
        }
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(url, headers=headers, data=data)

        return self.request_result_handler(payload=data, response=response, url=url)

    def fetch_user_details(self, token):
        url = f"{self.base_url}/user/get_user_details/"
        payload = ""
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.request("GET", url, headers=headers, data=payload)

        return self.request_result_handler(payload=payload, response=response, url=url)
