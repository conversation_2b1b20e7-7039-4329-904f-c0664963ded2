from datetime import timedelta
from django.contrib import admin
from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    Invoice,
    PromotionalOffer,
    SubscriptionPlan, 
    Module, 
    CompanySubscription, 
    ModuleSubscription,
    SubscriptionAudit
)

from datetime import timedelta
from subscription_and_invoicing import models
from django.db.models import Sum, F
from django.utils import timezone

class PriceRangeFilter(admin.SimpleListFilter):
    title = 'Price Range'
    parameter_name = 'price_range'

    def lookups(self, request, model_admin):
        return (
            ('0-50k', '₦0 - ₦50,000'),
            ('50k-150k', '₦50,000 - ₦150,000'),
            ('150k-300k', '₦150,000 - ₦300,000'),
            ('300k+', '₦300,000+'),
        )

    def queryset(self, request, queryset):
        if self.value() == '0-50k':
            return queryset.filter(price__lte=50000)
        if self.value() == '50k-150k':
            return queryset.filter(price__gt=50000, price__lte=150000)
        if self.value() == '150k-300k':
            return queryset.filter(price__gt=150000, price__lte=300000)
        if self.value() == '300k+':
            return queryset.filter(price__gt=300000)

class ExpiryFilter(admin.SimpleListFilter):
    title = 'Expiry Status'
    parameter_name = 'expiry_status'

    def lookups(self, request, model_admin):
        return (
            ('expired', 'Expired'),
            ('due_7', 'Due in 7 days'),
            ('due_30', 'Due in 30 days'),
            ('active', 'Active (>30 days)'),
        )

    def queryset(self, request, queryset):
        now = timezone.now()
        if self.value() == 'expired':
            return queryset.filter(expiry_date__lt=now)
        if self.value() == 'due_7':
            return queryset.filter(expiry_date__range=(now, now + timedelta(days=7)))
        if self.value() == 'due_30':
            return queryset.filter(expiry_date__range=(now, now + timedelta(days=30)))
        if self.value() == 'active':
            return queryset.filter(expiry_date__gt=now + timedelta(days=30))
        

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'name', 
        'duration_months', 
        'price_display', 
        'is_active', 
        'created_at'
    )
    list_filter = (
        'is_active', 
        'duration_months'
    )
    search_fields = (
        'name', 
        'description'
    )
    readonly_fields = (
        'created_at', 
        'updated_at'
    )
    
    def price_display(self, obj):
        """
        Display price with formatted currency
        """
        formatted_price = '{:,.2f}'.format(float(obj.price))
        return format_html('₦{}', formatted_price)
    price_display.short_description = 'Price'
    
    def get_queryset(self, request):
        """
        Optimize query performance
        """
        return super().get_queryset(request)

    def save_model(self, request, obj, form, change):
        """
        Add created_by logic if needed
        """
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    # def has_delete_permission(self, request, obj=None):
    #     """
    #     Prevent deletion of active plans
    #     """
    #     if obj and obj.is_active:
    #         return False
    #     return super().has_delete_permission(request, obj)

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        """
        Customize form fields if needed
        """
        if db_field.name == 'duration_months':
            kwargs['choices'] = [(6, '6 Months'), (12, '12 Months')]
        return super().formfield_for_choice_field(db_field, request, **kwargs)

@admin.register(models.PromotionalOffer)
class PromotionalOfferAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_amount_display', 'is_active', 'start_date', 'end_date')
    list_filter = ('is_active', 'start_date', 'end_date')
    search_fields = ('name', 'description')
    
    def discount_amount_display(self, obj):
        formatted_amount = '{:,.2f}'.format(float(obj.discount_amount))
        return format_html('₦{}', formatted_amount)
    discount_amount_display.short_description = 'Discount Amount'

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = (
        'invoice_reference', 
        'company_link', 
        'amount_display', 
        'amount_due',
        'batch_id',
        'payment_status',
        'expiry_status',
        'sales_officer',
        'created_at'
    )
    list_filter = (
        'payment_status',
        ExpiryFilter,
        'promo_applied',
        'sales_officer',
    )
    search_fields = (
        'invoice_reference',
        'batch_id',
        'company__company_name',
        'package_description',
    )
    readonly_fields = (
        'invoice_reference',
        'batch_id',
        'created_at', 
        'updated_at'
    )
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'company', 'company_subscription', 'invoice_reference', 'batch_id',
                'package_description', 'sales_officer'
            )
        }),
        ('Financial Details', {
            'fields': (
                'amount_paid', 'balance_due', 'settled_amount', 'amount_brought_forward',
                'payment_status', 'used_excess_payment'
            )
        }),
        ('Subscription Details', {
            'fields': (
                'start_date', 'expiry_date', 'promo_applied'
            )
        }),
        ('Additional Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'company', 'company_subscription', 'sales_officer'
        )

    def company_link(self, obj):
        url = reverse('admin:requisition_company_change', args=[obj.company.id])
        return format_html('<a href="{}">{}</a>', url, obj.company.company_name)
    company_link.short_description = 'Company'

    def amount_display(self, obj):
        if obj.amount_paid:
            formatted_amount = '{:,.2f}'.format(float(obj.amount_paid))
            return format_html('₦{}', formatted_amount)
        return "₦0.00"
    amount_display.short_description = 'Amount Paid'

    def amount_due(self, obj):
        if obj.balance_due and obj.balance_due > 0:
            formatted_balance = '{:,.2f}'.format(float(obj.balance_due))
            return format_html(
                '<span style="color: red;">₦{}</span>', 
                formatted_balance
            )
        return format_html('₦0.00')
    amount_due.short_description = 'Balance Due'

    def expiry_status(self, obj):
        if not obj.expiry_date:
            return '-'
        days_until_expiry = (obj.expiry_date - timezone.now()).days
        if days_until_expiry < 0:
            return format_html(
                '<span style="color: red;">Expired</span>'
            )
        elif days_until_expiry <= 7:
            return format_html(
                '<span style="color: orange;">{} days</span>', 
                days_until_expiry
            )
        elif days_until_expiry <= 30:
            return format_html(
                '<span style="color: blue;">{} days</span>', 
                days_until_expiry
            )
        return f"{days_until_expiry} days"
    expiry_status.short_description = 'Days Until Expiry'
class ModuleSubscriptionInline(admin.TabularInline):
    model = ModuleSubscription
    extra = 1
    readonly_fields = (
        'created_at',
        'days_remaining_display',
        'start_date',
        'end_date',
    )
    fields = (
        'module',
        'plan',
        'module_overall_sub_days',
        'invoice_ref',
        'is_active',
        'start_date',
        'end_date',
        'days_remaining_display',
        'is_auto_renewal',
        'pending_balance'
    )
    
    def days_remaining_display(self, obj):
        if not obj.id:  # New object being created
            return "-"
        days = obj.days_remaining()
        if days < 0:
            return format_html('<span style="color: red;">Expired</span>')
        elif days < 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return f"{days} days"
    days_remaining_display.short_description = 'Days Remaining'
    
    # def total_price_display(self, obj):
    #     """Display total price, considering promotional offers"""
    #     if obj.plan:
    #         base_price = obj.plan.price
    #         if obj.promotional_offer:
    #             try:
    #                 discount = float(obj.promotional_offer.discount_amount)
    #                 discounted_price = max(0, base_price - discount)
    #                 return format_html(
    #                     '₦{:,.2f} <span style="color: green;">(Promo: -₦{:,.2f})</span>',
    #                     discounted_price,
    #                     discount
    #                 )
    #             except (ValueError, TypeError):
    #                 pass
    #         return format_html('₦{:,.2f}', base_price)
    #     return '-'
    # total_price_display.short_description = 'Total Price'
    
    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        form = formset.form
        form.base_fields['pending_balance'].widget.attrs['style'] = 'width: 100px;'
        return formset

@admin.register(CompanySubscription)
class CompanySubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        'company',
        'subscribed_company', 
        'status', 
        'access_type',
        'overall_sub_days',
        'invoice_ref',
        'active_modules_display',
        'pos_included',
        'created_at'
    )
    list_filter = (
        'status', 
        'access_type',
        'pos_included',
    )
    search_fields = (
        'company__company_name',
    )
    readonly_fields = ('created_at', 'updated_at')
    inlines = [ModuleSubscriptionInline]
    actions = ['mark_as_expired', 'extend_all_modules']

    autocomplete_fields = ['created_by', 'company']

    def get_queryset(self, request):
        # Update all subscription days when admin is loaded if refresh parameter is present
        qs = super().get_queryset(request).select_related(
            'company',
        ).prefetch_related('modulesubscription_set')

        # Check if we should refresh the data (but avoid infinite recursion)
        if request.GET.get('refresh_days') == '1' and not request.GET.get('no_refresh'):
            self.refresh_all_subscription_days(request)

        return qs

    def refresh_all_subscription_days(self, request=None):
        """Update all subscription days data."""
        # For better performance, this could be moved to a background task
        for subscription in CompanySubscription.objects.all():
            subscription.update_overall_subscription_days()

        # If this was called from a view, redirect back with no_refresh to prevent loops
        if request:
            from django.contrib import messages
            messages.success(request, "All subscription days have been refreshed.")

    def refresh_subscription_days(self, request, queryset):
        """Admin action to refresh subscription days for selected companies."""
        count = 0
        for subscription in queryset:
            subscription.update_overall_subscription_days()
            count += 1

        self.message_user(request, f"Successfully updated subscription days for {count} companies.")

    refresh_subscription_days.short_description = "Refresh subscription days"

    def subscribed_company(self, obj):
        url = reverse('admin:requisition_company_change', args=[obj.company.id])
        return format_html('<a href="{}">{}</a>', url, obj.company.company_name)

    subscribed_company.short_description = 'Subscribed Company'

    def active_modules_display(self, obj):
        active = obj.modulesubscription_set.filter(
            is_active=True,
            end_date__gt=timezone.now()
        ).count()
        total = Module.objects.count()
        if active < total:
            return format_html(
                '<span style="color: orange;">{}/{}</span>',
                active, total
            )
        return f"{active}/{total}"

    active_modules_display.short_description = 'Active Modules'

    # def get_queryset(self, request):
    #     return super().get_queryset(request).select_related(
    #         'company',
    #     ).prefetch_related('modulesubscription_set')
    #
    # def subscribed_company(self, obj):
    #     url = reverse('admin:requisition_company_change', args=[obj.company.id])
    #     return format_html('<a href="{}">{}</a>', url, obj.company.company_name)
    # subscribed_company.short_description = 'Subscribed Company'
    #
    # def active_modules_display(self, obj):
    #     active = obj.modulesubscription_set.filter(
    #         is_active=True,
    #         end_date__gt=timezone.now()
    #     ).count()
    #     total = Module.objects.count()
    #     if active < total:
    #         return format_html(
    #             '<span style="color: orange;">{}/{}</span>',
    #             active, total
    #         )
    #     return f"{active}/{total}"
    # active_modules_display.short_description = 'Active Modules'

@admin.register(ModuleSubscription)
class ModuleSubscriptionAdmin(admin.ModelAdmin):

    list_display = (
        'module',
        'company_name',
        'plan_display',
        'is_active',
        'start_date',
        'end_date',
        'days_remaining_display',
        'pending_balance_display',
        'promo_details',
        'module_overall_sub_days'
        # 'total_price_display'
    )
    list_filter = (
        'is_active', 
        'module',
        'plan'
    )
    search_fields = (
        'company_subscription__company__company_name',
        'module__name',
        'plan__name'
    )

    autocomplete_fields = ['company_subscription', 'module', 'promotional_offer', 'plan']

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        # Check if we should refresh the data
        if request.GET.get('refresh_days') == '1' and not request.GET.get('no_refresh'):
            self.refresh_all_module_days(request)

        return qs

    def refresh_all_module_days(self, request=None):
        """Update all module subscription days."""
        # For better performance, this could be moved to a background task
        for subscription in ModuleSubscription.objects.all().select_related('company_subscription'):
            subscription.update_module_overall_days()

            if request:
                from django.contrib import messages
                messages.success(request, "All module subscription days have been refreshed.")

    def refresh_module_days(self, request, queryset):
        """Admin action to refresh module subscription days for selected subscriptions."""
        count = 0
        for subscription in queryset:
            subscription.update_module_overall_days()
            count += 1

        self.message_user(request, f"Successfully updated subscription days for {count} module subscriptions.")

    refresh_module_days.short_description = "Refresh module subscription days"

    def company_name(self, obj):
        return obj.company_subscription.company.company_name

    company_name.short_description = 'Company'

    def plan_display(self, obj):
        return obj.plan.name if obj.plan else '-'

    plan_display.short_description = 'Subscription Plan'

    def days_remaining_display(self, obj):
        days = obj.days_remaining()
        if days <= 0:
            return format_html('<span style="color: red;">Expired</span>')
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return f"{days} days"

    days_remaining_display.short_description = 'Days Remaining'

    def module_overall_sub_days_display(self, obj):
        """Display the stored total subscription days value."""
        # Return the stored value if it exists
        if obj.module_overall_sub_days and obj.module_overall_sub_days.isdigit():
            return obj.module_overall_sub_days

        # Otherwise calculate it on the fly (this shouldn't happen with proper implementation)
        print(f"Calculating total days for module: {obj.module}, company: {obj.company_subscription}")
        total_days = ModuleSubscription.objects.filter(
            module=obj.module,
            company_subscription=obj.company_subscription
        ).aggregate(Sum(F('end_date') - F('start_date')))["end_date__sum"]

        result = total_days.days if total_days else "0"
        print(f"Total days result: {result}")
        return result

    module_overall_sub_days_display.short_description = "Total Subscribed Days"

    def pending_balance_display(self, obj):
        if obj.pending_balance > 0:
            formatted_balance = '{:,.2f}'.format(float(obj.pending_balance))
            return format_html(
                '<span style="color: red;">₦{}</span>',
                formatted_balance
            )
        return '-'

    pending_balance_display.short_description = 'Pending Balance'

    def promo_details(self, obj):
        if obj.promotional_offer:
            try:
                discount_amount = float(obj.promotional_offer.discount_amount)
                formatted_amount = '{:,.2f}'.format(discount_amount)
                return format_html(
                    '{} (-₦{})',
                    obj.promotional_offer.name,
                    formatted_amount
                )
            except (ValueError, TypeError):
                return '-'
        return '-'

    promo_details.short_description = 'Promotion Applied'
    
    # def company_name(self, obj):
    #     return obj.company_subscription.company.company_name
    # company_name.short_description = 'Company'
    #
    # def plan_display(self, obj):
    #     return obj.plan.name if obj.plan else '-'
    # plan_display.short_description = 'Subscription Plan'
    #
    # def days_remaining_display(self, obj):
    #     days = obj.days_remaining()
    #     if days <= 0:
    #         return format_html('<span style="color: red;">Expired</span>')
    #     elif days <= 7:
    #         return format_html('<span style="color: orange;">{} days</span>', days)
    #     return f"{days} days"
    # days_remaining_display.short_description = 'Days Remaining'
    #
    # def module_overall_sub_days(self, obj):
    #     """Dynamically calculate total subscription days for display in admin."""
    #     print(f"Calculating total days for module: {obj.module}, company: {obj.company_subscription}")
    #
    #     total_days = ModuleSubscription.objects.filter(
    #         module=obj.module,
    #         company_subscription=obj.company_subscription
    #     ).aggregate(Sum(F('end_date') - F('start_date')))["end_date__sum"]
    #
    #     print(f"Total days result: {total_days}")
    #
    #     # Return the days or 0 if nothing comes back
    #     return total_days.days if total_days else "0"
    #
    # module_overall_sub_days.short_description = "Total Subscribed Days"
    #
    # def pending_balance_display(self, obj):
    #     if obj.pending_balance > 0:
    #         formatted_balance = '{:,.2f}'.format(float(obj.pending_balance))
    #         return format_html(
    #             '<span style="color: red;">₦{}</span>',
    #             formatted_balance
    #         )
    #     return '-'
    # pending_balance_display.short_description = 'Pending Balance'

    # def total_price_display(self, obj):
    #     """Display total price, considering promotional offers"""
    #     if not obj or not obj.plan:
    #         return '-'

    #     try:
    #         base_price = float(obj.plan.price or 0)
    #     except (ValueError, TypeError):
    #         return '-'

    #     if obj.promotional_offer:
    #         try:
    #             discount = float(obj.promotional_offer.discount_amount or 0)
    #             discounted_price = max(0, base_price - discount)
    #             return format_html(
    #                 '₦{:,.2f} <span style="color: green;">(Promo: -₦{:,.2f})</span>',
    #                 discounted_price,
    #                 discount
    #             )
    #         except (ValueError, TypeError):
    #             pass

    #     return format_html('₦{:,.2f}', base_price)

    # total_price_display.short_description = 'Total Price'


    # def promo_details(self, obj):
    #     if obj.promotional_offer:
    #         try:
    #             discount_amount = float(obj.promotional_offer.discount_amount)
    #             formatted_amount = '{:,.2f}'.format(discount_amount)
    #             return format_html(
    #                 '{} (-₦{})',
    #                 obj.promotional_offer.name,
    #                 formatted_amount
    #             )
    #         except (ValueError, TypeError):
    #             return '-'
    #     return '-'
    # promo_details.short_description = 'Promotion Applied'

@admin.register(Module)
class ModuleAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'is_premium', 'description', 'requires_subscription', 'created_at')
    list_filter = ('is_premium', 'requires_subscription')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at',)
    ordering = ('name',)


@admin.register(SubscriptionAudit)
class SubscriptionAuditAdmin(admin.ModelAdmin):
    list_display = ('company', 'action', 'action_by', 'created_at', 'view_details')
    list_filter = ('action', 'created_at')
    search_fields = ('company__company_name', 'action', 'action_by__email')
    readonly_fields = ('created_at',)

    def view_details(self, obj):
        return format_html(
            '<pre style="font-size: 11px;">{}</pre>',
            obj.details
        )
    view_details.short_description = 'Audit Details'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
