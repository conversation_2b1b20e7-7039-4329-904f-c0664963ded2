# from django.core.management.base import BaseCommand
# from django.utils import timezone
# from subscription_and_invoicing.models import Invoice
# from subscription_and_invoicing.views import SubscriptionRenewalAPIView
# from django.contrib.auth.models import User
# from rest_framework.request import Request
# from rest_framework.test import APIRequestFactory
#
# class Command(BaseCommand):
#     help = 'Checks for subscriptions due for renewal and triggers the renewal process'
#
#     def handle(self, *args, **options):
#         due_invoices = Invoice.objects.filter(
#             is_active=True,
#             expiry_date__lte=timezone.now().date() + timezone.timedelta(days=7)
#         )
#
#         for invoice in due_invoices:
#             self.stdout.write(f"Processing renewal for Invoice {invoice.id}")
#
#             # Simulate a request object
#             factory = APIRequestFactory()
#             user = User.objects.get(username='admin')  # Assuming 'admin' is a valid username
#             request = factory.post(
#                 '/subscription-renewal/',
#                 {'company_id': invoice.company.id, 'module_id': invoice.module.id}
#             )
#             request.user = user
#
#             # Call the API view
#             view = SubscriptionRenewalAPIView.as_view()
#             response = view(request)
#
#             if response.status_code == 201:
#                 self.stdout.write(self.style.SUCCESS(f"Renewal initiated for Invoice {invoice.id}"))
#             else:
#                 self.stdout.write(self.style.ERROR(f"Failed to initiate renewal for Invoice {invoice.id}: {response.data}"))
