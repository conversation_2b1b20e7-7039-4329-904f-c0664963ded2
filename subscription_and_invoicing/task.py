from core.models import PaystackPayment
from subscription_and_invoicing import models as subscription
from rest_framework.response import Response
from celery import shared_task
from decimal import Decimal
from rest_framework import status
from django.contrib.auth import get_user_model
from django.db import transaction
from subscription_and_invoicing.models import CommissionStructure, Commission, InvoiceTransaction, Invoice
from subscription_and_invoicing.serializers import InvoiceTransactionSerializer


User = get_user_model()


@shared_task
def paystack_payment_confirmation(id):
    instance = PaystackPayment.objects.filter(id=id).first()
    batch_id = instance.reference
    invoice = subscription.Invoice.objects.get(batch_id=batch_id)
    amount_paid = instance.amount

    if not batch_id or not amount_paid:
        return Response({"error": "Missing required fields"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        paystack_payment = PaystackPayment.objects.get(reference=batch_id)
    except PaystackPayment.DoesNotExist:
        return Response({"error": "Invalid batch ID"}, status=status.HTTP_404_NOT_FOUND)

    try:
        invoice = subscription.Invoice.objects.get(batch_id=batch_id)
    except subscription.Invoice.DoesNotExist:
        return Response({"error": "Invoice not found"}, status=status.HTTP_404_NOT_FOUND)

    if paystack_payment.reference != batch_id:
        return Response({"error": "Batch ID does not match reference"}, status=status.HTTP_400_BAD_REQUEST)

    amount_paid_decimal = Decimal(amount_paid)
    settled_amount = invoice.settled_amount + amount_paid_decimal
    amount_due_before_payment = invoice.amount_due
    amount_due_after_payment = max(Decimal(0), invoice.amount_due - amount_paid_decimal)

    excess_payment = amount_paid_decimal - amount_due_before_payment if amount_paid_decimal > amount_due_before_payment else Decimal(
        0)
    invoice.settled_amount = settled_amount

    payment_method = paystack_payment.channel

    if excess_payment > 0:
        invoice.payment_status = 'paid_excess'
        invoice.amount_due = Decimal(0)
        invoice.amount_brought_forward = excess_payment
    elif amount_due_after_payment == 0:
        invoice.payment_status = 'paid'
        invoice.is_active = True
    elif 0 < amount_due_after_payment < invoice.amount:
        invoice.payment_status = 'part_payment'
        invoice.amount_due = amount_due_after_payment
        invoice.amount_brought_forward = amount_due_after_payment
    else:
        invoice.payment_status = 'unpaid'

    invoice.save()

    invoice_transaction = subscription.InvoiceTransaction.objects.create(
        invoice=invoice,
        amount=amount_paid_decimal,
        payment_method=payment_method,
        excess_payment=excess_payment,
        is_processed=True,
        invoice_reference=invoice.invoice_reference,
    )

    _process_commission(invoice_transaction, amount_paid_decimal)

    return "SUCCESSFULLY UPDATED SUBSCRIPTION PAYMENT"



def _process_commission(invoice_transaction, amount_paid_decimal):
    commission_structures = subscription.CommissionStructure.objects.all()

    for structure in commission_structures:
        recipients = User.objects.filter(commission__role=structure)

        for recipient in recipients:
            commission_amount = amount_paid_decimal * structure.percentage
            subscription.Commission.objects.create(
                invoice_transaction=invoice_transaction,
                recipient=recipient,
                role=structure.role,
                amount=commission_amount,
                paid=False
            )