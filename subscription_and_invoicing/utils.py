from subscription_and_invoicing.models import ModuleSubscription, CompanySubscription


def refresh_all_subscription_days():
    """Utility function to refresh all subscription days."""
    # Update module subscriptions first
    for module_sub in ModuleSubscription.objects.all():
        module_sub.update_module_overall_days()

    # Then update company subscriptions
    for company_sub in CompanySubscription.objects.all():
        company_sub.update_overall_subscription_days()

    return True


# from django.http import HttpResponseRedirect
# from django.contrib import messages
# from django.contrib.admin.views.decorators import staff_member_required
#
#
# @staff_member_required
# def refresh_subscription_days_view(request):
#     """Admin view to refresh all subscription days."""
#     refresh_all_subscription_days()
#     messages.success(request, "All subscription days have been refreshed.")
#
#     # Redirect back to the previous page or a default admin page
#     redirect_to = request.META.get('HTTP_REFERER', '/admin/')
#     return HttpResponseRedirect(redirect_to)