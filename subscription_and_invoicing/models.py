from datetime import datetime, timedelta, date
from pprint import pprint

from pprint import pprint
# from django.contrib.auth.models import User
from django.utils import timezone
import random
import string
from django.db.models import Sum
import uuid
import json
import json
from django.conf import settings
from django.db import models
from django.contrib.auth import get_user_model
from rest_framework.views import APIView

from core.models import BaseModel
from helpers.reusable_functions import (
    generate_transaction_pin,
    generate_password,
    get_account_details,
)
from performance_sales_metrics_dashboard import models as perf

from datetime import timedelta
from django.utils import timezone
from decimal import Decimal
from invoicing import models as inv
from requisition.models import Company
from sales_app.models import Customer
from requisition import models as req
import logging
from subscription_and_invoicing.apis import LibertyPayOnboardingMgr
from django.db.models import Sum, F, ExpressionWrapper, DurationField

# from requisition.models import Company
User = get_user_model()


class SubscriptionModule(models.Model):
    pass
class SubscriptionType(models.Model):
    pass


class PromotionalOffer(models.Model):
    """Tracks promotional subscription offers"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.name} ({self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')})"

class SubscriptionPlan(models.Model):
    """Defines available subscription plans """
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration_months = models.IntegerField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.duration_months} month(s) - ₦{self.price}"

    class Meta:
        ordering = ['price']

class Module(models.Model):
    """Available system modules that can be subscribed to"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    is_premium = models.BooleanField(default=True)
    requires_subscription = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class AccessPath:
    """Define the three distinct access paths for Paybox360"""
    PAID = 'paid'
    TRIAL = 'trial'
    BYPASS = 'bypass'

    CHOICES = (
        (PAID, 'Paid Subscription'),
        (TRIAL, 'Free Trial'),
        (BYPASS, 'Free Merchant')
    )

class CompanySubscription(models.Model):
    """Tracks company subscriptions and their status"""
    SUBSCRIPTION_STATUS = (
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('trial', 'Trial'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    )

    company = models.ForeignKey('requisition.Company', on_delete=models.CASCADE)
    modules = models.ManyToManyField(Module, through='ModuleSubscription')
    access_type = models.CharField(
        max_length=20, 
        choices=AccessPath.CHOICES,
        help_text="Determines how the company accesses Paybox360 modules"
    )
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.SET_NULL, null=True, blank=True)
    invoice_ref = models.CharField(max_length=100, blank=True, null=True)
    overall_sub_days = models.CharField(max_length=225, blank=True, null=True)
    status = models.CharField(max_length=20, choices=SUBSCRIPTION_STATUS)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True,
        related_name='created_subscriptions'
    )
    pos_included = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.company.company_name} Subscription"

    def update_overall_subscription_days(self):
        """Calculate and update total subscription days for all modules under this company."""
        # Get all module subscriptions for this company, including expired ones
        module_subscriptions = self.modulesubscription_set.all()

        total_days = 0
        for subscription in module_subscriptions:
            # Convert string to int if needed
            if subscription.module_overall_sub_days and subscription.module_overall_sub_days.isdigit():
                module_days = int(subscription.module_overall_sub_days)
            else:
                # Calculate for this subscription if not already set
                delta = (subscription.end_date - subscription.start_date).days
                module_days = max(0, delta)  # Ensure we don't count negative days

            total_days += module_days

        self.overall_sub_days = str(total_days)
        self.save(update_fields=['overall_sub_days'])

        return total_days

    def save(self, *args, **kwargs):
        # Handle update_fields to avoid recursive save
        update_fields = kwargs.get('update_fields')
        if update_fields and 'overall_sub_days' in update_fields:
            return super().save(*args, **kwargs)

        super().save(*args, **kwargs)

    def is_active(self):
        """Check if subscription is active based on access type and status"""
        if self.access_type in [AccessPath.BYPASS, AccessPath.TRIAL]:
            return self.status in ['active', 'trial']

        # For paid subscriptions, check if any modules are active
        return self.modulesubscription_set.filter(
            is_active=True,
            end_date__gt=timezone.now()
        ).exists()

    def has_module_access(self, module_code):
        """Check if company has access to specific module based on subscription type"""
        try:
            # Free merchants and trials have access to all modules
            if self.access_type in [AccessPath.BYPASS, AccessPath.TRIAL]:
                return self.status in ['active', 'trial']

            # Paid subscriptions need to check specific module access
            return ModuleSubscription.objects.filter(
                company_subscription=self,
                module__code=module_code,
                is_active=True,
                end_date__gt=timezone.now()
            ).exists()

        except Module.DoesNotExist:
            return False


class ModuleSubscription(models.Model):
    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('active', 'Active'),
        ('expired', 'Expired'),
    ]

    """Links modules to company subscriptions with specific access controls"""
    company_subscription = models.ForeignKey(CompanySubscription, on_delete=models.CASCADE)
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.PROTECT, null=True, blank=True)
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    is_auto_renewal = models.BooleanField(default=False)
    promotional_offer = models.ForeignKey(
        PromotionalOffer,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    pending_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Balance to be paid after successful onboarding"
    )
    invoice_ref = models.CharField(max_length=100, blank=True, null=True)
    module_overall_sub_days = models.CharField(max_length=225, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started')
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.module.name} - {self.company_subscription.company} ({'Active' if self.is_active else 'Inactive'})"

    class Meta:
        unique_together = ('company_subscription', 'module')

    def update_module_overall_days(self):
        """Calculate total days this module has been subscribed to."""
        print(f"Updating total subscription days for module: {self.module}, company: {self.company_subscription}")

        # Get all subscriptions for this module and company, including expired ones
        subscriptions = ModuleSubscription.objects.filter(
            module=self.module,
            company_subscription=self.company_subscription
        )

        total_days = 0
        for sub in subscriptions:
            delta = (sub.end_date - sub.start_date).days
            total_days += max(0, delta)  # Ensure we don't count negative days

        self.module_overall_sub_days = str(total_days)
        print(f"Final module_overall_sub_days value: {self.module_overall_sub_days}")

        # Save with update_fields to avoid recursive save
        self.save(update_fields=['module_overall_sub_days'])

        # After updating module days, update company subscription total
        self.company_subscription.update_overall_subscription_days()

        return total_days

    def mark_expired(self):
        """
        Marks the subscription as expired if the end_date has passed.
        """
        now = timezone.now()

        if self.status == "not_started":
            return  # Do nothing if subscription hasn't started

        if self.is_active and self.end_date < now:
            print(f"Marking subscription as expired: {self}")
            self.is_active = False
            self.status = "expired"
            self.save()
            # You might need to implement this method in CompanySubscription
            if hasattr(self.company_subscription, 'update_status'):
                self.company_subscription.update_status()

    def days_remaining(self):
        if not self.is_active:
            return 0
        now = timezone.now()
        if now > self.end_date:
            return 0
        return (self.end_date - now).days

    def save(self, *args, **kwargs):
        """
        Unified save method:
        - Updates module_overall_sub_days.
        - Assigns start_date if missing.
        - Calculates end_date based on plan duration.
        - Determines status based on start/end dates.
        """
        # Handle update_fields to avoid recursive save
        update_fields = kwargs.get('update_fields')
        if update_fields and 'module_overall_sub_days' in update_fields:
            return super().save(*args, **kwargs)

        print(f"Saving subscription for module: {self.module}, company: {self.company_subscription}")

        now = timezone.now()

        # Assign start_date if not set
        if not self.start_date:
            print("Start date not provided — setting to now.")
            self.start_date = now

        # Assign end_date based on plan duration if not set
        if not self.end_date and self.plan:
            print(f"Calculating end date based on plan duration ({self.plan.duration_months} months)")
            duration_months = self.plan.duration_months
            start = self.start_date

            # Calculate end_date considering month/year transitions
            if start.month + duration_months <= 12:
                end_month = start.month + duration_months
                end_year = start.year
            else:
                years_to_add = (start.month + duration_months - 1) // 12
                end_month = (start.month + duration_months - 1) % 12 + 1
                end_year = start.year + years_to_add

            try:
                self.end_date = start.replace(year=end_year, month=end_month, day=start.day)
            except ValueError:
                # Handle months with fewer days (e.g., Feb 31)
                if end_month == 2:  # February
                    end_day = 29 if ((end_year % 4 == 0 and end_year % 100 != 0) or (end_year % 400 == 0)) else 28
                elif end_month in [4, 6, 9, 11]:  # 30-day months
                    end_day = 30
                else:  # 31-day months
                    end_day = 31

                self.end_date = start.replace(year=end_year, month=end_month, day=end_day)

        # Determine status based on start_date and end_date
        if self.start_date > now:
            print("Subscription hasn't started yet.")
            self.status = 'not_started'
        elif self.end_date and self.end_date < now:
            print("Subscription has expired.")
            self.status = 'expired'
        else:
            print("Subscription is active.")
            self.status = 'active'

        # Save the instance first
        print("Saving to the database.")
        super().save(*args, **kwargs)

        # Then update module days if we're not already in the update process
        if not update_fields or 'module_overall_sub_days' not in update_fields:
            self.update_module_overall_days()


class SubscriptionAudit(models.Model):
    """Tracks all subscription-related events for auditing"""
    company = models.ForeignKey('requisition.Company', on_delete=models.CASCADE)
    subscription = models.ForeignKey(CompanySubscription, on_delete=models.CASCADE, null=True)
    action = models.CharField(max_length=50)
    action_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    details = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.action} by {self.action_by} for {self.company} on {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        ordering = ['-created_at']



class Invoice(models.Model):
    """Tracks billing information for company subscriptions"""
    PAYMENT_STATUS_CHOICES = [
        ("paid", "Paid"),
        ("part_payment", "Part Payment"),
        ("paid_excess", "Paid in Excess"),
        ("unpaid", "Unpaid"),
    ]

    # Core relationships
    company = models.ForeignKey(
        'requisition.Company',
        on_delete=models.CASCADE,
        related_name="subscription_invoices"
    )
    company_subscription = models.ForeignKey(
        'subscription_and_invoicing.CompanySubscription',
        on_delete=models.SET_NULL,
        related_name='invoices',
        null=True,
        blank=True
    )
    leads = models.ForeignKey(
        'performance_sales_metrics_dashboard.Lead',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="subscription_invoices"
    )

    # Invoice details
    invoice_reference = models.CharField(
        max_length=255,
        default=uuid.uuid4,
        unique=True,
        editable=False
    )
    batch_id = models.CharField(
        max_length=10,
        unique=True,
        editable=False,
        null=True,
        blank=True
    )

    # Financial fields
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0
    )
    balance_due = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0
    )
    amount_brought_forward = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0
    )
    settled_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Subscription details
    subscribed_modules = models.ManyToManyField(
        'subscription_and_invoicing.Module',
        through='subscription_and_invoicing.InvoiceModuleDetail',
        related_name='invoices'
    )
    start_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)

    # Status fields
    is_active = models.BooleanField(default=False)
    payment_status = models.CharField(
        max_length=15,
        choices=PAYMENT_STATUS_CHOICES,
        default="unpaid"
    )
    used_excess_payment = models.BooleanField(default=False)

    # Attribution
    sales_officer = models.ForeignKey(
        'performance_sales_metrics_dashboard.SalesOfficer',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='generated_invoices'
    )
    package_description = models.CharField(max_length=255, blank=True, null=True)
    promo_applied = models.BooleanField(default=False)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_invoices'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['invoice_reference']),
            models.Index(fields=['batch_id']),
            models.Index(fields=['payment_status']),
        ]

    def __str__(self):
        return f"Invoice {self.invoice_reference} - {self.company.company_name}"

    @property
    def is_due_for_renewal(self):
        if not self.expiry_date:
            return False
        return self.is_active and self.expiry_date <= timezone.now() + timedelta(days=7)

    @staticmethod
    def generate_batch_id(length=5):
        return "".join(random.choices(string.ascii_uppercase + string.digits, k=length))

    def recalculate_amount_due(self):
        """
        Recalculates the total amount due for the invoice based on subscribed modules.
        """
        total_amount = sum(
            detail.total_price
            for detail in self.invoicemoduledetail_set.all()
        )

        self.total_amount = total_amount
        self.balance_due = max(0, total_amount - self.settled_amount)
        self.save()


class InvoiceModuleDetail(models.Model):
    """Tracks individual module details for each invoice"""
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=1, help_text="Number of months")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    class Meta:
        unique_together = ['invoice', 'module']

    def save(self, *args, **kwargs):
        """Calculate total price before saving"""
        discount_decimal = Decimal(self.discount) / Decimal(100)  # Convert discount to Decimal
        self.total_price = (self.unit_price * self.quantity) * (Decimal(1) - discount_decimal)
        super().save(*args, **kwargs)

class InvoiceAudit(models.Model):
    """Tracks all changes to invoices for auditing"""
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    action = models.CharField(max_length=50)
    action_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    previous_state = models.JSONField(null=True)
    new_state = models.JSONField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']


class InvoiceTransaction(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50)
    transaction_date = models.DateTimeField(auto_now_add=True)
    is_processed = models.BooleanField(default=False)
    excess_payment = models.FloatField(default=0)
    invoice_reference = models.CharField(max_length=255)

    def __str__(self):
        return self.invoice.invoice_reference


class Commission(models.Model):
    invoice_transaction = models.ForeignKey(
        InvoiceTransaction, on_delete=models.CASCADE
    )
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    role = models.ForeignKey(
        "CommissionStructure", on_delete=models.CASCADE, null=True, blank=True
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    paid = models.BooleanField(default=False)


class CommissionStructure(models.Model):
    role = models.CharField(max_length=50, unique=True)
    percentage = models.DecimalField(max_digits=5, decimal_places=4)

    def __str__(self):
        return f"{self.role}: {self.percentage}"


class UserCompanyDetails(BaseModel):
    STEP_CHOICES = [
        ("user_creation", "User_Creation"),
        ("verify_account", "Verify_Account"),
        ("create_transaction_pin", "Create_Transaction_Pin"),
        ("create_paybox_user", "Create_Paybox_User"),
        ("create_company", "Create_Company"),
    ]
    first_name = models.CharField(max_length=225, blank=True, null=True)
    last_name = models.CharField(max_length=225, blank=True, null=True)
    email = models.CharField(max_length=225, blank=True, null=True)
    phone_number = models.CharField(max_length=13, blank=True, null=True)
    username = models.CharField(max_length=225, blank=True, null=True)
    state = models.CharField(max_length=225, blank=True, null=True)
    lga = models.CharField(max_length=225, blank=True, null=True)
    street = models.CharField(max_length=225, blank=True, null=True)
    nearest_landmark = models.CharField(max_length=225, blank=True, null=True)
    company_name = models.CharField(max_length=225, blank=True, null=True)
    industry = models.CharField(max_length=225, blank=True, null=True)
    size = models.IntegerField(default=0)
    channel = models.CharField(max_length=225, blank=True, null=True)
    access = models.TextField(null=True, blank=True)
    cac_num = models.CharField(max_length=225, blank=True, null=True)
    company_wallet = models.CharField(max_length=225, blank=True, null=True)
    pin = models.CharField(max_length=225, blank=True, null=True)
    password = models.CharField(max_length=225, blank=True, null=True)
    implementation_steps = models.CharField(
        max_length=225, choices=STEP_CHOICES, default="user_creation"
    )
    user_creation_data = models.TextField(blank=True, null=True)
    verify_account_data = models.TextField(blank=True, null=True)
    create_transaction_pin_data = models.TextField(blank=True, null=True)
    create_paybox_user_data = models.TextField(blank=True, null=True)
    create_company_data = models.TextField(blank=True, null=True)

    @classmethod
    def create_user(cls, user_data: dict):

        onboarding_mgr = LibertyPayOnboardingMgr()

        password = generate_password()
        pin = generate_transaction_pin()

        object_instance = cls.objects.create(
            first_name=user_data.get("first_name"),
            last_name=user_data.get("last_name"),
            email=user_data.get("email"),
            phone_number=user_data.get("phone_number"),
            username=user_data.get("username"),
            state=user_data.get("state"),
            lga=user_data.get("lga"),
            size=user_data.get("size"),
            nearest_landmark=user_data.get("nearest_landmark"),
            street=user_data.get("street"),
            company_name=user_data.get("company_name"),
            company_wallet=user_data.get("company_wallet_type"),
            channel=user_data.get("channel"),
            cac_num=user_data.get("cac_num"),
            password=password,
            pin=pin,
        )

        result = onboarding_mgr.sign_up(
            first_name=user_data.get("first_name"),
            last_name=user_data.get("last_name"),
            email=user_data.get("email"),
            phone_number=user_data.get("phone_number"),
            username=user_data.get("username"),
            state=user_data.get("state"),
            lga=user_data.get("lga"),
            nearest_landmark=user_data.get("nearest_landmark"),
            street=user_data.get("street"),
            password=password,
            pin=pin,
        )

        object_instance.save()
        # object_instance.user_creation_data = result
        if result:
            logger.debug(f"Result received: {result}")
            object_instance.user_creation_data = json.dumps(result)
        else:
            object_instance.user_creation_data = json.dumps(
                {"error": "No data returned"}
            )

        object_instance.save()
        success_code = [200, 201]
        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")

            passcode = data.get("passcode")
            access = data.get("access")

            object_instance.implementation_steps = "verify_account"
            object_instance.access = access

            object_instance.save()

            object_instance.verify_account(passcode=passcode)
        else:
            pass

        return result

    def verify_account(self, passcode):

        onboarding_mgr = LibertyPayOnboardingMgr()
        result = onboarding_mgr.verify_user(self.email, passcode)

        self.verify_account_data = json.dumps(result)
        self.save()
        success_code = [200, 201]
        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")
            self.implementation_steps = "create_transaction_pin"
            self.save()
            self.create_transaction_pin()

        else:
            pass

    def create_transaction_pin(self):
        onboarding_mgr = LibertyPayOnboardingMgr()
        result = onboarding_mgr.create_transaction_pin(
            transaction_pin=self.pin, token=self.access
        )

        self.create_transaction_pin_data = json.dumps(result)
        self.save()
        success_code = [200, 201]
        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")
            self.implementation_steps = "create_paybox_user"
            self.save()
            self.create_paybox_user()

    def create_paybox_user(self):

        onboarding_mgr = LibertyPayOnboardingMgr()
        result = onboarding_mgr.fetch_user_details(token=self.access)
        self.create_paybox_user_data = json.dumps(result)
        self.save()

        success_code = [200, 201]

        if (
            result.get("status") == "success"
            and result.get("status_code") in success_code
        ):
            data = result.get("response")
            pprint(data)
            user_data = data.get("user_data")
            accounts_data = data.get("accounts_data")
            # print(accounts_data,"\n\n")
            # liberty_pay_acct_detail = get_account_details(account_data=accounts_data)
            # account_name = liberty_pay_acct_detail.get("account_name") if liberty_pay_acct_detail else None
            # bank_name = liberty_pay_acct_detail.get("bank_name")
            # account_no = liberty_pay_acct_detail.get("account_number") if liberty_pay_acct_detail else None
            self.implementation_steps = "create_company"
            self.save()

            liberty_pay_id = user_data.get("id")
            user_qs = User.objects.filter(liberty_pay_id=liberty_pay_id)
            if user_qs.exists():
                user = user_qs.first()

            else:
                user = User.objects.create(
                    liberty_pay_id=liberty_pay_id,
                    liberty_pay_customer_id=user_data.get("customer_id"),
                    phone_no=user_data.get("phone_number"),
                    email=user_data.get("email"),
                    first_name=user_data.get("first_name"),
                    last_name=user_data.get("last_name"),
                    bvn_number=user_data.get("bvn_number"),
                    bvn_first_name=user_data.get("bvn_first_name"),
                    bvn_last_name=user_data.get("bvn_last_name"),
                    account_no=None,
                    account_name=None,
                    street=user_data.get("street"),
                    state=user_data.get("state"),
                    lga=user_data.get("lga"),
                    nearest_landmark=user_data.get("nearest_landmark"),
                    gender=user_data.get("gender"),
                    is_active=bool(user_data.get("is_active")),
                    liberty_pay_customer_status=bool(user_data.get("is_active")),
                    liberty_pay_user_type=user_data.get("type_of_user"),
                    bank=None,
                    bank_code="",
                )
            self.create_company(user=user)

    def create_company(self, user):
        company_data = {
            "company_name": self.company_name,
            "industry": self.industry,
            "cac_num": self.cac_num,
            "size": self.size,
            "wallet_type": self.company_wallet,
        }

        company = req.Company.create_company(
            user=user, validated_data=company_data, transaction_pin=self.pin
        )

        """
        After company creation, fetch the company email, find a matching leads in the leads table.
        Loop through the leads and update the onboard_status
        
        """
        company_email = self.email

        leads = perf.Lead.objects.filter(company_email=company_email)
        for lead in leads:
            if lead.onboard_status == "AWAITING_ONBOARDING":
                lead.onboard_status = "ONBOARDING"
                lead.save()

    def tie_subscription_to_company(self):
        """
        This method ties the subscription to the company based on the payment status of invoices.
        """
        company_email = self.email
        leads_qs = perf.Lead.objects.filter(company_email=company_email)
        invoices = Invoice.objects.filter(leads__in=leads_qs)

        for invoice in invoices:
            if invoice.payment_status in ["paid", "paid_excess"]:
                subscription_modules = SubscriptionModule.objects.filter(invoice=invoice)

                for subscription_module in subscription_modules:
                    subscription_module.company = self

                    # Calculate the expiry date and set active status
                    expiry_date = subscription_module.calculate_expiry_date()
                    subscription_module.is_active = expiry_date > timezone.now()

                    subscription_module.save()

                # Set invoice as active if any of the subscription modules are active
                invoice.is_active = subscription_modules.filter(is_active=True).exists()
                invoice.save()

        self.save()





