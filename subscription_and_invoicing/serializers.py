from django.views.decorators.csrf import csrf_exempt
from rest_framework import serializers
from datetime import datetime, timedelta, date
# from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Sum
from django.db import models
from helpers.reusable_functions import is_valid_uuid
from invoicing import models as inv
from invoicing.models import InvoiceItem
from requisition.models import Company
from sales_app.models import Customer
from stock_inventory.models import Branch
from subscription_and_invoicing import models as subscription
from performance_sales_metrics_dashboard import models as perf
from django.conf import settings
from django.db import transaction
from decimal import Decimal
from django.contrib.auth import get_user_model

from subscription_and_invoicing.models import Invoice, InvoiceAudit, InvoiceModuleDetail, InvoiceTransaction, Module, \
    SubscriptionPlan, UserCompanyDetails, ModuleSubscription, CompanySubscription

from subscription_and_invoicing.models import UserCompanyDetails

User = get_user_model()


class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields =  "__all__"


valid_plans = SubscriptionPlan.objects.filter(is_active=True)
valid_durations = valid_plans.values_list('duration_months', flat=True).distinct()


class SubscriptionModuleSerializer(serializers.Serializer):
    module_id = serializers.PrimaryKeyRelatedField(queryset=Module.objects.all(), source="module")
    plan = serializers.PrimaryKeyRelatedField(queryset=valid_plans)

    def validate(self, attrs):
        return attrs  # No need to check availability since all modules are available for their plans

    def validate_plan(self, value):
        if value.duration_months not in valid_durations:
            raise serializers.ValidationError(f"Invalid duration. Only {list(valid_durations)} months are allowed.")
        return value


class CreateInvoiceSerializer(serializers.Serializer):
    subscription_modules = SubscriptionModuleSerializer(many=True)

    def validate_subscription_modules(self, value):
        if not value:
            raise serializers.ValidationError("At least one module must be selected.")
        return value

    @transaction.atomic
    def validate(self, attrs):
        request = self.context.get("request")
        user = request.user

        company = user.default_company
        branch = user.default_branch

        if not company or not branch:
            raise serializers.ValidationError("Customer's company or branch is not properly set up.")

        customer = Customer.objects.filter(
            company=company,
            branch=branch,
            email=user.email
        ).last()

        if not customer:
            customer = Customer.objects.create(
                company=company,
                branch=branch,
                email=user.email,
                created_by=user,
                name=user.get_full_name(),
                phone=getattr(user, 'phone_number', None),
            )

        total_price = 0

        company_subscription = CompanySubscription.objects.create(
            company=company,
            status='inactive',
            created_by=user
        )

        module_subscriptions = []
        for module_data in attrs["subscription_modules"]:
            module = module_data["module"]
            plan = module_data["plan"]

            module_subscription = ModuleSubscription.objects.create(
                company_subscription=company_subscription,
                module=module,
                plan=plan,
                is_active=False,  # Not active yet
                start_date=timezone.now(),
                end_date=timezone.now() + timedelta(days=plan.duration_months * 30),
                pending_balance=plan.price,
                status="not_started"  # Set status explicitly
            )
            module_subscriptions.append(module)

            total_price += plan.price

        company_subscription.modules.set(module_subscriptions)

        #  Create the invoice ONCE and capture batch_id
        invoice = self.create_invoice(company, attrs["subscription_modules"], customer, user)

        #  Save batch_id in invoice_ref fields
        company_subscription.invoice_ref = invoice.batch_id
        company_subscription.save()
        ModuleSubscription.objects.filter(company_subscription=company_subscription).update(
            invoice_ref=invoice.batch_id
        )

        invoice = self.create_invoice(
            company=company,
            subscription_modules=attrs["subscription_modules"],
            customer=customer,
            created_by=user
        )

        return {
            "invoice_reference": invoice.invoice_reference,
            "amount_payable": invoice.balance_due,
            "total_price": total_price,
            "subscription_modules": [
                {
                    "name": item.module.name,
                    "price": item.unit_price,
                    "total_price": item.total_price,
                }
                for item in invoice.invoicemoduledetail_set.all()
            ],
            "customer_name": customer.name,
            "start_date": invoice.start_date,
            "expiry_date": invoice.expiry_date,
        }

    @staticmethod
    @transaction.atomic
    def create_invoice(company, subscription_modules, customer, created_by):
        total_amount_due = 0
        batch_id = Invoice.generate_batch_id()

        invoice = Invoice.objects.create(
            company=company,
            created_by=created_by,
            total_amount=0,
            balance_due=0,
            start_date=timezone.now(),
            expiry_date=timezone.now() + timedelta(days=12 * 30),
            batch_id=batch_id,
        )

        items = []
        for module_data in subscription_modules:
            module = module_data["module"]
            plan = module_data["plan"]

            total_price = plan.price
            total_amount_due += total_price

            InvoiceModuleDetail.objects.create(
                invoice=invoice,
                module=module,
                quantity=1,
                unit_price=total_price,
                discount=0.0,
                total_price=total_price,
            )

            items.append(
                {
                    "item_description": module.name,
                    "unit_price": total_price,
                    "quantity": 1,
                    "discount": 0.0,
                }
            )

        invoice.total_amount = total_amount_due
        invoice.balance_due = max(0, total_amount_due - invoice.settled_amount)
        invoice.save()

        InvoiceItem.generate_invoice(
            company=company,
            items=items,
            created_by=created_by,
            customer=customer,
            tax_rate=0.0,
            description="Subscription Invoice",
            discount_value=0.0,
            delivery_fee=0.0,
            received_part_payment=False,
            amount_received=0.0,
            branch=customer.branch,
            batch_id=batch_id,
        )

        return invoice


class ModuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Module
        fields = "__all__"



# class SubscriptionModuleSerializer(serializers.ModelSerializer):
#     subscription_type_name = serializers.CharField(source='subscription_type.name')
#
#     class Meta:
#         model = subscription.SubscriptionModule
#         fields = ['subscription_type_name', 'duration_in_months', 'start_date', 'invoice']


class InvoiceSerializer(serializers.ModelSerializer):
    modules = SubscriptionModuleSerializer(many=True, read_only=True)

    class Meta:
        model = subscription.Invoice
        # fields = [
        #     'id', 'company', 'modules', 'amount', 'amount_due',
        #     'start_date', 'expiry_date', 'sales_officer',
        #     'amount_brought_forward', 'used_excess_payment',
        #     'payment_status'
        # ]
        exclude = ["module", "settled_amount", "invoice_reference", ]


# class InvoiceSerializer(serializers.ModelSerializer):
#     modules = SubscriptionModuleSerializer(many=True, read_only=True)
#     amount_due = serializers.SerializerMethodField()
#
#     class Meta:
#         model = subscription.Invoice
#         exclude = ["module", "settled_amount", "invoice_reference", ]
#
#     def get_amount_due(self, obj):
#         # Assuming you want to calculate amount_due based on some related data.
#         # Here's a simplified example. Adjust the query according to your actual data structure.
#
#         # Example: Querying related `SubscriptionModule` instances linked to this invoice
#         sub_invoice = subscription.Invoice.objects.filter(id=obj.id).first()
#
#         if sub_invoice:
#             # Your logic for amount_due based on the sub_invoice
#             # For example, summing prices from related modules
#             total_amount = sub_invoice.modules.aggregate(total=models.Sum('subscription_type__price'))['total'] or 0
#             amount_due = max(0, total_amount - sub_invoice.settled_amount)
#             return amount_due
#         return None


# class InvoiceTransactionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = subscription.InvoiceTransaction
#         fields = '__all__'
#

class InvoiceTransactionSerializer(serializers.ModelSerializer):
    payment_details = serializers.SerializerMethodField()

    class Meta:
        model = subscription.InvoiceTransaction
        fields = '__all__'

    def get_payment_details(self, obj):
        # This method retrieves the payment details from the context if available.
        return self.context.get('payment_details')

class GetInvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = subscription.Invoice
        fields = '__all__'
class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'


# class SubscriptionModuleSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = subscription.SubscriptionModule
#         fields = '__all__'


class SubscriptionTypesSerializer(serializers.ModelSerializer):
    class Meta:
        model = subscription.SubscriptionType
        fields = '__all__'


class CommissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = subscription.Commission
        fields = '__all__'


class InvoiceUpdateSerializer(serializers.ModelSerializer):
    subscription_types = serializers.ListField(
        child=serializers.IntegerField(), write_only=True
    )
    duration_in_months = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = subscription.Invoice
        fields = ['subscription_types', 'duration_in_months']  # Include other fields if necessary

    def update(self, instance, validated_data):
        subscription_types = validated_data.get('subscription_types', [])
        duration_in_months = validated_data.get('duration_in_months', 3)  # Default to 3 months if not provided

        # Current modules associated with the invoice
        existing_modules = subscription.SubscriptionModule.objects.filter(invoice=instance)

        # List of existing subscription type IDs
        existing_subscription_type_ids = [module.subscription_type.id for module in existing_modules]

        # Remove modules that are no longer in the payload
        modules_to_remove = [module for module in existing_modules if
                             module.subscription_type.id not in subscription_types]
        for module in modules_to_remove:
            instance.amount -= module.subscription_type.price
            instance.amount_due -= module.subscription_type.price
            module.delete()

        # Add or update the modules
        for subscription_type_id in subscription_types:
            if subscription_type_id not in existing_subscription_type_ids:
                subscription_type = subscription.SubscriptionType.objects.get(id=subscription_type_id)
                subscription.SubscriptionModule.objects.create(
                    subscription_type=subscription_type,
                    duration_in_months=duration_in_months,  # Use the provided duration
                    invoice=instance
                )
                instance.amount += subscription_type.price
                instance.amount_due += subscription_type.price
            else:
                # Update the duration for existing modules if needed
                module = subscription.SubscriptionModule.objects.get(
                    subscription_type_id=subscription_type_id, invoice=instance
                )
                if module.duration_in_months != duration_in_months:
                    module.duration_in_months = duration_in_months
                    module.save()

        # Ensure amount_due is correctly calculated
        instance.recalculate_amount_due()
        instance.save()

        return instance

    def to_representation(self, instance):
        return InvoiceSerializer(instance).data


# class InvoiceUpdateSerializer(serializers.Serializer):
#     subscription_types = serializers.ListField()
#
#     def update(self, instance, validated_data):
#         subscription_types = validated_data.get('subscription_types')
#         subscription_type_qs = subscription.SubscriptionType.objects.filter(id__in=subscription_types)
#         duration = instance.subscriptionmodule_set.first().subscritpion_in_months
#         for subscription_type in subscription_type_qs:
#             module = models.SubscriptionModule.objects.get_or_create(subscritpion_type=subscription_type,
#                                                               subscritpion_in_months=duration, invoice=instance)
#         instance.recalculate_amount_due()
#
#         return instance

# class Meta:
#     model = Invoice
#     fields = ['amount', 'start_date', 'expiry_date', 'is_active', 'paid','module']
#     read_only_fields = ['company', 'module', 'settled_amount', 'invoice_reference', 'paid']

# def validate(self, data):
#     if 'amount' in data and data['amount'] <= 0:
#         raise serializers.ValidationError("Amount must be greater than zero.")
#     return data

# def validate_module(self, value):
#     if value is not None and not isinstance(value, SubscriptionModule):
#         try:
#             return SubscriptionModule.objects.get(pk=value)
#         except SubscriptionModule.DoesNotExist:
#             raise serializers.ValidationError("Invalid module ID")
#     return value


class InvoiceListSerializer(serializers.ModelSerializer):
    company_name = serializers.CharField(source='company.name', read_only=True)

    class Meta:
        model = subscription.Invoice
        fields = ['id', 'invoice_reference', 'company_name', 'amount', 'amount_due',
                  'settled_amount', 'start_date', 'expiry_date', 'paid', 'is_active']



class UserCompanyDetailsSerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length=225, allow_blank=True, required=False)
    last_name = serializers.CharField(max_length=225, allow_blank=True, required=False)
    email = serializers.EmailField()
    phone_number = serializers.CharField(max_length=13, allow_blank=True, required=False)
    username = serializers.CharField(max_length=225, allow_blank=True, required=False)
    state = serializers.CharField(max_length=225, allow_blank=True, required=False)
    lga = serializers.CharField(max_length=225, allow_blank=True, required=False)
    street = serializers.CharField(max_length=225, allow_blank=True, required=False)
    nearest_landmark = serializers.CharField(max_length=225, allow_blank=True, required=False)
    company_name = serializers.CharField(max_length=225, allow_blank=True, required=False)
    industry = serializers.CharField(max_length=225, allow_blank=True, required=False)
    size = serializers.IntegerField()
    channel = serializers.CharField(max_length=225, allow_blank=True, required=False)
    cac_num = serializers.CharField(max_length=225, allow_blank=True, required=False)
    company_wallet_type = serializers.CharField(max_length=225, allow_blank=True, required=False)
    implementation_steps = serializers.ChoiceField(choices=UserCompanyDetails.STEP_CHOICES, default="user_creation")
    # user_creation_data = serializers.CharField()
    # verify_account_data = serializers.CharField()
    # create_transaction_pin_data = serializers.CharField()
    # create_paybox_user_data = serializers.CharField()
    # create_company_data = serializers.CharField()
