from django.core.management.base import BaseCommand
from accounting.models import AccountType, Account
from accounting.utils import generate_account_code


ACCOUNT_DATA = {
    "Asset": [
        # {"name": "Cash/Bank", "statement_type": "Balance Sheet", "statement_category": "Cash and Cash Equivalents"}
        # {
        #     "name": "Suspense Account",
        #     "statement_type": "Balance Sheet",
        #     "statement_category": "Cash and Cash Equivalents",
        # }
    ],
    "Expense": [
        # {"name": "Employee Benefits Expense", "statement_type": "Income Statement", "statement_category": "Operating Expenses"}
        {
            "name": "Amortization",
            "statement_type": "Income Statement",
            "statement_category": "Operating Expenses",
        },
        {
            "name": "Shipping Charge",
            "statement_type": "Income Statement",
            "statement_category": "Operating Expenses",
        },
    ],
    "Income": [
        {
            "name": "Exchange Gain",
            "statement_type": "Income Statement",
            "statement_category": "Revenue",
        },
        {
            "name": "Uncategorized income",
            "statement_type": "Income Statement",
            "statement_category": "Revenue",
        },
    ],
}

ACCOUNT_RENAME_MAP = {
    "Discount": "Discount Received",
    "Exchange Gain or Loss": "Exchange Loss",
    "Uncategorized": "Uncategorized expense",
}


class Command(BaseCommand):
    help = "Recreates all account types and accounts."

    def handle(self, *args, **kwargs):

        # AccountType.objects.all().delete()
        # Account.objects.all().delete()
        # Step 1: Create Account Types
        account_types = {
            "Asset": "Debit",
            "Liability": "Credit",
            "Equity": "Credit",
            "Income": "Credit",
            "Expense": "Debit",
            "Cost of Goods Sold": "Debit",
        }

        for old_name, new_name in ACCOUNT_RENAME_MAP.items():
            try:
                account = Account.objects.get(name=old_name)
                account.name = new_name
                account.save()
                self.stdout.write(
                    self.style.SUCCESS(f"Renamed account: {old_name} to {new_name}")
                )
            except Account.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f"Account with name '{old_name}' does not exist. Skipping."
                    )
                )

        for name, balance in account_types.items():
            account_type, created = AccountType.objects.get_or_create(
                name=name,
                defaults={
                    "description": f"Default account type for {name}",
                    "standard_balance": balance,
                },
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f"Created account type: {name}"))

        # Step 2: Create Accounts
        for account_type_name, accounts in ACCOUNT_DATA.items():
            try:
                account_type = AccountType.objects.get(name=account_type_name)
            except AccountType.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(
                        f"AccountType '{account_type_name}' does not exist. Skipping."
                    )
                )
                continue

            for account in accounts:
                account_name = account["name"]
                statement_type = account["statement_type"]
                statement_category = account["statement_category"]

                # Check if the account already exists
                existing_account = Account.objects.filter(name=account_name).first()

                if existing_account:
                    # Update existing account information, except for the account code
                    existing_account.account_type = account_type
                    existing_account.statement_type = statement_type
                    existing_account.statement_category = statement_category
                    existing_account.description = f"Default account for {account_name}"
                    existing_account.paybox_default = True
                    existing_account.is_active = True
                    existing_account.save()
                    self.stdout.write(
                        self.style.SUCCESS(f"Updated account: {account_name}")
                    )
                else:
                    # Create a new account
                    account_code = generate_account_code()
                    Account.objects.create(
                        name=account_name,
                        account_type=account_type,
                        account_code=account_code,
                        statement_type=statement_type,
                        statement_category=statement_category,
                        description=f"Default account for {account_name}",
                        paybox_default=True,
                        is_active=True,
                    )
                    self.stdout.write(
                        self.style.SUCCESS(f"Created account: {account_name}")
                    )
