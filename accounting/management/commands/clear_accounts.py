from django.core.management.base import BaseCommand
from accounting.models import AccountType, Account, JournalEntry, JournalLine
from accounting.utils import generate_account_code


class Command(BaseCommand):
    help = "Recreates all account types and accounts."

    def handle(self, *args, **kwargs):

        JournalLine.objects.all().delete()
        JournalEntry.objects.all().delete()
        AccountType.objects.all().delete()
        Account.objects.all().delete()

        self.stdout.write(self.style.SUCCESS("cleared all accounts and account types"))
