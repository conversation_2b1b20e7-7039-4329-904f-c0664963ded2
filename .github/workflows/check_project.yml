name: Dependency Management and Tests

on: [pull_request]

jobs:
  build-lint-and-test:
    runs-on: ubuntu-20.04

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: github_actions
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready 
          --health-interval 10s 
          --health-timeout 5s 
          --health-retries 5
      
      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python 3.10
        uses: actions/setup-python@v3
        with:
          python-version: 3.10.0

      - name: Install dependencies
        run: |
          python3 -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          python -m spacy download en_core_web_sm
          pip install flake8

      - name: Check for abnormalities
        env:
          AGENCY_BANKING_BASE_URL: ${{secrets.AGENCY_BANKING_BASE_URL}}
          AGENCY_SECRETE_KEY: ${{secrets.AGENCY_SECRETE_KEY}}
          AWS_ACCESS_KEY_ID: ${{secrets.AWS_ACCESS_KEY_ID}}
          AWS_SECRET_ACCESS_KEY: ${{secrets.AWS_SECRET_ACCESS_KEY}}
          BASE_URL: ${{secrets.BASE_URL}}
          BITLY_ACCESS_TOKEN: ${{secrets.BITLY_ACCESS_TOKEN}}
          BREVO_API_KEY: ${{secrets.BREVO_API_KEY}}
          CHAT_GPT_API: ${{secrets.CHAT_GPT_API}}
          COMMISSION_ACCOUNT_NUMBER_VFD: ${{secrets.COMMISSION_ACCOUNT_NUMBER_VFD}}
          DATABASE_HOST: ${{secrets.DATABASE_HOST}}
          DATABASE_NAME: ${{secrets.DATABASE_NAME}}
          DATABASE_PORT: ${{secrets.DATABASE_PORT}}
          DATABASE_USER: ${{secrets.DATABASE_USER}}
          DATABASE_PASSWORD: ${{secrets.DATABASE_PASSWORD}}
          EMAIL_ENCRYPTION_KEY: ${{secrets.EMAIL_ENCRYPTION_KEY}}
          ENVIRONMENT: ${{secrets.ENVIRONMENT}}
          FLOAT_ACCOUNT_NUMBER_VFD: ${{secrets.FLOAT_ACCOUNT_NUMBER_VFD}}
          INSTANT_WAGE_ACCOUNT_ID: ${{secrets.INSTANT_WAGE_ACCOUNT_ID}}
          INSTANT_WAGE_USER_ID: ${{secrets.INSTANT_WAGE_USER_ID}}
          LIBERTY_PAY_CORPORATE_TOKEN: ${{secrets.LIBERTY_PAY_CORPORATE_TOKEN}}
          LIBERTY_PAY_PLUS_BASE_URL: ${{secrets.LIBERTY_PAY_PLUS_BASE_URL}}
          LIBERTY_PAY_PLUS_PROD_BASE_URL: ${{secrets.LIBERTY_PAY_PLUS_PROD_BASE_URL}}
          LIBERTY_PAY_PLUS_DEV_BASE_URL: ${{secrets.LIBERTY_PAY_PLUS_DEV_BASE_URL}}
          MAILGUN_APIKEY: ${{secrets.MAILGUN_APIKEY}}
          MAILGUN_URL: ${{secrets.MAILGUN_URL}}
          MANUAL_PAYOUT_USER_EMAIL: ${{secrets.MANUAL_PAYOUT_USER_EMAIL}}
          MONO_WEBHOOK_SECRET: ${{secrets.MONO_WEBHOOK_SECRET}}
          OPENAI_API_KEY: ${{secrets.OPENAI_API_KEY}}
          OTP_SECRET: ${{secrets.OTP_SECRET}}
          PAYBOX_PAYMENT_LINK: ${{secrets.PAYBOX_PAYMENT_LINK}}
          PAYBOX_WHISPER_SMS_API_KEY: ${{secrets.PAYBOX_WHISPER_SMS_API_KEY}}
          PAYBOX_WHISPER_SMS_TOKEN: ${{secrets.PAYBOX_WHISPER_SMS_TOKEN}}
          PAYSTACK_SECRET_KEY: ${{secrets.PAYSTACK_SECRET_KEY}}
          REMINDER_TEMPLATE: ${{secrets.REMINDER_TEMPLATE}}
          REQUISITION_DISBURSEMENT_TEMPLATE: ${{secrets.REQUISITION_DISBURSEMENT_TEMPLATE}}
          REQUISITION_INSUFFICIENT_PURSE_BAL: ${{secrets.REQUISITION_INSUFFICIENT_PURSE_BAL}}
          SECRET_WEB_TOKEN: ${{secrets.SECRET_WEB_TOKEN}}
          SEEDS_PENNIES_SMS_WHISPER_API_KEY: ${{secrets.SEEDS_PENNIES_SMS_WHISPER_API_KEY}}
          SUPPLIER_INVITE_FRONTEND_URL: ${{secrets.SUPPLIER_INVITE_FRONTEND_URL}}
          TOKEN_URL: ${{secrets.TOKEN_URL}}
          VFD_ACCESS_TOKEN_LIVE: ${{secrets.VFD_ACCESS_TOKEN_LIVE}}
          VFD_ACCESS_TOKEN_TEST: ${{secrets.VFD_ACCESS_TOKEN_TEST}}
          VFD_WALLET_CREDENTIALS_LIVE: ${{secrets.VFD_WALLET_CREDENTIALS_LIVE}}
          VFD_WALLET_CREDENTIALS_TEST: ${{secrets.VFD_WALLET_CREDENTIALS_TEST}}
          WHISPER_TRANSACTIONAL_URL: ${{secrets.WHISPER_TRANSACTIONAL_URL}}
          WHISPER_KEY: ${{secrets.WHISPER_KEY}}
          YOU_VERIFY_COMPANY_TOKEN: ${{secrets.YOU_VERIFY_COMPANY_TOKEN}}
          ZOOM_ACCOUNT_ID: ${{secrets.ZOOM_ACCOUNT_ID}}
          ZOOM_CLIENT_ID: ${{secrets.ZOOM_CLIENT_ID}}
          ZOOM_CLIENT_SECRET: ${{secrets.ZOOM_CLIENT_SECRET}}
          ZOOM_SDK_CLIENT_ID: ${{secrets.ZOOM_SDK_CLIENT_ID}}
          ZOOM_SDK_CLIENT_SECRET: ${{secrets.ZOOM_SDK_CLIENT_SECRET}}
          ZOOM_URL: ${{secrets.ZOOM_URL}}
        run: |
          bash check_project.sh

      # - name: Create .env file
      #   run: |
      #     echo "AGENCY_BANKING_BASE_URL=${{secrets.AGENCY_BANKING_BASE_URL}}" >> .env
      #     echo "AGENCY_SECRETE_KEY=${{secrets.AGENCY_SECRETE_KEY}}" >> .env
      #     echo "AWS_ACCESS_KEY_ID=${{secrets.AWS_ACCESS_KEY_ID}}" >> .env
      #     echo "AWS_SECRET_ACCESS_KEY=${{secrets.AWS_SECRET_ACCESS_KEY}}" >> .env
      #     echo "BASE_URL=${{secrets.BASE_URL}}" >> .env
      #     echo "BITLY_ACCESS_TOKEN=${{secrets.BITLY_ACCESS_TOKEN}}" >> .env
      #     echo "BREVO_API_KEY=${{secrets.BREVO_API_KEY}}" >> .env
      #     echo "COMMISSION_ACCOUNT_NUMBER_VFD=${{secrets.COMMISSION_ACCOUNT_NUMBER_VFD}}" >> .env
      #     echo "DATABASE_HOST=${{secrets.DATABASE_HOST}}" >> .env
      #     echo "DATABASE_NAME=${{secrets.DATABASE_NAME}}" >> .env
      #     echo "DATABASE_PORT=${{secrets.DATABASE_PORT}}" >> .env
      #     echo "DATABASE_USER=${{secrets.DATABASE_USER}}" >> .env
      #     echo "DATABASE_PASSWORD=${{secrets.DATABASE_PASSWORD}}" >> .env
      #     echo "ENVIRONMENT=${{secrets.ENVIRONMENT}}" >> .env
      #     echo "FLOAT_ACCOUNT_NUMBER_VFD=${{secrets.FLOAT_ACCOUNT_NUMBER_VFD}}" >> .env
      #     echo "INSTANT_WAGE_ACCOUNT_ID=${{secrets.INSTANT_WAGE_ACCOUNT_ID}}" >> .env
      #     echo "INSTANT_WAGE_USER_ID=${{secrets.INSTANT_WAGE_USER_ID}}" >> .env
      #     echo "LIBERTY_PAY_CORPORATE_TOKEN=${{secrets.LIBERTY_PAY_CORPORATE_TOKEN}}" >> .env
      #     echo "LIBERTY_PAY_PLUS_BASE_URL=${{secrets.LIBERTY_PAY_PLUS_BASE_URL}}" >> .env
      #     echo "LIBERTY_PAY_PLUS_PROD_BASE_URL=${{secrets.LIBERTY_PAY_PLUS_PROD_BASE_URL}}" >> .env
      #     echo "LIBERTY_PAY_PLUS_DEV_BASE_URL=${{secrets.LIBERTY_PAY_PLUS_DEV_BASE_URL}}" >> .env
      #     echo "MAILGUN_APIKEY=${{secrets.MAILGUN_APIKEY}}" >> .env
      #     echo "MAILGUN_URL=${{secrets.MAILGUN_URL}}" >> .env
      #     echo "MONO_WEBHOOK_SECRET=${{secrets.MONO_WEBHOOK_SECRET}}" >> .env
      #     echo "OTP_SECRET=${{secrets.OTP_SECRET}}" >> .env
      #     echo "PAYBOX_PAYMENT_LINK=${{secrets.PAYBOX_PAYMENT_LINK}}" >> .env
      #     echo "PAYBOX_WHISPER_SMS_API_KEY=${{secrets.PAYBOX_WHISPER_SMS_API_KEY}}" >> .env
      #     echo "PAYBOX_WHISPER_SMS_TOKEN=${{secrets.PAYBOX_WHISPER_SMS_TOKEN}}" >> .env
      #     echo "PAYSTACK_SECRET_KEY=${{secrets.PAYSTACK_SECRET_KEY}}" >> .env
      #     echo "REMINDER_TEMPLATE=${{secrets.REMINDER_TEMPLATE}}" >> .env
      #     echo "REQUISITION_DISBURSEMENT_TEMPLATE=${{secrets.REQUISITION_DISBURSEMENT_TEMPLATE}}" >> .env
      #     echo "REQUISITION_INSUFFICIENT_PURSE_BAL=${{secrets.REQUISITION_INSUFFICIENT_PURSE_BAL}}" >> .env
      #     echo "SECRET_WEB_TOKEN=${{secrets.SECRET_WEB_TOKEN}}" >> .env
      #     echo "VFD_ACCESS_TOKEN_LIVE=${{secrets.VFD_ACCESS_TOKEN_LIVE}}" >> .env
      #     echo "VFD_ACCESS_TOKEN_TEST=${{secrets.VFD_ACCESS_TOKEN_TEST}}" >> .env
      #     echo "VFD_WALLET_CREDENTIALS_LIVE=${{secrets.VFD_WALLET_CREDENTIALS_LIVE}}" >> .env
      #     echo "VFD_WALLET_CREDENTIALS_TEST=${{secrets.VFD_WALLET_CREDENTIALS_TEST}}" >> .env
      #     echo "WHISPER_TRANSACTIONAL_URL=${{secrets.WHISPER_TRANSACTIONAL_URL}}" >> .env
      #     echo "WHISPER_KEY=${{secrets.WHISPER_KEY}}" >> .env
      #     echo "YOU_VERIFY_COMPANY_TOKEN=${{secrets.YOU_VERIFY_COMPANY_TOKEN}}" >> .env
      #     bash check_project.sh
      #     ls -a

      - name: Lint
        run: |
          flake8 .
        continue-on-error: true

      # - name: Run tests
      #   env:
      #     DATABASE_HOST: localhost
      #     DATABASE_NAME: github_actions
      #     DATABASE_PORT: 5432
      #     DATABASE_USER: postgres
      #     DATABASE_PASSWORD: postgres
      #     REDIS_HOST: localhost
      #     REDIS_PORT: 6379
        # run: |
          # python manage.py makemigrations invoicing leave_management linkshortener payroll_app performance_sales_metrics_dashboard sales_app stock_inventory subscription_and_invoicing account cart_management clock_app core finance_system instant_web requisition
          # python manage.py migrate
          # python manage.py test
