from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from instant_web import models, resources


# Register your model(s) here.
class InstantWebResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.InstantWebResource
    search_fields = [
        "company__company_name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class QRCodeAdmin(admin.ModelAdmin):
    list_display = [field.name for field in models.QRCode._meta.concrete_fields]
    search_fields = ['name', 'company__name', 'branch__name', 'category__name']


class TableAdmin(admin.ModelAdmin):
    list_display = [field.name for field in models.QRCode._meta.concrete_fields]
    search_fields = ['name', 'company__name', 'branch__name', 'category__name']


admin.site.register(models.InstantWeb, InstantWebResourceAdmin)
admin.site.register(models.QRCode, QRCodeAdmin)
admin.site.register(models.Table, TableAdmin)


