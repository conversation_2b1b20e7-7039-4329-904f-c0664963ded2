from django.test import TestCase

# Create your tests here.
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from .models import InstantWeb, QRCode, Table
from requisition.models import Company
from stock_inventory.models import Branch, Category
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseAPITestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='testpass', first_name='<PERSON>',
                                             last_name='<PERSON>e')
        self.client.force_authenticate(user=self.user)

        self.company = Company.objects.create(
            company_name="Test Company",
            user=self.user
        )

        self.branch = Branch.objects.create(
            name="Test Branch",
            company=self.company,
            created_by=self.user,
            address="123 Test Street",
            vat=7.5
        )

        self.category1 = Category.objects.create(name="Electronics", company=self.company, created_by=self.user)
        self.category2 = Category.objects.create(name="Books", company=self.company, created_by=self.user)


class QRCodeAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.qr_code = QRCode.objects.create(
            name="Test QR Code",
            company=self.company,
            branch=self.branch,
            all_categories=False,
            scans=10
        )
        self.qr_code.categories.set([self.category1])

    def test_create_qr_code(self):
        url = reverse('create_qrcode')
        data = {
            "name": "New QR Code",
            "company": self.company.id,
            "branch": self.branch.id,
            "categories": [self.category1.id, self.category2.id],
            "all_categories": False
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("code_url", response.data)
        self.assertTrue(QRCode.objects.filter(name="New QR Code").exists())

    def test_scan_qr_code(self):
        url = reverse('qr-code-link', kwargs={'pk': self.qr_code.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_302_FOUND)
        self.qr_code.refresh_from_db()
        self.assertEqual(self.qr_code.scans, 11)

    def test_list_qr_codes(self):
        url = reverse('qr_code_list')
        response = self.client.get(url, {'company': self.company.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], "Test QR Code")
        self.assertIn('total_qr_codes', response.data)
        self.assertIn('total_scans', response.data)

    def test_list_qr_codes_with_filters(self):
        url = reverse('qr_code_list')
        response = self.client.get(url, {'company': self.company.id, 'branch': self.branch.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url, {'company': self.company.id, 'categories': self.category1.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url, {'company': self.company.id, 'scans': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url, {'company': self.company.id, 'all_categories': False})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_partial_update_qr_code(self):
        url = reverse('qr_code_detail', kwargs={'pk': self.qr_code.id})
        data = {
            "name": "Partially Updated QR Code"
        }
        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.qr_code.refresh_from_db()
        self.assertEqual(self.qr_code.name, "Partially Updated QR Code")

    def test_delete_qr_code(self):
        url = reverse('qr_code_detail', kwargs={'pk': self.qr_code.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(QRCode.objects.filter(id=self.qr_code.id).exists())


class TableAPITestCase(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.table1 = Table.objects.create(
            name="Test Table 1",
            company=self.company,
            branch=self.branch,
            no_of_seats=6,
            scans=5,
            link="http://example.com/table1"
        )
        self.table2 = Table.objects.create(
            name="Test Table 2",
            company=self.company,
            branch=self.branch,
            no_of_seats=4,
            scans=8,
            link="http://example.com/table2"
        )
        self.table1.categories.set([self.category1])
        self.table2.categories.set([self.category2])

    def test_list_tables(self):
        url = reverse('table-list')
        response = self.client.get(url, {'company': self.company.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        self.assertIn('total_tables', response.data)
        self.assertIn('total_scans', response.data)

    def test_list_tables_with_filters(self):
        url = reverse('table-list')
        response = self.client.get(url, {'company': self.company.id, 'branch': self.branch.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

        response = self.client.get(url, {'company': self.company.id, 'categories': self.category1.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], "Test Table 1")

        response = self.client.get(url, {'company': self.company.id, 'scans': 8})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], "Test Table 2")

        response = self.client.get(url, {'company': self.company.id, 'all_categories': False})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_scan_table(self):
        url = reverse('qr-code-link', kwargs={'pk': self.table1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_302_FOUND)
        self.table1.refresh_from_db()
        self.assertEqual(self.table1.scans, 6)

    def test_partial_update_table(self):
        url = reverse('table-detail', kwargs={'pk': self.table1.id})
        data = {
            "name": "Updated Test Table 1"
        }
        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.table1.refresh_from_db()
        self.assertEqual(self.table1.name, "Updated Test Table 1")

    def test_delete_table(self):
        url = reverse('table-detail', kwargs={'pk': self.table1.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(Table.objects.filter(id=self.table1.id).exists())


class GetInstantWebCategoriesByBranchTest(BaseAPITestCase):
    def setUp(self):
        super().setUp()

        # Create InstantWeb and associate categories
        self.instant_web = InstantWeb.objects.create(
            company=self.company,
            branch=self.branch,
            created_by=self.user
        )
        self.instant_web.navigation_set_menu.add(self.category1, self.category2)

        # Define the URL for the endpoint
        self.url = reverse('instant-web-categories', args=[self.branch.id])

    def test_get_instant_web_categories_by_branch_success(self):
        response = self.client.get(self.url)

        # Ensure the response is successful
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Ensure the correct number of categories are returned
        self.assertEqual(len(response.data), 2)

        # Ensure the correct categories are returned
        category_names = [category['name'] for category in response.data]
        self.assertIn(self.category1.name, category_names)
        self.assertIn(self.category2.name, category_names)

    def test_get_instant_web_categories_by_branch_no_instant_web(self):
        # Remove the InstantWeb object to simulate no association
        self.instant_web.delete()

        response = self.client.get(self.url)

        # Ensure the response returns a 404 since the InstantWeb object does not exist
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['detail'], "No Instant web found for this branch.")

    def test_get_instant_web_categories_by_invalid_branch(self):
        # Use an invalid branch ID
        invalid_url = reverse('instant-web-categories', kwargs={'branch_id': '6d9edc39-042b-4c0c-9bd7-590c69174943'})

        response = self.client.get(invalid_url)

        # Ensure the response returns a 404 since the branch does not exist
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['detail'], "Branch not found.")
