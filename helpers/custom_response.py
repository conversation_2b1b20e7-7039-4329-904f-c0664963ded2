from rest_framework.response import Response as DRFResponse


# Create your custom response(s) here.
class InvalidResponse(Exception):
    pass


class Response:
    """
    Default Response object for all API calls.
    Returns Django's Rest Framework Response.
    Sample Response:
        {
            'status': str,
            'status_code': str,
            'data': dict / None,
            'errors': dict / None
        }
    """

    def __new__(cls, data=None, errors=None, status_code=None, *args, **kwargs):
        payload = cls.format(data, errors, status_code)
        return DRFResponse(payload, *args, **kwargs)

    @classmethod
    def format(cls, data, errors, status_code):
        data, errors = cls.validate(data, errors)
        status = 'success' if data and not errors else 'failure'
        if not data and not errors:
            raise InvalidResponse('Both data and errors cannot be None.')
        return dict(
            status=status,
            status_code=status_code,
            data=data,
            errors=errors
        )

    @classmethod
    def validate(cls, data, errors):
        try:
            data = None if data is None else dict(data)
            errors = None if errors is None else dict(errors)
            return (data, errors)
        except Exception as TypeError:
            raise InvalidResponse(
                'None or dict-like structure expected for both data and errors.')
