# import json
# import os
#
# expense_categories = [
#     'Rent',
#     'Mortgage',
#     'Utilities',
#     'Groceries',
#     'Transportation',
#     'Healthcare',
#     'Insurance',
#     'Entertainment',
#     'Dining Out',
#     'Shopping',
#     'Education',
#     'Travel',
#     'Savings',
#     'Investments',
#     'Retirement',
#     'Debt Payments',
#     'Loan Payments',
#     'Childcare',
#     'Pets',
#     'Home Maintenance',
#     'Clothing',
#     'Gym Membership',
#     'Subscriptions',
#     'Charitable Giving',
#     'Taxes',
#     'Financial Services',
#     'Personal Care',
#     'Gifts',
#     'Hobbies',
#     'Professional Development',
#     'Home Improvement',
#     'Housing Association Fees',
#     'Utilities',
#     'Internet and Phone',
#     'Transportation',
#     'Fuel',
#     'Public Transportation',
#     'Car Payments',
#     'Car Insurance',
#     'Car Maintenance',
#     'Healthcare',
#     'Medical Expenses',
#     'Prescriptions',
#     'Dental Care',
#     'Vision Care',
#     'Life Insurance',
#     'Entertainment',
#     'Movies',
#     'Concerts',
#     'Cable or Streaming Services',
#     'Books and Magazines',
#     'Dining Out',
#     'Restaurants',
#     'Fast Food',
#     'Coffee Shops',
#     'Groceries',
#     'Food',
#     'Household Supplies',
#     'Personal Care Products',
#     'Clothing',
#     'Clothes',
#     'Shoes',
#     'Accessories',
#     'Electronics',
#     'Gadgets',
#     'Computers',
#     'Home Appliances',
#     'Travel',
#     'Flights',
#     'Hotels',
#     'Transportation',
#     'Sightseeing',
#     'Savings',
#     'Emergency Fund',
#     'Retirement Savings',
#     'Investments',
#     'Debt Payments',
#     'Credit Card Payments',
#     'Loan Payments',
#     'Student Loans',
#     'Personal Loans',
#     'Childcare',
#     'Babysitters',
#     'Daycare',
#     'Pet Care',
#     'Pet Food',
#     'Veterinary Expenses',
#     'Home Maintenance',
#     'Repairs',
#     'Renovations',
#     'Lawn Care',
#     'Home Supplies',
#     'Gym or Fitness Memberships',
#     'Subscriptions',
#     'Magazines',
#     'Streaming Services',
#     'Charitable Donations',
#     'Taxes',
#     'Income Tax',
#     'Property Tax',
#     'Financial Services',
#     'Financial Advisor Fees',
#     'Accounting Fees',
#     'Legal Fees',
#     'Personal Care',
#     'Haircuts',
#     'Spa Services',
#     'Skincare',
#     'Gifts',
#     'Birthdays',
#     'Holidays',
#     'Weddings',
#     'Anniversaries',
#     'Hobbies',
#     'Sports',
#     'Crafts',
#     'Gaming',
#     'Professional Development',
#     'Courses',
#     'Workshops',
#     'Conferences',
#     'Home Improvement',
#     'Furniture',
#     'Appliances',
#     'Home Decor',
#     'Housing Association Fees',
#     'Condo or HOA Fees',
#     'Utilities',
#     'Electricity',
#     'Water',
#     'Internet and Phone',
#     'Mobile Plan',
#     'Cable or Internet Bill',
#     'Transportation',
#     'Fuel',
#     'Car Payments',
#     'Car Insurance',
#     'Car Maintenance',
#     'Healthcare',
#     'Health Insurance',
#     'Medical Bills',
#     'Prescriptions',
#     'Dental Care',
#     'Vision Care',
#     'Life Insurance',
#     'Entertainment',
#     'Movies',
#     'Concerts',
#     'Cable or Streaming Services',
#     'Books and Magazines',
#     'Dining Out',
#     'Restaurants',
#     'Fast Food',
#     'Coffee Shops',
#     'Groceries',
#     'Food',
#     'Household Supplies',
#     'Personal Care Products',
#     'Clothing',
#     'Clothes',
#     'Shoes',
#     'Accessories',
#     'Electronics',
#     'Gadgets',
#     'Computers',
#     'Home Appliances',
#     'Travel',
#     'Flights',
#     'Hotels',
#     'Transportation',
#     'Sightseeing',
#     'Savings',
#     'Emergency Fund',
#     'Retirement Savings',
#     'Investments',
#     'Debt Payments',
#     'Credit Card Payments',
#     'Loan Payments',
#     'Student Loans',
#     'Personal Loans',
#     'Childcare',
#     'Babysitters',
#     'Daycare',
#     'Pet Care',
#     'Pet Food',
#     'Veterinary Expenses',
#     'Home Maintenance',
#     'Repairs',
#     'Renovations',
#     'Lawn Care',
#     'Home Supplies',
#     'Gym or Fitness Memberships',
#     'Subscriptions',
#     'Magazines',
#     'Streaming Services',
#     'Charitable Donations',
#     'Taxes',
#     'Income Tax',
#     'Property Tax',
#     'Financial Services',
#     'Financial Advisor Fees',
#     'Accounting Fees',
#     'Legal Fees',
#     'Personal Care',
#     'Haircuts',
#     'Spa Services',
#     'Skincare',
#     'Gifts',
#     'Birthdays',
#     'Holidays',
#     'Weddings',
#     'Anniversaries',
#     'Hobbies',
#     'Sports',
#     'Crafts',
#     'Gaming',
#     'Professional Development',
#     'Courses',
#     'Workshops',
#     'Conferences',
#     'Home Improvement',
#     'Furniture',
#     'Appliances',
#     'Home Decor',
#     'Housing Association Fees',
#     'Condo or HOA Fees',
#     'Financial Services',
#     'Bank Fees',
#     'Investment Fees',
#     'Legal and Accounting Fees',
#     'Personal Care',
#     'Health and Wellness',
#     'Beauty Products',
#     'Gifts',
#     'Gifts for Others',
#     'Personal Gifts',
#     'Hobbies and Entertainment',
#     'Entertainment Expenses',
#     'Hobby Supplies',
#     'Professional Development',
#     'Education Expenses',
#     'Online Courses',
#     'Books and Materials',
#     'Technology',
#     'Gadgets and Electronics',
#     'Software Subscriptions',
#     'Travel and Transportation',
#     'Flights and Hotels',
#     'Car Rentals',
#     'Public Transportation',
#     'Food and Dining',
#     'Restaurants and Cafes',
#     'Groceries and Cooking',
#     'Health and Fitness',
#     'Gym Memberships',
#     'Fitness Classes',
#     'Healthcare',
#     'Doctor Visits',
#     'Prescriptions and Medications',
#     'Insurance',
#     'Health and Medical Insurance',
#     'Car Insurance',
#     'Home Insurance',
#     'Life Insurance',
#     'Other Insurance',
#     'Charitable Giving',
#     'Donations and Philanthropy',
#     'Religious Contributions',
#     'Miscellaneous',
#     'Uncategorized Expenses'
# ]
#
# common_company_industries = [
#     'Aerospace and Defense',
#     'Agriculture',
#     'Architecture',
#     'Automotive',
#     'Banking and Finance',
#     'Biotechnology',
#     'Chemicals',
#     'Communications',
#     'Construction',
#     'Consumer Goods',
#     'Education',
#     'Electronics',
#     'Energy and Utilities',
#     'Entertainment and Media',
#     'Food and Beverage',
#     'Government and Public Administration',
#     'Healthcare and Pharmaceuticals',
#     'Hospitality and Tourism',
#     'Information Technology',
#     'Insurance',
#     'Manufacturing',
#     'Mining and Metals',
#     'Nonprofit and Charity',
#     'Real Estate',
#     'Retail',
#     'Telecommunications',
#     'Transportation and Logistics',
#     'Wholesale Trade'
#     'Aerospace and Defense',
#     'Automotive',
#     'Banking and Finance',
#     'Biotechnology',
#     'Chemicals',
#     'Communications',
#     'Consumer Goods',
#     'Education',
#     'Energy and Utilities',
#     'Entertainment and Media',
#     'Food and Beverage',
#     'Healthcare and Pharmaceuticals',
#     'Information Technology',
#     'Manufacturing',
#     'Retail',
#     'Telecommunications',
#     'Transportation and Logistics',
#     'Real Estate'
# ]
#
# # Search for categories that match the search text
# search_text = 'financial'
# matching_categories = [category for category in expense_categories if search_text.lower() in category.lower()]
#
#
# # print(matching_categories)
#
#
# def search_in_list_of_dicts(data, search_key, search_value):
#     for item in data:
#         if item.get(search_key) == search_value:
#             return item
#     return None  # Return None if no match is found
#
#
# #
# # if __name__ == "__main__":
# #     # relative_path = 'list_categories.json'
# #     #
# #     # file = get_absolute_file_path(file_name=relative_path)
# #     # print(file)
# #
# #     test_list = [
# #         {'Course': "C++", 'Author': "Jerry"},
# #         {'Course': "C++", 'Author': "Jero"},
# #         {'Course': "Python", 'Author': "Mark"},
# #         {'Course': "Java", 'Author': "Paul"}
# #     ]
# #
# #     search_text = 'Jer'
# #
# #     res = [item for item in test_list if any(search_text.lower() in value.lower() for value in item.values())]
# #
# #     print(res)
#
#
# if __name__ == '__main__':
#     cat = []
#     categories = []
#
#     for i in expense_categories:
#         if i not in cat:
#             cat.append(i)
#             categories.append({"Category": i.upper()})
#
#     # output_filename = "categories.json"
#     # with open(output_filename, "w") as json_file:
#     #     json.dump(categories, json_file, indent=4)
#     # print(len(expense_categories), len(cat))
# #     # print(f"Fixtures written to '{output_filename}'")
# #
# if __name__ == "__main__":
#     cat = []
#     industries = []
#     for count, title in enumerate(common_company_industries, start=1):
#         if title not in cat:
#             cat.append(title)
#             # industries.append(
#             #     {
#             #         "model": "requisition.companyindustry",
#             #         "pk": count,
#             #         "fields": {
#             #             "industry": title.upper(),
#             #             "created_at": "2023-08-09T09:39:43.112Z",
#             #             "updated_at": "2023-08-09T09:39:43.112Z"
#             #         }
#             #     }
#             # )
#             industries.append({
#                 "industry": title.upper(),
#             })
#
#     # print(len(cat), len(industries))
#     # output_filename = "company_industry.json"
#     # with open(output_filename, "w") as json_file:
#     #     json.dump(industries, json_file, indent=4)
#     # # print(len(expense_categories), len(cat))
#     # print(f"Fixtures written to '{output_filename}'")
#     local_path = os.path.join(os.getcwd(), 'cdr')
#     print(local_path)