import json
from dataclasses import dataclass
import requests
from django.conf import settings


@dataclass
class LibertyPay:

    def get_agent_balance(self, user_ids):
        """
        This function gets agent balance
        """
        url = "https://backend.libertypayng.com/accounts/check_balance_levels/"

        headers = {
            "Authorization": f"Bearer {settings.AGENCY_SECRETE_KEY}",
            "Content-Type": "application/json",
        }
        payload = json.dumps({"user_ids": user_ids})

        response = requests.post(url, headers=headers, data=payload)

        return response.json()
