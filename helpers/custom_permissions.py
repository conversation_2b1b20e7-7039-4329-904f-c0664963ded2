from django.conf import settings
from rest_framework.permissions import BasePermission

from core.exceptions import InvalidRequestException
from core.models import BlackListEntry, ConstantTable
from helpers.custom_exceptions import (
    AdminSendMoneyException,
    BlackListEntryException,
    BranchRequiredException,
    BranchUUIDValidationException,
    BranchValidationException,
    CompanyOwnerException,
    CompanyRequiredException,
    CompanySaleMembershipException,
    CompanyStockMembershipException,
    CompanyUUIDValidationException,
    CompanyValidationException,
    CreateWalletException,
    EditException,
    SendMoneyBlackListEntryException,
)
from helpers.reusable_functions import UserIdentifier, is_valid_uuid
from helpers.custom_response import Response as CustomResponse
from rest_framework import status
from requisition.helpers.enums import TeamChoices, UserRole
from requisition.models import Budget, Company, Requisition, Team, TeamMember
from stock_inventory.models import Branch

logger = settings.LOGGER


# Create your custom permission(s) here.
class IsCompanyOwner(BasePermission):
    """
    Custom permission class to check if the user owns any active company.
    """

    def has_permission(self, request, view):
        """
        Check if the user has permission to access the view based on their company ownership.
        Args:
            request (HttpRequest): The request object containing user information.
            view (View): The view being accessed.
        Returns:
            bool: True if the user is the owner of an active company, False otherwise.
        Raises:
            CompanyOwnerException: Raised when the user is not the owner of any active company.
        """
        companies = UserIdentifier.get_company_ownership(user=request.user)
        if companies is not None:
            return True
        raise CompanyOwnerException


class CanEditTeam(BasePermission):
    """
    Custom permission class to check if the user is the owner of any active company.
    """

    def has_permission(self, request, view):
        """
        Check if the user has permission to access the view based on their company membership and role.
        Args:
            request (HttpRequest): The request object containing user information.
            view (View): The view being accessed.
        Returns:
            bool: True if the user is a member of the company and has the authorization, False otherwise.
        Raises:
            EditException: Raised when the user does not have the necessary permissions.
        """
        user = request.user

        if request.method == "DELETE":
            data = request.GET
        else:
            data = request.data

        team_id = data.get("team_id")

        if not is_valid_uuid(team_id):
            return CustomResponse(
                errors={"details": "invalid ID type for team ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            team = Team.objects.get(id=team_id)
        except Team.DoesNotExist:
            return CustomResponse(
                errors={"details": "invalid ID team ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )

        member_object = TeamMember.objects.filter(member=user, team_id=team_id).first()

        logger.info("EDIT TEAM PERMISSION")

        logger.info("0000000000000000000000000000")
        logger.info("0000000000000000000000000000")

        logger.info(f"METHOD ==> {request.method}")

        logger.info("0000000000000000000000000000")
        logger.info("0000000000000000000000000000")

        logger.info(f"OBJECT INSTANCE: ->>>>>>>>>>>> {member_object}")
        logger.info(f"TEAM ID: {team_id}")

        roles = [
            "ADMIN",
            "DISBURSER",
            "OWNER",
            "SUB_ADMIN",
        ]
        logger.info(f"{roles}")
        if member_object and (member_object.role in roles):
            logger.info(f"ROLE ==> {member_object.role}")
            return True
        raise EditException


class CanDisburse(BasePermission):
    """
    Custom permission class to check if the current user has the authorization to disburse for the Team.
    """

    def has_permission(self, request, view):
        """
        Check if the user has permission to access the view based on their company membership and role.
        Args:
            request (HttpRequest): The request object containing user information.
            view (View): The view being accessed.
        Returns:
            bool: True if the user has the authorization to disburse for the Team, False otherwise.
        Raises:
            EditException: Raised when the user does not have the necessary permissions.
        """
        user = request.user
        data = request.data
        requisition_id = data.get("requisition_id")
        try:
            req = Requisition.objects.get(id=requisition_id)
        except Requisition.DoesNotExist:
            raise InvalidRequestException({"message": "Requisition ID is invalid or empty"})

        team_member = req.team.members.filter(member=user)
        roles = [
            UserRole.ADMIN,
            UserRole.DISBURSER,
            UserRole.OWNER,
            UserRole.SUB_ADMIN,
        ]
        if team_member.exists():
            member_instance = team_member.first()
            if req.team.company.user == user or member_instance.role in roles:
                return True
        raise EditException


class CanEditBudget(BasePermission):
    """
    Custom permission class to check if the current user has the authorization to disburse for the Team.
    """

    def has_permission(self, request, view):
        """
        Check if the user has permission to access the view based on their company membership and role.
        Args:
            request (HttpRequest): The request object containing user information.
            view (View): The view being accessed.
        Returns:
            bool: True if the user has the authorization to disburse for the Team, False otherwise.
        Raises:
            EditException: Raised when the user does not have the necessary permissions.
        """

        user = request.user
        data = request.data

        budget_id = data.get("budget")
        # print(budget_id, "budget \n\n")
        budget = Budget.objects.filter(id=budget_id)
        # print(budget, "\n\n")
        roles = [
            UserRole.ADMIN,
            UserRole.DISBURSER,
            UserRole.OWNER,
            UserRole.SUB_ADMIN,
        ]
        if budget:
            budget_instance = budget.last()
            # print(budget_instance, "budget instance \n\n")
            team_instance = budget_instance.team
            # print(team_instance, "team instance \n\n")
            if team_instance:
                team_members = team_instance.members
                member_instance = team_members.filter(member=user).last()
                # print(member_instance.role, "role \n\n")
                if member_instance.role in roles:
                    return True
                else:
                    raise EditException
            elif not team_instance and user == budget_instance.user:
                return True
            else:
                print("TEAM INSTANCE IS NONE")
                raise EditException
        return True


class IsStockActiveMember(BasePermission):
    """
    Custom permission to check if the authenticated user is an active STOCK
    member of the specified company.
    """

    def has_permission(self, request, view):
        """
        This method checks if the user is authenticated.
        If the company exists, it checks the user's stock membership in that company.
        If the user is an admin, they have permission. If not an admin, the function
        permits safe methods (GET, HEAD, OPTIONS) and denies other methods.
        Args:
            request (Request): The HTTP request object.
            view (View): The view associated with the request.
        Returns:
            bool: True if the user has permission; False otherwise.
        """
        request_body = request.data
        user = request.user
        company_id = request.query_params.get("company_id")
        if company_id is None:
            company_id = request.query_params.get("company")
        if company_id is None:
            company_id = request_body.get("company")
        if UserIdentifier.get_company_ownership(user=user) is None:
            membership = UserIdentifier.get_user_membership(
                user=user,
                company=company_id,
                team_type=TeamChoices.STOCK,
            )
            if membership is not None:
                return True
            raise CompanyStockMembershipException
        return True


class IsSalesActiveMember(BasePermission):
    """
    Custom permission to check if the authenticated user is an active SALES
    member of the specified company.
    """

    def has_permission(self, request, view):
        """
        This method checks if the user is authenticated.
        If the company exists, it checks the user's sales membership in that company.
        If the user is an admin, they have permission. If not an admin, the function
        permits safe methods (GET, HEAD, OPTIONS) and denies other methods.
        Args:
            request (Request): The HTTP request object.
            view (View): The view associated with the request.
        Returns:
            bool: True if the user has permission; False otherwise.
        """
        request_body = request.data
        user = request.user
        company_id = request.query_params.get("company_id")
        if company_id is None:
            company_id = request.query_params.get("company")
        if company_id is None:
            company_id = request_body.get("company")
        if UserIdentifier.get_company_ownership(user=user) is None:
            membership = UserIdentifier.get_user_membership(
                user=user,
                company=company_id,
                team_type=TeamChoices.SALES,
            )
            if membership is not None:
                return True
            raise CompanySaleMembershipException
        return True


class CompanyRequiredPermission(BasePermission):

    def has_permission(self, request, view):
        request_body = request.data
        company_id = request.query_params.get("company_id")
        if company_id is None:
            company_id = request.query_params.get("company")
        if company_id is None:
            company_id = request_body.get("company")
        if company_id is None:
            raise CompanyRequiredException
        if not is_valid_uuid(company_id):
            raise CompanyUUIDValidationException
        company = Company.retrieve_company(id=company_id)
        if company is None:
            raise CompanyValidationException
        return True


class BranchRequiredPermission(BasePermission):

    def has_permission(self, request, view):
        request_body = request.data
        branch_id = request.query_params.get("branch_id")
        if branch_id is None:
            branch_id = request.query_params.get("branch")
        if branch_id is None:
            branch_id = request_body.get("branch")
        if branch_id is None:
            raise BranchRequiredException
        if not is_valid_uuid(branch_id):
            raise BranchUUIDValidationException
        branch = Branch.objects.filter(id=branch_id).first()
        if branch is None:
            raise BranchValidationException
        return True


class CanInitiateTransfer(BasePermission):

    def has_permission(self, request, view):
        user_instance = request.user
        email = user_instance.email
        const = ConstantTable.get_constant_instance()

        if not const.send_money:
            raise AdminSendMoneyException

        if BlackListEntry.is_blacklisted(email=email):
            raise BlackListEntryException

        elif BlackListEntry.can_send_money(email=email):
            raise SendMoneyBlackListEntryException
        
        elif BlackListEntry.failled_wallet_update(email=email):
            raise CreateWalletException

        else:
            return True


class CompanyOwnerPermission(BasePermission):
    """
    Custom permission class to check if the user owns any active company.
    """

    def has_permission(self, request, view):
        """
        Check if the user has permission to access the view based on their company ownership.
        Args:
            request (HttpRequest): The request object containing user information.
            view (View): The view being accessed.
        Returns:
            bool: True if the user is the owner of an active company, False otherwise.
        Raises:
            CompanyOwnerException: Raised when the user is not the owner of any active company.
        """
        request_body = request.data
        company_id = request.query_params.get("company_id")
        if company_id is None:
            company_id = request.query_params.get("company")
        if company_id is None:
            company_id = request_body.get("company")
        company = Company.retrieve_company(id=company_id, user=request.user)
        if company is None:
            raise CompanyOwnerException
        return True
