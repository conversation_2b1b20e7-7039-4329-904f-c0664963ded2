from rest_framework.exceptions import APIException
from rest_framework.views import exception_handler

from helpers.custom_response import Response


# Create your custom exception(s) here.
def custom_exception_handler(exception, context):
    """
    Custom Exception Handler for Django REST framework.
    NOTE:
    This function is designed to be used as a custom exception handler in Django REST framework.
    It takes two parameters, `exception` (the raised exception) and `context` (context of the exception).
    The function calls the default exception handler (`exception_handler`) to get the response.
    If the response is not None, it extracts the status code from the response.
    Then, it creates a new `Response` object with the errors data from the original response,
    along with the extracted status code and status, and returns it.
    Args:
        exception (Exception): The raised exception object.
        context (dict): The context of the exception.
    Returns:
        Response: A new Response object with error data and status code from the original response.
    """
    response = exception_handler(exception, context)
    if response is not None:
        status_code = response.status_code
    return Response(
        errors=response.data,
        status_code=status_code,
        status=response.status_code,
    )


class CompanyOwnerException(APIException):
    """
    This exception is raised when a user attempts to perform an action that requires ownership
    of a company, but the user does not have a registered company.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (403 - Forbidden).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 403
    default_detail = "company owner permission is required."
    default_code = "forbidden"


class EditException(APIException):
    """
    This exception is raised when a user attempts to perform an action that requires admin level
    permission, but the user is not authorized.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (403 - Forbidden).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 403
    default_detail = "you do not have the necessary permissions to perform this action."
    default_code = "forbidden"


class CompanyStockMembershipException(APIException):
    """
    This exception is raised when a user attempts to perform an action that requires STOCK
    membership of a company, but the user is not a STOCK member of the registered company.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (403 - Forbidden).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 403
    default_detail = "user is not a member of STOCK in the selected company."
    default_code = "forbidden"


class CompanyValidationException(APIException):
    """
    This exception is raised when an invalid company ID is provided.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (400 - Bad Request).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 400
    default_detail = "provide a valid company ID."
    default_code = "bad request"


class CompanyRequiredException(APIException):
    """
    This exception is raised when an action requires a valid company ID.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (400 - Bad Request).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 400
    default_detail = "provide a valid company ID."
    default_code = "bad request"


class CompanySaleMembershipException(APIException):
    """
    This exception is raised when a user attempts to perform an action that requires SALES
    membership of a company, but the user is not a SALES member of the registered company.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (403 - Forbidden).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 403
    default_detail = "user is not a member of SALES in the selected company."
    default_code = "forbidden"


class CompanyUUIDValidationException(APIException):
    """
    This exception is raised when an invalid UUID is provided for a company.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (400 - Bad Request).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 400
    default_detail = "provide a valid company ID."
    default_code = "bad request"


class BranchValidationException(APIException):
    """
    This exception is raised when an invalid branch ID is provided.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (400 - Bad Request).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 400
    default_detail = "provide a valid branch ID."
    default_code = "bad request"


class BranchRequiredException(APIException):
    """
    This exception is raised when an action requires a valid branch ID.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (400 - Bad Request).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 400
    default_detail = "provide a valid branch ID."
    default_code = "bad request"


class BranchUUIDValidationException(APIException):
    """
    This exception is raised when an invalid UUID is provided for a branch.
    Attributes:
        status_code (int): The HTTP status code associated with the exception (400 - Bad Request).
        default_detail (str): The default error detail message.
        default_code (str): The default error code.
    """

    status_code = 400
    default_detail = "provide a valid branch ID."
    default_code = "bad request"


class BlackListEntryException(APIException):
    status_code = 403
    default_detail = {
        "error": "error",
        "message": "User is blacklisted and cannot perform this action. "
        "Please contact support for further assistance.",
    }
    default_code = "Not permitted"


class SendMoneyBlackListEntryException(APIException):
    status_code = 403
    default_detail = {
        "error": "error",
        "message": "We're sorry, but your account is currently restricted from sending money."
        " This restriction may be due to security concerns or account activity.",
    }
    default_code = "Not permitted"


class CreateWalletException(APIException):
    status_code = 403
    default_detail = {
        "error": "error",
        "message": "We're sorry, but your Wallet account creation might be in-complete. Please contact support",
    }
    default_code = "Not permitted"


class AdminSendMoneyException(APIException):
    status_code = 403
    default_detail = {
        "error": "error",
        "message": "Service is currently not available. Please try again later.",
    }
    default_code = "Not permitted"
