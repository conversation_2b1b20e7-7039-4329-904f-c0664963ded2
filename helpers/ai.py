import base64
import io
import json

from django.conf import settings
from django.db import models
from PIL import Image
import requests

# from requisition.helpers.receipt_extract import get_completion
from stock_inventory.models import PriceList, Product


# Paybox360 AI Manager.
def generate_ai_response(prompt, encoded_image):
    GOOGLE_API_KEY = settings.GOOGLE_API_KEY

    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-001:generateContent?key={GOOGLE_API_KEY}"
    headers = {"Content-Type": "application/json"}

    request_payload = {
        "contents": [
            {
                "parts": [
                    {"text": prompt},
                    {"inline_data": {"mime_type": "image/jpeg", "data": encoded_image}},
                ]
            }
        ]
    }

    response = requests.post(url, json=request_payload, headers=headers)
    if response.status_code == 200:
        response_dict = response.json()
        ai_response = response_dict["candidates"][0]["content"]["parts"][0]["text"]
        return ai_response
    else:
        print(f"Error: {response.status_code}, {response.text}")
        return None


def get_product_names(company_id, branch_id):
    products = (
        Product.objects.filter(company_id=company_id)
        .filter(models.Q(all_branches=True) | models.Q(selected_branches__id=branch_id))
        .values_list("name", flat=True)
    )
    return list(products)


def get_item_id_and_price(product_name, company_id, branch_id):
    product = Product.objects.filter(name=product_name.title()).first()
    if product is None:
        return None
    stock_detail = (
        PriceList.objects.filter(company=company_id, item=product)
        .filter(models.Q(all_branches=True) | models.Q(selected_branches__id=branch_id))
        .first()
    )
    if stock_detail is None:
        return None
    return {
        "item_id": product.id,
        "category_id": product.category.id,
        "price": stock_detail.price,
    }


def image_encoder(image, is_base64=False):
    if is_base64:
        if "," in image:
            image = image.split(",")[1]
        image = Image.open(io.BytesIO(base64.b64decode(image)))
    else:
        image = Image.open(image)
    img = image.resize((512, int(image.height * 512 / image.width)))
    img_io = io.BytesIO()
    img.save(img_io, format="JPEG")
    img_io.seek(0)
    return base64.b64encode(img_io.read()).decode("utf-8")


def generate_sales_items(image, is_base64=False, company_id=None, branch_id=None):
    encoded_image = image_encoder(image, is_base64)
    inventory = get_product_names(company_id, branch_id)
    prompt = (
        """
    I have an image of a handwritten sales order, likely from a restaurant setting. Due to handwriting differences, you may not be accurate so I provided you with a reference list of items to improve accuracy.\n
    Your entire response/output is going to consist of a single JSON object {}, and you will MUST NOT wrap it within JSON md markers.\n
    Please parse the information from the image and create a digital sales order.\n
    Your response must be only a valid JSON with these keys: 'order_items' and 'order_total' (which will be a sum of all the order items prices) using the following structure:\n
        {
        "order_items": [
            {
            "Name": "[Name (from image)]",
            "Quantity": [Quantity (from image)],
            "Price": [Price (from image)]
            },
            {
            "Name": "[Name (from image)]",
            "Quantity": [Quantity (from image)],
            "Price": [Price (from image)]
            }
            # ... add more items as needed
        ],
        "order_total": [Total amount from image]
        }\n
    All fields are required.\n
    **This is a reference list to help improve the accuracy of parsing items in the handwritten sales order. If there are any conflicts between the spelling in the image and the items listed here, prioritize the spellings in this list.**
    """
        + ", ".join(inventory)
        + """
    """
    )
    """
    NOTE: Returns only item(s) on PriceList model.
    """
    ai_response = generate_ai_response(prompt, encoded_image)
    if ai_response:
        response_data = json.loads(ai_response)
        if "error" in response_data:
            return response_data
        valid_data = []
        for item in response_data.get("order_items"):
            item_details = get_item_id_and_price(item["Name"], company_id, branch_id)
            if item_details is not None:
                item["Price"] = float(item_details.get("price"))
                item["item_id"] = str(item_details.get("item_id"))
                item["category_id"] = str(item_details.get("category_id"))
                valid_data.append(item)
        if len(valid_data) >= 1:
            return valid_data
        return None


def process_stock_items(image, is_base64=False, company_id=None, branch_id=None):
    encoded_image = image_encoder(image, is_base64)
    inventory = get_product_names(company_id, branch_id)
    prompt = (
        """
    I have an image of a handwritten stock items. Due to handwriting differences, you may not be accurate so I provided you with a reference list of items to improve accuracy.\n
    Your entire response/output is going to consist of a single JSON object {}, and you will MUST NOT wrap it within JSON md markers.\n
    Please parse the information from the image and create a digital inventory list.\n
    Your response must be only a valid JSON with these keys: 'stock_items' using the following structure:\n
        {
        "stock_items": [
            {
            "Category": "[Category (from image)]",
            "Product": "[Product (from image)]",
            "Quantity": [Quantity (from image)],
            "Cost Price": [Cost Price (from image)],
            "Selling Price": [Selling Price (from image)]
            },
            "Category": "[Category (from image)]",
            "Product": "[Product (from image)]",
            "Quantity": [Quantity (from image)],
            "Cost Price": [Cost Price (from image)],
            "Selling Price": [Selling Price (from image)]
            # ... add more items as needed
        ]
        }\n
    All fields are required.\n
    **This is a reference list to help improve the accuracy of parsing items in the handwritten stock items. If there are any conflicts between the spelling in the image and the items listed here, prioritize the spellings in this list.**
    """
        + ", ".join(inventory)
        + """
    """
    )
    ai_response = generate_ai_response(prompt, encoded_image)
    if ai_response:
        response_data = json.loads(ai_response)
        return response_data
    else:
        return None
    


def process_price_list(image, is_base64=False, company_id=None, branch_id=None):
    encoded_image = image_encoder(image, is_base64)
    prompt = (
        """
        I have an image containing handwritten stock items and their price list. Please parse the information from the image and accurately convert it into a digital text format.\n
        
        Your response/output should be a single JSON object {} WITHOUT ANY additional markdown or code formatting. The JSON will have this key: 'price_list' using the following structure:\n
        {
            "price_list": [
                {
                "Category": "[Category (from image)]",
                "Product": "[Product (from image)]",
                "Price": [Price (from image)],
                },
                {
                "Category": "[Category (from image)]",
                "Product": "[Product (from image)]",
                "Price": [Price (from image)],
                }
            ]
        }\n
        You will perform this task in two steps:\n
        
        1. First, convert the handwritten items into a digital version.\n
        
        2. Then, review the items to correct any misspelled words. If any words are unclear, apply contextual reasoning to deduce the most likely correct spelling before finalizing the response.\n

        Return the final corrected JSON object to me.\n

        IF THE IMAGE PRESENTED DOES NOT IN ANY WAY LOOK LIKE A PRICE LIST, JUST RETURN AN EMPTY JSON. \n
    """
    )
    ai_response = generate_ai_response(prompt, encoded_image)
    if ai_response:
        response_data = json.loads(ai_response)
        return response_data
    else:
        return None
