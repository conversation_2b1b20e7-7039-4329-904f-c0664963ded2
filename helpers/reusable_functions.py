import base64
from datetime import datetime
import json
import math
import random
import re
import secrets
import string
import uuid

from django.conf import settings
from django.core.paginator import Paginator as django_core_paginator
import pytz
import requests
from rest_framework import serializers

from requisition.helpers.enums import UserRole, UserStatus
from requisition.models import Company, TeamMember


User = settings.AUTH_USER_MODEL
logger = settings.LOGGER


# Helper function(s) and object(s).
def strip_all_whitespaces(string: str):
    split_string = string.split(" ")
    return " ".join(split_string)


def is_valid_uuid(string: str):
    """
    Check if the given string is a valid UUID (Universally Unique Identifier).
    Args:
        string (str): The string to be checked for UUID validity.
    Returns:
        bool: True if the string is a valid UUID, False otherwise.
    """
    try:
        uuid_str = str(string)
        uuid_object = uuid.UUID(uuid_str)
        return str(uuid_object) == string
    except ValueError:
        return False


def generate_random_acct_digit():
    """Generate a random number between ********** and **********"""
    acct_num = random.randint(**********, **********)
    return f"{acct_num}"


def get_account_details(account_data: list):
    account = None
    for acct in account_data:
        if acct.get("account_type") == "COLLECTION":
            account = acct
            break
    return account


def get_wema_account_details(account_data: list):
    account = None
    for data in account_data:
        if (
            data.get("account_type") == "COLLECTION"
            and data.get("bank_name") == "Wema Bank PLC"
        ):
            account = data
            break
    return account


def round_float_amount(amount):
    return math.floor(amount * 10**2) / 10**2


def match_acct_number(acct_number):
    pattern = r"^\d{10}$"
    return bool(re.match(pattern, acct_number))


def format_currency(amount):
    """
    Format a numeric amount as a currency string.
    Args:
        amount (int or float or None): The numeric value to be formatted as currency.
            If None, the returned currency string will be 'NGN0'.
    Returns:
        str: The formatted currency string in the format 'NGN{amount}'.
            If the input amount is None, the string will be 'NGN0'.
    """
    if amount is None:
        currency = "NGN0"
    else:
        currency = "NGN{:,}".format(amount)
    return currency


def generate_unique_reference():
    """
    Generate a unique reference ID in the format 'yyyymmdd-8character_random_string'.
    Returns:
        str: A unique reference ID consisting of the current date and an 8-character random string.
    """
    current_date = datetime.now().strftime("%Y%m%d")
    random_string = "".join(
        secrets.choice(string.ascii_letters + string.digits) for _ in range(8)
    )
    unique_id = f"{current_date}-{random_string}"
    return unique_id


def convert_string_to_base32(text: str):
    """
    Convert a given string to a base32-encoded string.
    Args:
        text (str): The input string that needs to be encoded.
    Returns:
        str: The base32-encoded string representing the input text.
    """
    encoded_bytes = base64.b32encode(text.encode())
    encoded_string = encoded_bytes.decode()
    return encoded_string


def is_valid_string(string: str):
    if (
        string != ""
        and string != None
        and string != " "
        and string != "null"
        and string != "undefined"
    ):
        return True
    return False


def validate_phone_number(phone: str):
    phone_pattern = re.compile(r"^\+?1?\d{9,15}$")
    if not phone_pattern.match(phone):
        raise serializers.ValidationError({"message": "enter a valid phone number."})


class Paginator:
    """
    This class provides a method to paginate a queryset based on the request parameters.
    """

    @staticmethod
    def paginate(request, queryset):
        """
        This method takes the request object and the queryset to be paginated as input.
        It reads the pagination parameters from the request and paginates the queryset accordingly.
        NOTE:
        - the 'size' parameter defines the number of items per page.
        - the 'page' parameter determines the requested page number.
        Args:
            request (HttpRequest): The HTTP request object containing pagination parameters.
            queryset (QuerySet): The Django queryset to be paginated.
        Returns:
            Page: A Django Page object containing the paginated results.
        """
        query_parameters = request.GET
        page = query_parameters.get("page")
        size = query_parameters.get("size")
        page = page if is_valid_string(page) else 1
        size = size if is_valid_string(size) else 200
        if int(page) < 1 or int(size) < 1:
            raise serializers.ValidationError(
                {"message": "page size can not be less than 1."}
            )
        paginator = django_core_paginator(queryset, int(size))
        requested_page = int(page)
        verified_page = (
            requested_page
            if requested_page < paginator.num_pages
            else paginator.num_pages
        )
        paginated_page = paginator.page(verified_page)
        return paginated_page


class UserIdentifier:
    """
    Utility class to identify various aspects of a user's relationship with companies, branches, and teams.
    """

    ADMIN_ROLE = [
        UserRole.ADMIN,
        UserRole.OWNER,
        UserRole.SUB_ADMIN,
    ]
    BRANCH_ADMIN_ROLE = [
        UserRole.MANAGER,
        UserRole.SUPERVISOR,
    ]

    @classmethod
    def get_company_ownership(cls, user: User):
        """
        Get the companies owned by the given user.
        Args:
            user (User): The user for whom to retrieve owned companies.
        Returns:
            QuerySet[Company] or None: A queryset of owned companies if they exist, otherwise None.
        """
        companies = Company.objects.filter(user=user, is_active=True)
        if companies.exists():
            return companies
        return None

    @classmethod
    def get_user_membership(cls, user: User, company: Company, team_type: str):
        """
        This method first checks if the user has an admin role (which indicates a full access) in any team
        of the specified company. If they do, it returns information indicating that they are an admin with
        access to all branches. If not an admin, a check is done for branch specific admin roles, and lastly
        it checks for the first instance as a regular member and returns information about their membership,
        including the associated branch, if any.
        Args:
            user (User): The user for whom to retrieve membership information.
            company (Company): The company associated with the User.
            team_type ('SALES', 'STOCK'): The team type associated with the user.
        Returns:
            dict or None: A dictionary containing user's membership information, or None if no membership is found.
        """
        team_members = TeamMember.objects.filter(
            team__company=company,
            status=UserStatus.ACTIVE,
            is_active=True,
        )
        admin_membership = team_members.filter(
            member=user,
            role__in=cls.ADMIN_ROLE,
        )
        if admin_membership.exists():
            return {
                "message": "successfully fetched USER role.",
                "user": user.email,
                "is_admin": True,
                "is_branch_admin": False,
                "can_view_all_branches": True,
            }
        branch_admin_membership = team_members.filter(
            team__team_type=team_type, member=user, role__in=cls.BRANCH_ADMIN_ROLE
        ).first()
        if branch_admin_membership is not None:
            return {
                "message": "successfully fetched USER role.",
                "user": user.email,
                "is_admin": False,
                "is_branch_admin": True,
                "can_view_all_branches": False,
                "branch_id": branch_admin_membership.team.branch.id,
                "branch_name": branch_admin_membership.team.branch.name,
            }
        membership = team_members.filter(
            team__team_type=team_type, member=user, role=UserRole.MEMBER
        ).first()
        if membership is not None:
            return {
                "message": "successfully fetched USER role.",
                "user": user.email,
                "is_admin": False,
                "is_branch_admin": False,
                "can_view_all_branches": False,
                "branch_id": membership.team.branch.id,
                "branch_name": membership.team.branch.name,
            }
        return None


def split_string_by_colon(input_string):
    """
    Splits a string by the colon (":") character and returns the text after the colon.

    Args:
        input_string (str): The input string to be split.

    Returns:
        str: The text after the colon in the input string. If no colon is found, the original input string is returned.
    """
    # Split the input string using the ":" character as the separator
    parts = input_string.split(":")

    if len(parts) > 1:
        # If a colon is found, take the text after it and remove leading/trailing spaces
        split_string = parts[1].strip()
        return split_string
    else:
        # If no colon is found, return the original input string
        return input_string


def calculate_name_similarity(person_first_and_last_name, request_first_and_last_name):
    """
    returns a percentage of the name e.g person_first_and_last_name == john doe
    request_first_and_last_name == john do this returns 90% since e was omitted
    """

    # Convert names to lowercase for case-insensitive comparison
    person_first_and_last_name = person_first_and_last_name.lower()
    request_first_and_last_name = request_first_and_last_name.lower()
    # Initialize a 2D matrix to store the Levenshtein distances
    matrix = [
        [0] * (len(request_first_and_last_name) + 1)
        for _ in range(len(person_first_and_last_name) + 1)
    ]
    print(matrix)
    # Initialize the first row and column of the matrix
    for i in range(len(person_first_and_last_name) + 1):
        matrix[i][0] = i
    for j in range(len(request_first_and_last_name) + 1):
        matrix[0][j] = j
    # Fill in the matrix using the Levenshtein distance algorithm
    for i in range(1, len(person_first_and_last_name) + 1):
        for j in range(1, len(request_first_and_last_name) + 1):
            cost = (
                0
                if person_first_and_last_name[i - 1]
                == request_first_and_last_name[j - 1]
                else 1
            )
            matrix[i][j] = min(
                matrix[i - 1][j] + 1,  # Deletion
                matrix[i][j - 1] + 1,  # Insertion
                matrix[i - 1][j - 1] + cost,  # Substitution
            )
    # Calculate the similarity percentage
    max_length = max(len(person_first_and_last_name), len(request_first_and_last_name))
    similarity = (1 - matrix[-1][-1] / max_length) * 100
    return similarity


def format_string_to_date_object(date_string: str):
    date_format = "%Y-%m-%d"
    try:
        formatted_date = datetime.strptime(date_string, date_format)
    except ValueError as error:
        formatted_date = None
    return formatted_date


def convert_date_format(date_str, with_spaces=False):
    """
    Converts a date string from either 'YYYY-MM-DD' or 'DD-MM-YYYY' format to 'DD-MMM-YYYY' format.

    Parameters:
    -----------
    date_str : str
        The date string to be converted.

    Returns:
    --------
    str
        The date string in 'DD-MMM-YYYY' format.
    """
    date_str = str(date_str)
    try:
        # Try parsing the date in 'YYYY-MM-DD' format
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        try:
            # Try parsing the date in 'DD-MM-YYYY' format
            date_obj = datetime.strptime(date_str, "%d-%m-%Y")
        except ValueError:
            raise ValueError(
                "Date format should be either 'YYYY-MM-DD' or 'DD-MM-YYYY'"
            )

    # Return the date in 'DD-MMM-YYYY' format
    if not with_spaces:
        return date_obj.strftime("%d-%b-%Y")
    else:
        return date_obj.strftime("%d %b %Y")


def generate_transaction_pin():
    return random.randint(1000, 9999)


def generate_password():
    return str(random.randint(100000, 999999))


def send_sms(receiver, message, template_id, api_key):
    url = "https://whispersms.xyz/transactional/send"
    # Prepare the payload with dynamic message and placeholders
    payload = json.dumps(
        {
            "receiver": receiver,
            "template": template_id,
            "place_holders": {"message": message},
        }
    )

    # Set the request headers
    headers = {
        "Authorization": f"Api_key {api_key}",
        "Content-Type": "application/json",
    }

    try:
        print("1---", url, headers, payload)

        # Make the request to WhisperSMS API
        response = requests.post(url, headers=headers, data=payload)
        response.raise_for_status()  # Raise an exception for HTTP errors
        return response.json()  # Return the JSON response if successful
    except requests.exceptions.RequestException as e:
        print(f"Failed to send SMS: {e}")
        return None


def format_date_and_time(date_and_time: str):
    accepted_format = "%Y-%m-%d %H:%M:%S"
    try:
        time_zone = pytz.timezone(settings.TIME_ZONE)
        date_time_object = datetime.strptime(date_and_time, accepted_format)
        time_zone_object = time_zone.localize(date_time_object)
    except ValueError:
        raise serializers.ValidationError(
            {"message": "the accepted datetime format is 'YYYY-MM-DD HH:MM:SS'."}
        )
    return time_zone_object
