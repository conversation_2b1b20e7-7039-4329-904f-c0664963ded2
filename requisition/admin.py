from django.contrib import admin
from django.db.models.query import QuerySet
from import_export.admin import ImportExportModelAdmin

from account.models import Wallet
from requisition.models import PurchaseIndent, ProcurementPurchaseOrder, Asset
from requisition.resources import *
from django.contrib import admin
from .models import (
    ProcurementPurchaseInvoice, ProcurementPurchaseOrder, ProcurementEscrow, IndentProduct,
    PurchaseIndent, ProcurementPurchaseInvoiceUploadedFIles, Notes, Flags,
)


class TeamResourceAdmin(ImportExportModelAdmin):
    resource_class = TeamResource
    search_fields = [
        "team_name",
        "user__email",
        "user__phone_no",
        "company__company_name",
    ]
    list_filter = ["team_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MonoTopUpRecordAdmin(ImportExportModelAdmin):
    resource_class = MonoTopUpRecordResource
    search_fields = ["user"]
    list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserLinkedBanksAdmin(ImportExportModelAdmin):
    resource_class = UserLinkedBanksResource
    search_fields = ["user"]
    list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyResource
    search_fields = ["user__email", "user__phone_no", "company_name"]
    list_filter = [
        "company_wallet_type",
        "corporate_account_created",
        "instant_wage",
        "test_account",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def admin_create_coporate_wallet(self, request, queryset: QuerySet[Company]):

        message = "Invalid selection"
        for instance in queryset:
            message = Wallet.vfd_create_update_company_coporate_wallet(
                company_id=instance.id, branch_id=None, account_type=None
            )
        self.message_user(request, str(message))

    admin_create_coporate_wallet.short_description = "VFD: create update account/wallet"
    admin_create_coporate_wallet.allow_tags = True

    actions = [
        admin_create_coporate_wallet,
    ]


class RequisitionResourceAdmin(ImportExportModelAdmin):
    resource_class = RequisitionResource
    search_fields = [
        "user__email",
        "user__phone_no",
        "approved_by__email",
        "company__company_name",
    ]
    list_filter = [
        "priority",
        "status",
        "is_disbursed",
        "is_paid_budget",
        "is_inprogress",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TeamMemberResourceAdmin(ImportExportModelAdmin):
    resource_class = TeamMemberResource
    search_fields = ["email", "phone_no"]
    list_filter = ["role", "status", "is_registered"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CategoryResourceAdmin(ImportExportModelAdmin):
    resource_class = CategoryResource
    search_fields = [""]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommentResourceAdmin(ImportExportModelAdmin):
    resource_class = CommentResource
    search_fields = [""]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ExpenseResourceAdmin(ImportExportModelAdmin):
    resource_class = ExpenseResource
    search_fields = [
        "user__email",
        "user__phone_no",
        "company__company_name",
        "team__team_name",
    ]
    list_filter = (
        "disbursement_wallet",
        "status",
    )

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("payload")
        data.remove("receipt")
        data.remove("location")
        data.remove("item_description")
        return data


class BudgetResourceAdmin(ImportExportModelAdmin):
    resource_class = BudgetResource
    search_fields = ["user__email", "user__phone_no", "company__company_name"]
    list_filter = ["budget_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CompanyVerificationInfoResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyVerificationInfoResource
    search_fields = ["registration_no"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields] + [
            "response_data"
        ]
        data.remove("verification_response")
        return data


class CompanyVerificationMetaDataResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyVerificationMetaDataResource
    search_fields = ["registration_no"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields] + [
            "response_data"
        ]
        data.remove("verification_response")
        return data


class CompanyIndustryResourceAdmin(ImportExportModelAdmin):
    resource_class = CompanyIndustryResource
    search_fields = [""]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BudgetAllocationResourceAdmin(ImportExportModelAdmin):
    resource_class = BudgetAllocationResource
    search_fields = ["user__email", "budget__budget_name", "team__team_name"]
    list_filter = ("is_active",)

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("is_deleted")
        data.remove("start_date")
        data.remove("end_date")
        return data


class TeamMemberInviteResourceAdmin(ImportExportModelAdmin):
    resource_class = TeamMemberInviteResource
    search_fields = ["email", "company__company_name"]
    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class JotFormDataSyncResourceAdmin(ImportExportModelAdmin):
    resource_class = JotFormDataSyncResource
    search_fields = ["request_id", "full_name", "email", "bvn", "business_name", "registration_number", "directors_first_name", "directors_last_name", "sales_rep_name", "sales_rep_email"]
    list_filter = ["created_at", "purpose", "processing_request_status", "company_details_created", "has_set_passcode_on_agency_banking"]

    date_hierarchy = ("created_at")

    def get_list_display(self, request):
        data =  [field.name for field in self.model._meta.concrete_fields]
        data.remove("data")
        data.remove("serialised_data")
        return data


admin.site.register(Team, TeamResourceAdmin)
admin.site.register(Company, CompanyResourceAdmin)
admin.site.register(Requisition, RequisitionResourceAdmin)
admin.site.register(TeamMember, TeamMemberResourceAdmin)
admin.site.register(Category, CategoryResourceAdmin)
admin.site.register(Comment, CommentResourceAdmin)
admin.site.register(Expense, ExpenseResourceAdmin)
admin.site.register(Budget, BudgetResourceAdmin)
admin.site.register(CompanyVerificationInfo, CompanyVerificationInfoResourceAdmin)
admin.site.register(
    CompanyVerificationMetaData, CompanyVerificationMetaDataResourceAdmin
)
admin.site.register(CompanyIndustry, CompanyIndustryResourceAdmin)
admin.site.register(BudgetAllocation, BudgetAllocationResourceAdmin)
admin.site.register(UserLinkedBanks, UserLinkedBanksAdmin)
admin.site.register(MonoTopUpRecord, MonoTopUpRecordAdmin)
admin.site.register(TeamMemberInvite, TeamMemberInviteResourceAdmin)

admin.site.register(ProcurementPurchaseOrder)
admin.site.register(ProcurementEscrow)
admin.site.register(IndentProduct)
admin.site.register(PurchaseIndent)
admin.site.register(ProcurementPurchaseInvoiceUploadedFIles)
admin.site.register(Notes)
admin.site.register(Flags)
admin.site.register(Asset)


class ProcurementPurchaseInvoiceAdmin(admin.ModelAdmin):
    list_display = (
        "po_invoice_number", 
        "document_date", 
        "status", 
        "vendor", 
        "procured_for", 
        "pi_requester", 
        "pi_approver",
        "purchase_order",
        "no_of_items", 
        "no_shipped", 
        "subtotal", 
        "shipping_fee",
        "allocation_balance",
        "vat", 
        "calculate_total",
        "total",
        "paid",
    )
 
    list_filter = ("status", "procurement_type", "accepted", "paid", "document_date")
    search_fields = ("po_invoice_number", "vendor__name", "pi_requester__username", "pi_approver__username")
    
    readonly_fields = ("subtotal", "calculate_total")

    fieldsets = (
        ("Invoice Details", {
            "fields": (
                "po_invoice_number", "document_date", "Due_date", "status", 
                "purchase_order", "pi_requester", "pi_approver", "procured_for", 
                "vendor", "procurement_type",
            ),
        }),
        ("Item Information", {
            "fields": (
                "no_of_items", "no_shipped", "shipping_fee", "vat", 
                "hard_copy_upload", "accepted", "paid", "allocation_balance","total"
            ),
        }),
        ("Calculated Fields", {
            "fields": ("subtotal", "calculate_total"),
        }),
    )

    
    def save_model(self, request, obj, form, change):
        obj.calculate_total()
        super().save_model(request, obj, form, change)


admin.site.register(ProcurementPurchaseInvoice, ProcurementPurchaseInvoiceAdmin)
admin.site.register(JotFormDataSync, JotFormDataSyncResourceAdmin)




class AdGeneneratedUsersResourceAdmin(ImportExportModelAdmin):
    resource_class = AdGeneneratedUsersResource
    search_fields = ["phone_number", "first_name" "last_name", "email", "paybox_agent_phone_number", "paybox_agent_name"]
    list_filter = ["created_at", "has_a_company", "last_activity", "channel", "jot_form_link_sent", "jot_form_data_submitted", "completed_sms_sequence", "paybox360_account_created", "company_details_created", "libertypay_account_created", "added_to_brevo_contact_list", "whatsapp_message_sent"]

    date_hierarchy = ("created_at")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

        
class AdGeneneratedUserActivityResourceAdmin(ImportExportModelAdmin):
    resource_class = AdGeneneratedUserActivityResource
    search_fields = ["phone_number", "first_name" "last_name", "email", "paybox_agent_phone_number", "paybox_agent_name"]
    list_filter = ["created_at", "channel"]

    date_hierarchy = ("created_at")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class AdSmsSequenceResourceAdmin(ImportExportModelAdmin):
    resource_class = AdSmsSequenceResource
    search_fields = ["phone_number", ]
    list_filter = ["created_at", "purpose"]

    date_hierarchy = ("created_at")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class TempPayboxStaffDataResourceAdmin(ImportExportModelAdmin):
    resource_class = TempPayboxStaffDataResource
    search_fields = ["phone_number", ]
    list_filter = ["created_at",]

    date_hierarchy = ("created_at")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(AdGeneneratedUsers, AdGeneneratedUsersResourceAdmin)
admin.site.register(AdGeneneratedUserActivity, AdGeneneratedUserActivityResourceAdmin)
admin.site.register(AdSmsSequence, AdSmsSequenceResourceAdmin)
admin.site.register(TempPayboxStaffData, TempPayboxStaffDataResourceAdmin)