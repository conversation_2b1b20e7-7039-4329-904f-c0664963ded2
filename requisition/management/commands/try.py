from django.core.management.base import BaseCommand

from core.tasks import verify_requisition_transaction
from helpers.disbursment_helper import LibertyPay
from requisition.helpers.func import get_company_verification_count


class Command(BaseCommand):
    help = 'This is my custom management command'

    def handle(self, *args, **options):
        test = LibertyPay().get_agent_balance(user_ids=279)
        print(test)
