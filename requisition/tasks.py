import pytz
import pyshorteners
from datetime import datetime, timedelta
from urllib.parse import urlencode

from core.services import WhisperSms
from linkshortener.models import UrlData
from celery import shared_task
from decouple import config

from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from account.models import AccountSystem
import requests

from requisition.helpers.request_cls import drop_a_notification_on_paybox360_calendly_slack_channel
from requisition.helpers.send_sms import sms_notifications
from requisition.models import AdGeneneratedUsers, AdSmsSequence, Budget, BudgetAllocation, Requisition, TempPayboxStaffData

User = get_user_model()

@shared_task
def toggle_budget_to_inactive():
    # Get today's date in the current timezone
    today = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()
    tomorrow = today + timedelta(days=1)
    # print(tomorrow, "\n\n")
    budgets = Budget.objects.filter(end_date__lt=tomorrow, is_active=True, is_deleted=False)

    if budgets:
        # print(budgets)
        allocations = BudgetAllocation.objects.filter(budget__in=budgets, is_active=True, is_deleted=False)
        allocations.update(is_active=False, updated_at=timezone.now())
        budgets.update(is_active=False, updated_at=timezone.now())

    return "TOGGLE BUDGET TO FALSE IF END DATE PASSED CURRENT DATE"


@shared_task
def insufficient_balance_notification_on_requisition(num, team_name, purse_balance, requested_amount):
    notification_instance = sms_notifications()
    response = notification_instance.requisition_notification_on_insufficient_purse_balance(
        num=num, team_name=team_name, purse_balance=purse_balance, requested_amount=requested_amount
    )
    # print(response)
    return f"{response}"


@shared_task
def request_disbursement_notification(phone_no, first_name, request_amount):
    response = sms_notifications().requsition_disbursed_sms(num=phone_no, name=first_name, amount=request_amount)
    return f"{response}"


@shared_task
def notify_coumpany_owner_on_create_requisition(
    requisition_id, amount, name, company_phone, first_name, reason, api_key
):
    link_base = "https://www.home.paybox360.com/approve-requisitions"
    params = {"requisitionId": requisition_id}
    full_url = f"{link_base}?{urlencode(params)}"
    short_url = UrlData.slugify_url(url=full_url)
    # print(short_url)
    url = f"https://pbxl.cc/lk/{short_url}"
    # url = f"https://pbxl.cc/lk/e010030e"
    # print(url)
    try:

        sms_n = sms_notifications().requsition_created_sms(
            num=company_phone,
            name=name,
            link=url,
            amount=amount,
            requester=first_name,
            reason=reason,
        )
        return str(sms_n)
    except Exception as err:
        return str(err)


@shared_task
def perform_bulk_requisition(user_id, acct_id, wallet_type, requisitions_to_approve):
    user = User.objects.get(id=user_id)
    acct_instance = AccountSystem.objects.get(id=acct_id)
    requisitions = Requisition.objects.filter(id__in=requisitions_to_approve).distinct()
    for requisition in requisitions:
        Requisition.requisition_disbursement(
            requisition=requisition, user=user, account=acct_instance, wallet_type=wallet_type
        )
    return True




@shared_task
def ad_jotform_re_target_sms():
    ad_generated_users_qs = AdGeneneratedUsers.objects.filter(jot_form_data_submitted=False, completed_sms_sequence=False)

    for record in ad_generated_users_qs:
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        TODAYS_DATE = TODAY.date()

        # check if it's already more than 2 days since the user came in
        if (TODAY - record.created_at).days > 2:
            record.completed_sms_sequence = True
            record.save()

            msg = "Hello! \n"
            msg += "Thank you for your interest in Paybox360! We noticed you haven't submitted your details yet.\n"
            msg += "To know more about paybox360 verticals, please check this\n"
            msg += "https://oti-jeazipiu.scoreapp.com  (link to Spend solution scorecard)\n"
            msg += "https://oti-a93axb5b.scoreapp.com  (link to the HR Solution scorecard)\n"
            msg += "https://oti-tr1fgp2a.scoreapp.com (link to the Stock & Inventory Scorecard)\n"

            msg += "Your drop your inquiry here\n\n"
            msg += "Thank you!"

            send_a_whatsapp_message_to_user(record.phone_number, msg)

            record.whatsapp_message_sent = True
            record.save()


            continue

        # check if more than 6 sms sequence has been sent today
        if len(AdSmsSequence.objects.filter(purpose="FILL_JOTFORM", phone_number=record.phone_number, created_at__date=TODAYS_DATE)) >= 6:
            continue  # Skip to the next record if the limit is reached

        # check if the last one sent is more than 30 minutes ago
        instance = AdSmsSequence.objects.filter(purpose="FILL_JOTFORM", phone_number=record.phone_number, created_at__date=TODAYS_DATE).last()
        if instance:
            if (TODAY - instance.created_at).total_seconds() > 1800:  # 30 minutes
                message = "Hello! \n"
                message += "Thank you for your interest in Paybox360! We noticed you haven't submitted your details yet.\n"
                message += "Please take a moment to fill out your information using the link below:\n"
                message += f"{config('JOT_FORM_FOR_PAYBOX_USSD_CAMAPIGN')}\n"
                message += "Your input helps us serve you better. Thank you!"

                WhisperSms.send_generic_sms(
                    phone_number=record.phone_number,
                    message=message
                )

                AdSmsSequence.objects.create(
                    phone_number=record.phone_number
                )
            continue  

        # If no SMS was sent in the last 30 minutes, send a new one
        message = "Hello! \n"
        message += "Thank you for your interest in Paybox360! We noticed you haven't submitted your details yet.\n"
        message += "Please take a moment to fill out your information using the link below:\n"
        message += f"{config('JOT_FORM_FOR_PAYBOX_USSD_CAMAPIGN')}\n"
        message += "Your input helps us serve you better. Thank you!"

        WhisperSms.send_generic_sms(
            phone_number=record.phone_number,
            message=message
        )

        AdSmsSequence.objects.create(
            phone_number=record.phone_number
        )

    return "DONE PROCESSING PENDING REQUEST"




def send_a_whatsapp_message_to_user(phone_number, message):
    url = f"{config('LIBERTY_PAY_MARKETING_BASE_URL')}/api/send-message/"
    payload = {
        "message": message,
        "chat_id": phone_number
    }

    headers = {
        'Content-Type': 'application/json',
    }

    try:
        res = requests.request("POST", url, headers=headers, json=payload)
    except Exception as e:
        res = f"Error sending notification: {e}" 
    
    return res





@shared_task
def assign_leads_to_paybox_staff(leads_phone_number):
    try:
        ad_generated_user_instance = AdGeneneratedUsers.objects.get(phone_number=leads_phone_number, is_assigned = False)
    except AdGeneneratedUsers.DoesNotExist:
        return "USER NOT FOUND"
    
    # get all the agent and check who has the least number of leads assigned to them
    # assign the lead to the agent with the least number of leads
    # if all agents have the same number of leads, assign to the first agent
    # if no agent is available, assign to the first agent

    agents = TempPayboxStaffData.objects.filter(is_available = True)

    if not agents:
        return "NO AGENT AVAILABLE"
    

    agents_leads_count = {}

    for agent in agents:
        agents_leads_count[agent.phone_number] = AdGeneneratedUsers.objects.filter(is_assigned=True, paybox_agent_phone_number=agent.phone_number).count()
    

    # get the agent with the least number of leads
    min_leads = min(agents_leads_count.values())
    min_leads_agent = [agent for agent, count in agents_leads_count.items() if count == min_leads][0]


    min_agent_instance = TempPayboxStaffData.objects.get(phone_number=min_leads_agent)

    ad_generated_user_instance.is_assigned = True
    ad_generated_user_instance.paybox_agent_phone_number = min_leads_agent
    ad_generated_user_instance.paybox_agent_name = f"{min_agent_instance.first_name} {min_agent_instance.last_name}"
    ad_generated_user_instance.save()

    # send a slack sms to the agents
    slack_msg = f"Hello {min_agent_instance.first_name}, you have been assigned a new lead with phone number {leads_phone_number}"

    try:
        drop_a_notification_on_paybox360_calendly_slack_channel(slack_msg)
    except Exception as e:
        # Optionally log the exception
        print(f"Error sending notification: {e}")