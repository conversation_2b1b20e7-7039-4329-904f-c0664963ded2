from import_export import resources

from requisition.models import AdGeneneratedUserActivity, AdGeneneratedUsers, AdSmsSequence, JotFormDataSync, MonoTopUpRecord, Team, Company, Requisition, Comment, TeamMember, Category, Expense, Budget, \
    CompanyVerificationInfo, CompanyVerificationMetaData, CompanyIndustry, BudgetAllocation, TeamMemberInvite, TempPayboxStaffData, UserLinkedBanks


class TeamResource(resources.ModelResource):
    class Meta:
        model = Team

class MonoTopUpRecordResource(resources.ModelResource):
    class Meta:
        model = MonoTopUpRecord
class UserLinkedBanksResource(resources.ModelResource):
    class Meta:
        model = UserLinkedBanks


class CompanyResource(resources.ModelResource):
    class Meta:
        model = Company


class TeamMemberResource(resources.ModelResource):
    class Meta:
        model = TeamMember


class RequisitionResource(resources.ModelResource):
    class Meta:
        model = Requisition
    
    def dehydrate_user(self, obj):
        return obj.user.email if obj.user else ""
    
    def dehydrate_approved_by(self, obj):
        return obj.approved_by.email if obj.approved_by else ""
    
    def dehydrate_company(self, obj):
        return obj.company.company_name if obj.company else ""
    
    def dehydrate_team(self, obj):
        return obj.team.team_name if obj.team else ""
    
    def dehydrate_member(self, obj):
        return obj.member.email if obj.member else ""


class CategoryResource(resources.ModelResource):
    class Meta:
        model = Category


class CommentResource(resources.ModelResource):
    class Meta:
        model = Comment


class ExpenseResource(resources.ModelResource):
    class Meta:
        model = Expense

    def dehydrate_user(self, obj):
        return obj.user.email if obj.user else ""
    
    def dehydrate_budget(self, obj):
        return obj.budget.budget_name if obj.budget else ""
    
    def dehydrate_team(self, obj):
        return obj.team.team_name if obj.team else ""
    
    def dehydrate_company(self, obj):
        return obj.company.company_name if obj.company else ""

class BudgetResource(resources.ModelResource):
    class Meta:
        model = Budget
    
    def dehydrate_user(self, obj):
        return obj.user.email if obj.user else ""
    
    def dehydrate_company(self, obj):
        return obj.company.company_name if obj.company else ""
    
    def dehydrate_team(self, obj):
        return obj.team.team_name if obj.team else ""


class CompanyVerificationInfoResource(resources.ModelResource):
    class Meta:
        model = CompanyVerificationInfo


class CompanyVerificationMetaDataResource(resources.ModelResource):
    class Meta:
        model = CompanyVerificationMetaData


class CompanyIndustryResource(resources.ModelResource):
    class Meta:
        model = CompanyIndustry


class BudgetAllocationResource(resources.ModelResource):
    class Meta:
        model = BudgetAllocation

class TeamMemberInviteResource(resources.ModelResource):
    class Meta:
        model = TeamMemberInvite



class JotFormDataSyncResource(resources.ModelResource):
    class Meta:
        model = JotFormDataSync


class AdGeneneratedUsersResource(resources.ModelResource):
    class Meta:
        model = AdGeneneratedUsers


class AdGeneneratedUserActivityResource(resources.ModelResource):
    class Meta:
        model = AdGeneneratedUserActivity


class AdSmsSequenceResource(resources.ModelResource):
    class Meta:
        model = AdSmsSequence

class TempPayboxStaffDataResource(resources.ModelResource):
    class Meta:
        model = TempPayboxStaffData
