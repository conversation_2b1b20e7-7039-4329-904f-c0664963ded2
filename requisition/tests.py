from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient, APITestCase
from django.contrib.auth import get_user_model
from requisition.models import Team, TeamMember, Company
from stock_inventory.models import Branch

User = get_user_model()

class BaseTestSetup(APITestCase):

    def setUp(self):
        self.client = APIClient()

        # Create a user and authenticate
        self.user = User.objects.create_user(
            email='<EMAIL>', password='testpass', first_name='<PERSON>', last_name='<PERSON><PERSON>'
        )
        self.client.force_authenticate(user=self.user)

        self.user2 = User.objects.create_user(
            email='<EMAIL>', password='testpass', first_name='Gegs', last_name='Lod'
        )

        # Create a company
        self.company = Company.objects.create(company_name='Test Company', user=self.user)

        # Create a branch
        self.branch = Branch.objects.create(name='Test Branch', company=self.company, vat=90.00, created_by=self.user)

        # Create a team
        self.team = Team.objects.create(
            user=self.user,
            team_name='Test Team',
            company=self.company,
            branch=self.branch
        )

        # Create team members
        self.team_member = TeamMember.objects.create(
            member=self.user,
            email='<EMAIL>',
            phone_no='1234567890',
            team=self.team
        )

        self.team_member2 = TeamMember.objects.create(
            member=self.user2,
            email='<EMAIL>',
            phone_no='0987654321',
            team=self.team
        )


class ListFilterTeamTests(BaseTestSetup):

    def test_search_team_by_team_name(self):
        response = self.client.get(
            reverse('list-filter-team'), {'search': 'Test Team'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['team_name'], 'Test Team')

    def test_search_team_by_member_first_name(self):
        response = self.client.get(
            reverse('list-filter-team'), {'search': 'jo'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['team_name'], 'Test Team')

    def test_search_team_by_member_email(self):
        response = self.client.get(
            reverse('list-filter-team'), {'search': '<EMAIL>'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['team_name'], 'Test Team')

    def test_search_team_no_results(self):
        response = self.client.get(
            reverse('list-filter-team'), {'search': 'Nonexistent Team'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)

    def test_filter_team_by_company(self):
        response = self.client.get(
            reverse('list-filter-team'), {'company': self.company.id}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['company_id'], str(self.company.id))

    def test_filter_team_by_branch(self):
        response = self.client.get(
            reverse('list-filter-team'), {'branch': self.branch.id}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['branch'], self.branch.name)



class ListFilterTeamMembersTests(BaseTestSetup):

    def test_filter_team_members_by_team_id(self):
        response = self.client.get(
            reverse('list-filter-team-members'), {'team_id': self.team.id}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)

    def test_filter_team_members_invalid_team_id(self):
        response = self.client.get(
            reverse('list-filter-team-members'), {'team_id': 'e69bf921-c1ed-4db3-8e75-06d5304635f5'}
        )   
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)

    
    def test_search_team_members_by_first_name(self):

        response = self.client.get(
            reverse('list-filter-team-members'), {'team_id': self.team.id, 'search': 'Gegs'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['first_name'], 'Gegs')

    def test_search_team_members_by_last_name(self):

        response = self.client.get(
            reverse('list-filter-team-members'), {'team_id': self.team.id, 'search': 'Lod'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['last_name'], 'Lod')

    def test_search_team_members_by_email(self):

        response = self.client.get(
            reverse('list-filter-team-members'), {'team_id': self.team.id, 'search': 'gegs@liberty'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['email'], '<EMAIL>')


    def test_search_team_members_by_role(self):
        self.team_member.role = 'Manager'
        self.team_member.save()

        response = self.client.get(
            reverse('list-filter-team-members'), {'team_id': self.team.id, 'search': 'Manager'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['role'], 'Manager')


    def test_no_team_members_found(self):
        response = self.client.get(
            reverse('list-filter-team-members'), {'search': 'Nonexistent'}
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

