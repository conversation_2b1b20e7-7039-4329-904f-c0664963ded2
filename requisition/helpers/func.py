import json
from datetime import timed<PERSON><PERSON>, datetime

from core.helpers.func import <PERSON><PERSON><PERSON><PERSON><PERSON>
from helpers.redis_storage import RedisStore


def iterator_helper_to_get_values(data: list, actual_key: str, expected_value: str, expected_to_return_val: str):
    to_get_value = 0  # Initialize to_get_value variable

    for item_ins in data:
        if item_ins.get(actual_key) == expected_value:
            to_get_value = item_ins.get(expected_to_return_val)
            break
    return to_get_value


def get_company_verification_count(user_id):
    redis_storage_object = RedisStore()
    verification_key = f"VER_{user_id}"
    val = redis_storage_object.get_data(key=verification_key)
    expiration = 60 * 60 * 24

    retry_datetime = str(datetime.now() + timedelta(days=1))

    # print(val)
    if val is None:
        # print("DOES NOT EXIST")
        request_count = 1
        value = {"count": request_count, "retry_datetime": retry_datetime}
        redis_storage_object.set_data_with_expiration(cache_key=verification_key, value=json.dumps(value),
                                                      expiration=expiration)
        return value

    else:
        # print("EXIST")
        string_result = ByteHelper.convert_byte_to_string(val)
        # print(string_result)
        request_count = json.loads(string_result)
        count = request_count.get("count")
        int_request_count = int(count)

        if int_request_count >= 3:
            return request_count

        # print(int_request_count, "\n")
        updated_count = int_request_count + 1
        value = {"count": updated_count, "retry_datetime": retry_datetime}
        redis_storage_object.set_data_with_expiration(cache_key=verification_key,
                                                      value=json.dumps(value), expiration=expiration)
        return value

def find_duplicate_email(members):
    email_count = {}
    duplicates = []

    # Count occurrences of each email
    for member in members:
        email = member['email']
        if email in email_count:
            email_count[email] += 1
        else:
            email_count[email] = 1

    # Collect emails that have more than one occurrence
    for email, count in email_count.items():
        if count > 1:
            duplicates.append(email)
    
    return duplicates