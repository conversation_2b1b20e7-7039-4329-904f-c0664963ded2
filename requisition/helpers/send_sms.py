import json

import requests

from config import settings


def sub_num(num):
    n = num[1:]
    n = "234" + str(n)
    return n


class sms_notifications:
    def __init__(self):
        self.url = settings.WHISPER_TRANSACTIONAL_URL
        self.headers = {
            "Authorization": f"Api_key {settings.WHISPER_KEY}",
            "Content-Type": "application/json",
        }

    def requsition_created_sms(
        self,
        num,
        name,
        link,
        amount,
        requester,
        reason,
    ):
        usr_phone = sub_num(num)
        payload = json.dumps(
            {
                "receiver": f"{usr_phone}",
                "template": settings.REMINDER_TEMPLATE,
                "place_holders": {
                    "name": f"{name}",
                    "link": f"{link}",
                    "requester": f"{requester}",
                    "amount": f"{amount}",
                    "reason": f"{reason}",
                },
            }
        )
        res = requests.request("POST", self.url, headers=self.headers, data=payload)
        return res.json()

    def requsition_disbursed_sms(
        self,
        num,
        name,
        amount,
    ):
        usr_phone = sub_num(num)
        payload = json.dumps(
            {
                "receiver": f"{usr_phone}",
                "template": settings.REQUISITION_DISBURSEMENT_TEMPLATE,
                "place_holders": {
                    "name": f"{name}",
                    "amount": f"{amount}",
                },
            }
        )
        res = requests.request("POST", self.url, headers=self.headers, data=payload)
        return res.json()

    def requisition_notification_on_insufficient_purse_balance(
        self, num, team_name, purse_balance, requested_amount
    ):
        usr_phone = sub_num(num)
        payload = json.dumps(
            {
                "receiver": f"{usr_phone}",
                "template": settings.REQUISITION_INSUFFICIENT_PURSE_BAL,
                "place_holders": {
                    "team": f"{team_name}",
                    "amount": f"{purse_balance}",
                    "balance": f"{requested_amount}",
                },
            }
        )
        res = requests.request("POST", self.url, headers=self.headers, data=payload)
        return res.json()


class MonoApis:
    def mono_key_exchange(code):
        url = "https://api.withmono.com/account/auth"

        payload = json.dumps({"code": f"{code}"})
        headers = {
            "mono-sec-key": f"{settings.MONO_WEBHOOK_SECRET}",
            "Content-Type": "application/json",
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return response.json()

    def get_bank_details(id):
        url = f"https://api.withmono.com/v1/accounts/{id}"

        payload = ""
        headers = {"mono-sec-key": f"{settings.MONO_WEBHOOK_SECRET}"}

        response = requests.request("GET", url, headers=headers, data=payload)

        return response.json()


