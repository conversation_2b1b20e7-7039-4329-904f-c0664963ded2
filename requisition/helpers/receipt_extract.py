from paddleocr import PaddleOCR
import fitz
from PIL import Image
import numpy as np
import os
import openai
import cv2

# Initialize the OCR instance
ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)  # Change GPU status to True if working on GPU
openai.api_key = '***************************************************'  # Enter your OpenAI API key

# Import OpenAI model
def get_completion(prompt, model="gpt-3.5-turbo", temperature=0):
    messages = [{"role": "user", "content": prompt}]
    response = openai.ChatCompletion.create(
        model=model,
        messages=messages,
        temperature=temperature,  # This is the degree of randomness of the model's output
    )
    return response.choices[0].message["content"]

# Prompt engineering to extract data
def prompt_engineering(input_text):
    prompt = f"""
    You are given a extracted data of invoice '''{input_text}'''\
    You have to extract information from it to include in an expense record, below are the keys need to be extracted if found otherwise keep N/A:\

    1. Date: The date when the expense was incurred. (do correction in spelling of month if required and don not inlcude time)\
    2. Amount: The cost of the expense i.e. total/Net amount. (do not imagine currency keep the same as it is)\
    3. Mode of Payment: The method used to make the payment such as credit card (VISA/MASTER), cash, COD.\
    4. Vendor: The name of the vendor or supplier from whom the goods or services were purchased (this information can also extract from URL and might be before .com or .org and not inlcude address in it).\
    5. Invoice Number: The unique identifier associated with the expense transaction. Musts be number or alphanumeric. Could be found as receipt, invoice or slip # \
    6. Location: The location where the expense occurred (e.g address).\
    7. Tax Information: type (e.g sales tax, GST) and value\
    8. Item description: showing items/services that purchased and availed. Also count quantity if available e.g 40 litres of fuel Or 15 qty of mugs\

    don't mention from where information is extracted information or (not mentioned in the given data) in output\
    if its cash memo then mode of payment is cash.

    """

    response = get_completion(prompt)
    return response

def process_invoice_file(file_path):
    print(file_path)
    if file_path.lower().endswith('.pdf'):
        # Process PDF files
        pdf_document = fitz.open(file_path)
        total_results = []
        # Convert PDF pages to images and perform OCR
        for page_number in range(pdf_document.page_count):
            page = pdf_document[page_number]
            pix = page.get_pixmap()
            # Convert Pixmap to a PIL Image object
            pil_image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            # Convert PIL Image to NumPy array
            image_np = np.array(pil_image)
            # Perform OCR and extract text
            page_text = ocr_and_extract_text(image_np)
            results = prompt_engineering(page_text)
            total_results.append(results)
        # Close the PDF document
        pdf_document.close()
        # print(total_results)
        return total_results
    else:
        # Process image files
        colored_image = cv2.imread(file_path)
        # Convert colored image to grayscale
        gray_image = cv2.cvtColor(colored_image, cv2.COLOR_BGR2GRAY)
        # Run OCR and extract text
        page_text = ocr_and_extract_text(gray_image)
        results = prompt_engineering(page_text)
        # print(results)
        return [results]

def ocr_and_extract_text(image_np):
    recognized_text = ""
    # Run OCR on the image
    results = ocr.ocr(image_np)
    for result in results[0]:
        text = result[1][0]
        recognized_text += str(text) + " "
    return recognized_text


def process_invoice_file_2(file):
    if file.name.lower().endswith('.pdf'):
        # Process PDF files
        pdf_document = fitz.open(stream=file.read(), filetype="pdf")
        total_results = []
        # Convert PDF pages to images and perform OCR
        for page_number in range(pdf_document.page_count):
            page = pdf_document[page_number]
            pix = page.get_pixmap()
            pil_image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            image_np = np.array(pil_image)
            page_text = ocr_and_extract_text(image_np)
            results = prompt_engineering(page_text)
            total_results.append(results)
        pdf_document.close()
        return total_results
    else:
        # Process image files
        colored_image = cv2.imdecode(np.fromstring(file.read(), np.uint8), cv2.IMREAD_COLOR)
        gray_image = cv2.cvtColor(colored_image, cv2.COLOR_BGR2GRAY)
        page_text = ocr_and_extract_text(gray_image)
        results = prompt_engineering(page_text)
        
        return [results]

# Rest of your code remains unchanged