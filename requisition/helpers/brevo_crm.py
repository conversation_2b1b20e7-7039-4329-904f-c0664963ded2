from decouple import config
import requests


class BrevoMarketingApis:
    def __init__(self) -> None:
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'api-key': config("BREVO_API_KEY")
        }

        self.base_url = "https://api.brevo.com/v3"
    

    def create_folder(self, folder_name):
        payload = {
            "name": folder_name
        }

        url = f"{self.base_url}/contacts/folders"

        response = requests.request("POST", url, headers=self.headers, json=payload)

        # {'id': 32}

        try:
            return response.json()
        except:
            return response.text
        
    
    def create_list(self, folder_id, list_name):
        payload = {
            "name": list_name,
            "folderId": folder_id
        }

        url = f"{self.base_url}/contacts/lists"

        response = requests.request("POST", url, headers=self.headers, json=payload)

        # {'id': 33}

        try:
            return response.json()
        except:
            return response.text
        
    
    def create_contact(self, first_name, last_name, phone_number, email, list_id=33):
        payload = {
            "email": email,
            "attributes": {
                "FIRSTNAME": first_name,
                "LASTNAME": last_name,
                "PHONE": phone_number 
            },
            "emailBlacklisted": False,
            "smsBlacklisted": False,
            "listIds": [
                list_id
            ],
            "updateEnabled": False,
            "smtpBlacklistSender": []
        }

        

        url = f"{self.base_url}/contacts"

        response = requests.request("POST", url, headers=self.headers, json=payload)

        try:
            return response.json()
        except:
            return response.text