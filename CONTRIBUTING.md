# Git Workflow and Commit Message Guidelines

## Table of Contents

- [Branching Strategy](#branching-strategy)

- [Commit Message Prefixes](#commit-message-prefixes)

- [Workflow Overview](#workflow-overview)

- [Pull Requests and Code Reviews](#pull-requests-and-code-reviews)

- [Rebasing and Merging](#rebasing-and-merging)

- [Best Practices](#best-practices)

---

## Branching Strategy

To maintain a clean and manageable Git history, we use the following branching strategy:

- **`master`**: The production-ready branch. Code here should always be stable.

- **`staging`**: The integration branch for features. This branch contains the latest code that will eventually be merged into `master`.

- **Feature Branches**: 

  - **Naming**: `feature/<feature-name>`

  - **Purpose**: For developing new features.

- **Bugfix Branches**:

  - **Naming**: `bugfix/<issue-name>`

  - **Purpose**: For fixing bugs or issues.

- **Hotfix Branches**:

  - **Naming**: `hotfix/<hotfix-name>`

  - **Purpose**: For critical fixes that need to be applied directly to `master`.

---

## Commit Message Prefixes

We use specific prefixes in commit messages to categorize changes. This helps in generating changelogs and understanding the impact of each commit.

### **Prefixes**

- **`feat:`** A new feature.

- **`fix:`** A bug fix.

- **`docs:`** Documentation-only changes.

- **`style:`** Code style changes (formatting, etc.).

- **`refactor:`** Code changes that neither fix a bug nor add a feature.

- **`perf:`** Performance improvements.

- **`test:`** Adding or updating tests.

- **`build:`** Changes to the build system or dependencies.

- **`ci:`** Changes to CI configuration files and scripts.

- **`chore:`** Miscellaneous tasks (e.g., updating scripts).

- **`revert:`** Reverting a previous commit.

- **`hotfix:`** An urgent bug fix, usually for production.

### **Example Commit Message**

```bash

git commit -m "feat: add user authentication API with JWT support"

```

---

## Workflow Overview

### **1. Start from the Latest Branch**

- Check out the branch you want to base your work on (`staging` or `master`).

- Pull the latest changes:

  ```bash

  git checkout staging

  git pull origin staging

  ```

### **2. Create a New Branch**

- Create a feature, bugfix, or hotfix branch:

  ```bash

  git checkout -b feature/add-user-authentication

  ```

### **3. Commit Your Changes**

- Make frequent, small commits with meaningful messages:

  ```bash

  git add .

  git commit -m "feat: add user authentication API"

  ```

### **4. Stay Up to Date**

- Regularly rebase your branch with the latest changes from `staging`:

  ```bash

  git pull origin staging --rebase

  ```

### **5. Push Your Branch**

- Push your branch to the remote repository:

  ```bash

  git push -u origin feature/add-user-authentication

  ```

---

## Pull Requests and Code Reviews

1\. **Create a Pull Request**: 

   - Once your work is complete, push your branch and open a pull request (PR) against `staging`.

   - Include a clear description of your changes.

2\. **Request a Review**: 

   - Request code review.

3\. **Address Feedback**: 

   - Make any requested changes, then update your PR.

4\. **Merge**: 

   - After approval, your PR will be merged into the target branch.

---

## Rebasing and Merging

- **Rebase**: 

  - Use rebase to keep your feature branch up to date with `staging` or `master`.

  - Avoid rebasing commits that have already been pushed to a shared branch.

- **Merging**: 

  - For shared branches (`master`, `staging`), use merge instead of rebase to preserve history and avoid conflicts.

  - Use `git merge` to integrate changes from the base branch.

---

## Best Practices

- **Frequent Commits**: Commit often, with clear, descriptive messages.

- **Small Changes**: Aim to keep each commit focused on a single task or issue.

- **Rebase Regularly**: Stay up to date with the base branch to minimize conflicts.

- **Avoid Force Pushes**: Avoid `git push --force` unless absolutely necessary and communicated with the team.

- **Communicate**: Always communicate with the team before making significant changes or rebasing shared branches.

---

This workflow and commit message convention will help us maintain a clean, organized, and efficient development process. Please follow these guidelines to ensure smooth collaboration across the team.