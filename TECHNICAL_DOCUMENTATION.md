# Project Technical Documentation

## 1. Project Overview
**Name:** Liberty Requisition
**Description:** A system to manage requisitions efficiently.
**Purpose / Goal:** Streamline the requisition process for better tracking and management.
**Main Features:** Requisition creation, approval workflows, reporting, and integration with accounting systems.

## 2. Architecture Overview
- **Diagram:** (insert or link to image)
- **Components:**
  - **Frontend:** Not applicable (backend-focused project)
  - **Backend:** Django-based backend
  - **Database:** PostgreSQL
  - **External APIs:** Integration with accounting systems
- **Data Flow:**
  - Data flows from user input through the backend to the database and external APIs for processing and storage.

## 3. Tech Stack
- **Backend:** Django, Celery
- **Database:** PostgreSQL
- **Others:** Redis, RabbitMQ, S3 for storage

## 4. Setup & Installation
```bash
git clone <repo>
cd liberty-requisition
pip install -r requirements.txt
cp .env.example .env
python manage.py runserver
```

## 5. Configuration
**Environment Variables:**
- `DATABASE_URL`: Connection string for PostgreSQL
- `SECRET_KEY`: Used for authentication
- `REDIS_URL`: Redis connection string
- `RABBITMQ_URL`: RabbitMQ connection string

## 6. Code Structure
```
account/
accounting/
cart_management/
clock_app/
config/
core/
dashboard/
finance_system/
helpers/
instant_web/
```
Explanation of Key Folders and Files:
- `account/`: Handles user accounts and authentication.
- `accounting/`: Manages financial transactions and integrations.
- `cart_management/`: Manages shopping cart functionalities.
- `config/`: Contains project settings and configurations.
- `core/`: Core utilities and shared resources.
- `helpers/`: Helper functions and utilities.

## 7. API Documentation
**Auth**
- `POST /api/login`
  - **Request:** `{ "email": "...", "password": "..." }`
  - **Response:** `{ "token": "...", "user": {...} }`

**Requisitions**
- `GET /api/requisitions`
- `POST /api/requisitions`

## 8. Database Schema
**Models / Tables:**
- **User:** `id`, `name`, `email`
- **Requisition:** `id`, `title`, `status`, `created_by`
- **Relationships:** User ↔ Requisition (One-to-Many)

## 9. Authentication & Authorization
- **Auth Strategy:** JWT token-based authentication
- **Roles:** Admin, User
- **Permissions Matrix:** Defined per endpoint

### Authentication Details
- **Login Endpoint:**
  - `POST /api/login`
  - **Request:** `{ "email": "...", "password": "..." }`
  - **Response:** `{ "token": "...", "user": {...} }`
- **Token Expiry:** Tokens expire after 24 hours and require re-authentication.
- **Refresh Tokens:**
  - Endpoint: `POST /api/token/refresh`
  - **Request:** `{ "refresh": "..." }`
  - **Response:** `{ "access": "..." }`
- **Password Reset:**
  - Endpoint: `POST /api/password-reset`
  - **Request:** `{ "email": "..." }`
  - **Response:** `{ "message": "Password reset link sent." }`

### Onboarding Process
1. **User Registration:**
   - Endpoint: `POST /api/register`
   - **Request:** `{ "name": "...", "email": "...", "password": "..." }`
   - **Response:** `{ "message": "Registration successful. Please verify your email." }`
2. **Email Verification:**
   - Endpoint: `GET /api/verify-email?token=...`
   - **Response:** `{ "message": "Email verified successfully." }`
3. **Profile Setup:**
   - Endpoint: `POST /api/profile`
   - **Request:** `{ "user_id": "...", "profile_data": {...} }`
   - **Response:** `{ "message": "Profile setup complete." }`
4. **KYC Verification:**
   - Endpoint: `POST /api/kyc`
   - **Request:** `{ "user_id": "...", "documents": ["doc1", "doc2"] }`
   - **Response:** `{ "message": "KYC verification in progress." }`
   - **Details:** Users are required to upload government-issued ID and proof of address for verification.
5. **Approval Notification:**
   - Once KYC is approved, users receive an email notification confirming their account activation.

## 10. Background Jobs
- **Tool Used:** Celery
- **Jobs:**
  - Sync requisition status every 10 mins
  - Send approval notifications

## 11. Scripts & CLI
```bash
python manage.py refresh_tokens
python manage.py clear_expired_sessions
```

## 12. Testing
- **Framework:** PyTest
- **Run tests:**
```bash
pytest
```
- **Coverage report:** `coverage/index.html`

## 13. Deployment & CI/CD
- **Environments:** Dev, Staging, Production
- **CI/CD Tool:** GitHub Actions
- **Deployment Steps:**
  - Push to `main` triggers build & deploy to staging

## 14. Logging & Monitoring
- **Logging Tool:** Sentry
- **Logs:** Stored in `/var/logs/app.log`
- **Monitoring:** Prometheus, Uptime Robot

## 15. Known Issues & Limitations
- Limited support for multi-tenancy
- No frontend interface

## 16. Contributing
Refer to `CONTRIBUTING.md` for guidelines.

## 17. Appendices
- Glossary
- Example data
- Helper links
