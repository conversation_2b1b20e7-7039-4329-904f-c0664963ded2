from uuid import UUID

from django.conf import settings
from django.db import IntegrityError, transaction
from django.db.models import Max, Q
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import generics, filters, status
from rest_framework.exceptions import ValidationError
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Prefetch
from rest_framework.parsers import JSONParser
import json
import pandas as pd
from io import BytesIO
from django.http import HttpResponse

from account.helpers.core_banking import CoreBankingService
from account.models import CoreBankingCallback
from cart_management.filters import OrderFilter
from cart_management.models import (
    Buyer,
    Cart,
    CartItem,
    Order,
    OrderPipeline,
    OrderProduct,
    OrderStage,
    Product, IncompleteOrderRecord,
)
from cart_management.serializers import (
    BuyerSerializer,
    CartSerializer,
    CartItemSerializer,
    CreateOrderSerializer,
    CustomerDashboardSerializer,
    OrderListSerializer,
    OrderPipelineSerializer,
    OrderStageSerializer,
    OrderSerializer,
    PaymentRecordSerializer, IncompleteOrderRecordSerializer,
)
from core.auth.custom_auth import CustomUserAuthentication
from core.models import PaystackPayment
from core.pagenator import CustomPagination
from helpers.reusable_functions import is_valid_uuid
from instant_web.models import InstantWeb
from requisition.models import Company
from sales_app.helper.enums import MeansOfPaymentChoices
from sales_app.models import SalesTransaction
from stock_inventory.models import Branch


logger = settings.LOGGER


# Create your view(s) here.
class CustomDashboardPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 60
    page_query_param = "page"


class CartView(APIView):
    """
    API view to retrieve the cart for the current user or session.

    Methods:
        get_cart(request): Retrieves the cart for the authenticated user or the current session.
        get(request): Handles GET requests to return the cart details.
    """

    def get_cart(self, request):
        """
        Retrieves the cart for the authenticated user or the current session.

        Args:
            request: The HTTP request object.

        Returns:
            cart: The Cart object if it exists, otherwise None.
        """
        if request.user.is_authenticated:
            try:
                cart = Cart.objects.get(buyer=request.user.buyer)
            except Cart.DoesNotExist:
                cart = None
        else:
            session_id = request.session.session_key
            if not session_id:
                request.session.save()
                session_id = request.session.session_key
            try:
                cart = Cart.objects.get(session_id=session_id)
            except Cart.DoesNotExist:
                cart = None
        return cart

    def get(self, request):
        """
        Handles GET requests to return the cart details.

        Args:
            request: The HTTP request object.

        Returns:
            Response: A response object containing the serialized cart data or a 404 error if the cart does not exist.
        """
        cart = self.get_cart(request)
        if cart is None:
            return Response(
                {"error": "Cart does not exist"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = CartSerializer(cart)
        return Response(serializer.data)


class AddToCartView(APIView):
    """
    API view to add a product to the cart for the current user or session.

    Methods:
        get_cart(request): Retrieves the cart for the authenticated user or the current session.
        post(request, product_id): Handles POST requests to add a product to the cart.
    """

    def get_cart(self, request):
        if request.user.is_authenticated:
            cart, created = Cart.objects.get_or_create(buyer=request.user.buyer)
        else:
            session_id = request.session.session_key
            if not session_id:
                request.session.save()
                session_id = request.session.session_key
            cart, created = Cart.objects.get_or_create(session_id=session_id)
        return cart

    def post(self, request, product_id):
        """
        Adds a product to the cart for the current user or session.

        Args:
            request: The HTTP request object.
            product_id: The ID of the product to add to the cart.

        Returns:
            Response: A response object containing the serialized cart item data.
        """
        cart = self.get_cart(request)
        product = get_object_or_404(Product, id=product_id)
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart, product=product, quantity=1
        )
        if not created:
            cart_item.quantity += 1
            cart_item.save()
        serializer = CartItemSerializer(cart_item)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class UpdateCartbuyerView(APIView):
    """
    API view to update the buyer information for the cart.

    Methods:
        patch(request): Handles PATCH requests to update the buyer information for the cart.
        get_cart(request): Retrieves the cart for the authenticated user or the current session.
    """

    def patch(self, request):
        """
        Updates the buyer information for the cart.

        Args:
            request: The HTTP request object.

        Returns:
            Response: A response object indicating success or failure.
        """
        cart = self.get_cart(request)
        if cart is None:
            return Response(
                {"error": "Cart does not exist"}, status=status.HTTP_404_NOT_FOUND
            )

        buyer_data = request.data.get("buyer")
        if not buyer_data:
            return Response(
                {"error": "Buyer data is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        phone_number = buyer_data.get("phone_number")
        buyer, created = Buyer.objects.get_or_create(
            phone_number=phone_number, defaults=buyer_data
        )

        if not created:
            serializer = BuyerSerializer(buyer, data=buyer_data, partial=True)
            if serializer.is_valid():
                buyer = serializer.save()
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        cart.buyer = buyer
        cart.save()

        serializer = CartSerializer(cart)
        return Response(
            {"success": "Cart updated with buyer information", "cart": serializer.data},
            status=status.HTTP_200_OK,
        )

    def get_cart(self, request):
        if request.user.is_authenticated:
            cart, created = Cart.objects.get_or_create(buyer=request.user.buyer)
        else:
            session_id = request.session.session_key
            if not session_id:
                request.session.save()
                session_id = request.session.session_key
            cart, created = Cart.objects.get_or_create(session_id=session_id)
        return cart


class RemoveFromCartView(APIView):
    """
    API view to remove an item from the cart.

    Methods:
        delete(request, item_id): Handles DELETE requests to remove an item from the cart.
    """

    def delete(self, request, item_id):
        """
        Removes an item from the cart.

        Args:
            request: The HTTP request object.
            item_id: The ID of the cart item to remove.

        Returns:
            Response: A response object indicating success.
        """
        cart_item = get_object_or_404(CartItem, id=item_id)
        cart_item.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CreateOrderView(APIView):

    def post(self, request):
        serializer = CreateOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = serializer.validated_data.get("company_id")
        branch = serializer.validated_data.get("branch_id")
        cart_data = serializer.validated_data.get("cart")
        buyer_data = serializer.validated_data.get("buyer")

        pipeline = OrderPipeline.objects.filter(company=company, branch=branch).last()
        if pipeline is None:
            return Response(
                data={"error": "no order pipeline found for this branch."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        first_stage = (
            OrderStage.objects.filter(pipeline=pipeline).order_by("position").first()
        )
        if not first_stage:
            return Response(
                data={"error": "no stages found in the pipeline for this branch."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        buyer_email = buyer_data.get("email")
        buyer_phone_number = buyer_data.get("phone_number")

        # Check if the buyer exists with the given email and phone number
        buyer = Buyer.objects.filter(
            email=buyer_email, phone_number=buyer_phone_number
        ).last()
        # If buyer does not exist, create a new one
        if not buyer:
            buyer = Buyer.objects.create(
                email=buyer_email,
                first_name=buyer_data.get("first_name"),
                middle_name=buyer_data.get("middle_name"),
                last_name=buyer_data.get("last_name"),
                country=buyer_data.get("country"),
                address=buyer_data.get("address"),
                state=buyer_data.get("state"),
                city=buyer_data.get("city"),
                phone_number=buyer_phone_number,
                ship_to_different_address=buyer_data.get(
                    "ship_to_different_address", False
                ),
            )
        contact_data = request.data.get("contact", {})

        order = Order.objects.create(
            company=company,
            branch=branch,
            status="open",
            payment_status="unpaid",
            buyer=buyer,
            order_date=timezone.now().date(),
            order_time=timezone.now().time(),
            current_stage=first_stage,
            shipping=request.data.get("shipping"),
            discount=request.data.get("discount"),
            tax=request.data.get("tax"),
            contact=contact_data,
            additional_information=request.data.get("additional_information"),
            ship_to_different_address=request.data.get(
                "ship_to_different_address", False
            ),
            total_price=request.data.get("total_price"),
            vendor_whatsapp_url=request.data.get("vendor_whatsapp_url"),
            trail={"events": []},
        )
        subtotal = 0
        payment_option = request.data.get("buyer").get("paymentOption")

        for item in cart_data:
            product_price = item.get("price")
            product_quantity = item.get("quantity")
            product_price = item.get("price")
            product_quantity = item.get("quantity")
            product_subtotal = product_price * product_quantity

            order_product = OrderProduct.objects.create(
                orders=order,
                product=item.get("id"),
                product_name=item.get("product_name"),
                product_description=item.get("product_description"),
                product_img=item.get("product_img"),
                quantity=item.get("quantity"),
                price=item.get("price"),
                sub_total=product_subtotal,
                payment_option=payment_option,
            )
            subtotal += product_subtotal

        # Update the order with the calculated subtotal
        sub_total = float(subtotal) - float(order.discount)
        order.total_price = sum(
            [float(sub_total), float(order.shipping), float(order.tax)]
        )
        order.save()

        if payment_option == MeansOfPaymentChoices.CARD:
            payment_details = PaystackPayment.generate_payment_link(
                email=company.user.email,
                amount=float(order.total_price),
                reference=order.order_id,
            )
        if payment_option == MeansOfPaymentChoices.DELIVERY:
            payment_details = None
        if payment_option == MeansOfPaymentChoices.TRANSFER:
            pass
            request_handler = CoreBankingService()
            response = request_handler.get_one_time_account(
                request_reference=order.order_id,
                sub_company=order.branch.id,
            )
            if response.get("status") and response.get("status_code") != 400:
                if not isinstance(response.get("data"), str):
                    response_data = (
                        response.get("data").get("data").get("account_details")
                    )
                    payment_details = {
                        "amount": "",
                        "bank_name": response_data.get("bank_name"),
                        "bank_code": response_data.get("bank_code"),
                        "account_name": response_data.get("account_name"),
                        "account_number": response_data.get("account_number"),
                    }
                else:
                    payment_details = {
                        "amount": 0.0,
                        "bank_name": None,
                        "bank_code": None,
                        "account_name": None,
                        "account_number": None,
                    }
            else:
                payment_details = {
                    "amount": 0.0,
                    "bank_name": None,
                    "bank_code": None,
                    "account_name": None,
                    "account_number": None,
                }
        if payment_option == MeansOfPaymentChoices.USSD:
            payment_details = None

        # Check if an incomplete order record exists for this buyer's phone number and delete it
        self.delete_incomplete_order(buyer_phone_number)

        # Update trail
        trail_entry = {
            "event": "new order",
            "timestamp": timezone.now().isoformat(),
            "stage_name": first_stage.name,
            # "moved_by": request.user.get_full_name() if request.user.is_authenticated else "System"
        }
        order.trail["events"].append(trail_entry)

        order.save()
        self.clear_cart(request)

        serialized_buyer = BuyerSerializer(buyer).data

        serialized_order = OrderSerializer(order)
        serialized_order_data = serialized_order.data
        serialized_order_data.update(
            {
                "company_id": company.id,
                "company_name": company.company_name,
                "branch_id": branch.id,
                "branch_name": branch.name,
            }
        )
        data = {
            "checkout_order": {
                "id": order.id,
                "order_id": order.order_id,
                "company": order.company.company_name,
                "company_id": str(order.company.id),
                "branch": order.branch.name,
                "branch_id": str(order.branch.id),
                "buyer": order.buyer.email,
                "date": order.order_date,
                "time": order.order_time,
                "status": order.status,
                "payment_status": order.payment_status,
                "shipping": order.shipping,
                "discount": order.discount,
                "tax": order.tax,
                "total_price": order.total_price,
            },
            "buyer_details": serialized_buyer,
            "payment_details": payment_details,

        }
        return Response(data=data, status=status.HTTP_201_CREATED)

    def get_cart(self, request):
        print("Retrieving cart for the user.")
        if request.user.is_authenticated:
            cart, created = Cart.objects.get_or_create(buyer=request.user.buyer)
        else:
            session_id = request.session.session_key
            if not session_id:
                request.session.save()
                session_id = request.session.session_key
            cart, created = Cart.objects.get_or_create(session_id=session_id)
        print("Cart retrieved:", cart, "Created:", created)
        return cart

    def clear_cart(self, request):
        cart = self.get_cart(request)
        cart.items.all().delete()
        print("Cart items deleted.")

    def delete_incomplete_order(self, phone_number):
        """Delete the incomplete order record that matches the given phone number."""
        try:
            incomplete_order = IncompleteOrderRecord.objects.filter(phone=phone_number).last()
            if incomplete_order:
                incomplete_order.delete()
                print(f"Incomplete order record for phone number {phone_number} deleted.")
            else:
                print(f"No incomplete order record found for phone number {phone_number}.")
        except Exception as e:
            print(f"Error while deleting incomplete order record: {e}")


class GetOrderDetailAPIView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, order_id):

        if not order_id:
            return Response(
                {"message": "order_id is a required parameter"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            return Response(
                {"message": "Order not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = OrderSerializer(order)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ManagePipelineStagesAPIView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    """
    API view to manage pipeline stages including reordering, updating, and deleting stages.

    Methods:
        put(request, pipeline_id): Handles PUT requests to reorder stages within a pipeline.
        patch(request, stage_id): Handles PATCH requests to update a specific stage.
        delete(request, stage_id): Handles DELETE requests to remove a specific stage.
    """

    def post(self, request, pipeline_id):
        # Get the pipeline instance with custom error message
        try:
            pipeline = OrderPipeline.objects.get(id=pipeline_id)
        except OrderPipeline.DoesNotExist:
            return Response(
                {"error": "Invalid pipeline ID provided"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Create a list to store newly created stages
        created_stages = []

        # Get the current maximum position of stages in the pipeline
        max_position = pipeline.stages.aggregate(max_position=Max("position"))[
            "max_position"
        ]
        if max_position is None:
            max_position = 0

        # Check if request data is a list of stages
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data should be a list of stages."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate and save each stage
        try:
            with transaction.atomic():
                for stage_data in request.data:
                    max_position += 1
                    stage_data["position"] = max_position
                    stage_data["pipeline"] = pipeline_id

                    serializer = OrderStageSerializer(data=stage_data)
                    if serializer.is_valid():
                        stage = serializer.save()
                        created_stages.append(stage)
                    else:
                        raise ValueError(f"Invalid data for stage: {serializer.errors}")

        except ValueError as ve:
            return Response(
                {"error": str(ve)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except IntegrityError:
            return Response(
                {"error": "Database integrity error occurred while saving stages."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Serialize the created stages and return success response
        created_stages_serializer = OrderStageSerializer(created_stages, many=True)
        return Response(
            created_stages_serializer.data,
            status=status.HTTP_201_CREATED,
        )

    def put(self, request, pipeline_id):
        """
        Reorders stages within a pipeline.

        Args:
            request: The HTTP request object.
            pipeline_id: The ID of the pipeline to reorder stages for.

        Returns:
            Response: A response object indicating success or failure.
        """
        new_position = request.data.get("new_position", [])
        if not new_position:
            return Response(
                {"error": "New order of stages not provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Get the pipeline instance with custom error message
            pipeline = OrderPipeline.objects.get(id=pipeline_id)
        except OrderPipeline.DoesNotExist:
            return Response(
                {"error": "Invalid pipeline ID provided"},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            # Validate UUID format for stage IDs
            stage_ids = [UUID(id) for id in new_position]
        except ValueError:
            return Response(
                {"error": "Invalid UUID format for one or more stage IDs."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Retrieve stages based on the provided order
        stages = OrderStage.objects.filter(id__in=stage_ids, pipeline=pipeline)

        if len(stages) != len(stage_ids):
            return Response(
                {"error": "One or more stages do not belong to this pipeline."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            with transaction.atomic():
                # Update the order of stages based on the provided order
                for index, stage_id in enumerate(stage_ids):
                    stage = stages.get(id=stage_id)
                    stage.position = index
                    stage.save()

        except IntegrityError:
            return Response(
                {"error": "Database integrity error occurred while reordering stages."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Serialize the updated pipeline
        pipeline_serializer = OrderPipelineSerializer(pipeline)

        return Response(
            {
                "message": "Stages reordered successfully.",
                "job_pipeline": pipeline_serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def patch(self, request, pipeline_id, stage_id, **kwargs):
        """
        Updates a specific stage.

        Args:
            request: The HTTP request object.
            pipeline_id: The ID of the pipeline.
            stage_id: The ID of the stage to update.

        Returns:
            Response: A response object containing the serialized stage data.
        """
        try:
            # Validate the pipeline
            pipeline = OrderPipeline.objects.get(id=pipeline_id)
        except OrderPipeline.DoesNotExist:
            return Response(
                {"error": "Invalid pipeline ID provided"},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            # Validate the stage and ensure it belongs to the pipeline
            stage = OrderStage.objects.get(id=stage_id, pipeline=pipeline)
        except OrderStage.DoesNotExist:
            return Response(
                {
                    "error": "Invalid stage ID or this stage does not exist in the specified pipeline."
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = OrderStageSerializer(
            instance=stage, data=request.data, partial=True
        )
        try:
            serializer.is_valid(raise_exception=True)
            serializer.save()
        except ValidationError as e:
            return Response(
                {"error": "Validation error occurred.", "details": e.detail},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request, pipeline_id, stage_id, **kwargs):
        """
        Deletes a specific stage.

        Args:
            request: The HTTP request object.
            pipeline_id: The ID of the pipeline.
            stage_id: The ID of the stage to delete.

        Returns:
            Response: A response object indicating success or failure.
        """
        try:
            # Validate the pipeline
            pipeline = OrderPipeline.objects.get(id=pipeline_id)
        except OrderPipeline.DoesNotExist:
            return Response(
                {"error": "Invalid pipeline ID provided"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Validate the stage and ensure it belongs to the pipeline
            stage = OrderStage.objects.get(id=stage_id, pipeline=pipeline)
            stage.delete()
            return Response(
                {"message": "Stage deleted successfully."},
                status=status.HTTP_200_OK,
            )
        except OrderStage.DoesNotExist:
            return Response(
                {
                    "error": "Invalid stage ID or this stage does not exist in the specified pipeline."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PipelineDetailsView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    """
    API view to retrieve the details of a pipeline including its stages and associated orders.

    Methods:
        get(request, branch_id): Handles GET requests to return the pipeline details for a specific branch.
    """

    def get(self, request, branch_id):
        """
        Retrieves the details of a pipeline including its stages and associated orders.

        Args:
            request: The HTTP request object.
            branch_id: The ID of the branch to retrieve pipeline details for.

        Returns:
            Response: A response object containing the pipeline details including stages and orders.
        """
        # Retrieve branch details
        branch = get_object_or_404(Branch, pk=branch_id)

        instant_web = InstantWeb.objects.get(branch=branch)
        web_store_url = instant_web.store_url

        # Retrieve Pipeline Details
        pipeline = OrderPipeline.objects.filter(branch=branch).first()
        if not pipeline:
            return Response(
                {"error": "Pipeline not found for the branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Retrieve Stages
        stages_qs = OrderStage.objects.filter(pipeline=pipeline).order_by("position")
        if not stages_qs.exists():
            return Response(
                {"error": "No stages found for the pipeline"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Process Orders Data for the Entire Pipeline
        all_orders = Order.objects.filter(current_stage__in=stages_qs)
        if "payment_status" in request.query_params:
            all_orders = all_orders.filter(
                payment_status=request.query_params["payment_status"]
            )

        delivered_orders_count = all_orders.filter(is_delivered=True).count()
        cancelled_orders_count = all_orders.filter(is_cancelled=True).count()
        total_orders_count = all_orders.count()

        # Iterate Through Stages
        result_data = []
        for stage in stages_qs:
            # Process Orders Data for Each Stage
            orders = all_orders.filter(current_stage=stage)
            orders_count = orders.count()

            orders_data = []
            for order in orders:
                order_data = OrderSerializer(order).data
                orders_data.append(order_data)

            # Construct Stage Details
            stage_data = {
                "stage_id": stage.id,
                "stage_name": stage.name,
                "email_notification_enabled": stage.email_notification_enabled,
                "email_subject": stage.email_subject,
                "email_body": stage.email_body,
                "email_text": stage.email_text,
                "position": stage.position,
                "order_count": orders_count,
                "data": orders_data,
            }
            result_data.append(stage_data)

        # Construct Pipeline Details
        pipeline_data = {
            "pipeline_id": pipeline.id,
            "pipeline_name": pipeline.name,
            "delivered_orders_count": delivered_orders_count,
            "cancelled_orders_count": cancelled_orders_count,
            "total_orders_count": total_orders_count,
            "stages": result_data,
            "web_store_url": web_store_url,
        }

        # Return Response
        return Response(pipeline_data, status=status.HTTP_200_OK)


class PipelineForBranchView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, branch_id):
        try:
            branch = Branch.objects.get(id=branch_id)
            pipelines = OrderPipeline.objects.filter(branch=branch)
            serializer = OrderPipelineSerializer(pipelines, many=True)
            return Response({"result": serializer.data}, status=status.HTTP_200_OK)
        except Branch.DoesNotExist:
            return Response(
                {"error": "Branch not found"}, status=status.HTTP_404_NOT_FOUND
            )


class ExportStageOrdersToExcelView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Export orders of a specific stage in a pipeline to an Excel file.

        Args:
            request: The HTTP request object.
            pipeline_id: The ID of the pipeline.
            stage_id: The ID of the stage.

        Returns:
            An Excel file with the order data for the specified stage.
        """

        pipeline_id = request.query_params.get("pipeline_id")
        stage_id = request.query_params.get("stage_id")

        if not pipeline_id and stage_id:
            return Response ({"error": "Pipeline ID and Stage ID must be provided"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            pipeline = OrderPipeline.objects.get(id=pipeline_id)
        except OrderPipeline.DoesNotExist:
            return Response({"error": "Invalid pipeline ID"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            stage = OrderStage.objects.get(id=stage_id, pipeline=pipeline)
        except OrderStage.DoesNotExist:
            return Response({"error": "Invalid stage ID"}, status=status.HTTP_400_BAD_REQUEST)
        

        # Retrieve orders for the stage
        orders = Order.objects.filter(current_stage=stage)

        # Serialize order data
        orders_data = OrderSerializer(orders, many=True).data

        # If no orders, return an empty response
        if not orders_data:
            return Response({"error": "No orders found for this stage"}, status=status.HTTP_400_BAD_REQUEST)

        # Prepare the data for Excel
        result_data = []
        for order in orders_data:
            result_data.append({
                "Order ID": order["order_id"],
                "Buyer": f"{order['buyer']['first_name']} {order['buyer']['last_name']}".strip(),
                "Amount Paid": order["amount_paid"],
                "Status": order["status"],
                "Payment Status": order["payment_status"],
                "Order Date": order["order_date"],
                "Shipping": order["shipping"],
                "Total Price": order["total_price"],
                "Additional Information": order.get("additional_information", ""),
                "Is Delivered": order["is_delivered"],
                "Is Cancelled": order["is_cancelled"],
            })

        # Convert data to DataFrame
        df = pd.DataFrame(result_data)

        # Convert DataFrame to Excel
        excel_file = BytesIO()
        df.to_excel(excel_file, index=False)

        # Rewind the buffer
        excel_file.seek(0)

        # Save Excel file locally for debugging
        # with open('debug_exported_report.xlsx', 'wb') as f:
        #     f.write(excel_file.getvalue())

        response = HttpResponse(
            excel_file.getvalue(),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        response["Content-Disposition"] = f"attachment; filename=stage_{stage_id}_orders.xlsx"

        return response
    

class CustomerDashboardView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, branch_id):
        # Validate branch_id
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            return Response(
                {"error": f"Invalid branch ID"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get search parameters from the request
        search_query = request.GET.get("search", "")

        # Get all orders that are related to the specified branch_id
        orders = Order.objects.filter(current_stage__pipeline__branch=branch).distinct()

        # Get the buyers related to these orders
        buyers = Buyer.objects.filter(order__in=orders).distinct()

        # Filter buyers based on the search query
        if search_query:
            buyers = buyers.filter(
                Q(first_name__icontains=search_query)
                | Q(last_name__icontains=search_query)
                | Q(email__icontains=search_query)
                | Q(phone_number__icontains=search_query)
            ).distinct()

        response_data = []
        for buyer in buyers:
            total_orders = buyer.order_set.count()
            last_order = buyer.order_set.latest("order_date")
            name = f"{buyer.first_name or ''} {buyer.last_name or ''}".strip()
            response_data.append(
                {
                    "name": name,
                    "email": buyer.email,
                    "phone_number": buyer.phone_number,
                    "total_orders": total_orders,
                    "last_order": last_order.order_date,
                }
            )

        paginator = CustomDashboardPagination()
        paginated_data = paginator.paginate_queryset(response_data, request)

        serializer = CustomerDashboardSerializer(paginated_data, many=True)
        return paginator.get_paginated_response(serializer.data)


class OrderHistoryView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, branch_id):
        search_query = request.GET.get("search", "")
        stage_filter = request.GET.get("stage", None)
        payment_status_filter = request.GET.get("payment_status", None)

        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            return Response(
                {"error": f"Invalid branch ID"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        filters = {"current_stage__pipeline__branch_id": branch_id}

        if stage_filter:
            filters["current_stage__name"] = stage_filter
        if payment_status_filter:
            filters["payment_status"] = payment_status_filter

        # Apply search filter
        orders = (
            Order.objects.filter(**filters)
            .filter(
                Q(buyer__email__icontains=search_query)
                | Q(orderproduct__product_name__icontains=search_query)  # Update related name
                | Q(order_id__icontains=search_query)
            )
            .select_related("buyer", "current_stage")
            .prefetch_related(Prefetch('orderproduct_set', queryset=OrderProduct.objects.all()))  # Correct relation
            .distinct()
        )

        response_data = []
        for order in orders:
            order_products = order.orderproduct_set.all()  # Use order.orderproduct_set to access related objects
            for order_product in order_products:
                response_data.append(
                    {
                        "item": order_product.product_name,
                        "email": order.buyer.email,
                        "current_stage": (
                            order.current_stage.name if order.current_stage else "N/A"
                        ),
                        "payment_status": order.payment_status,
                        "order_date": order.order_date.strftime("%d-%m-%Y"),
                        "order_time": order.order_time.strftime("%H:%M:%S"),
                        "last_updated": order.updated_at.strftime("%d-%m-%Y"),
                    }
                )

        paginator = CustomDashboardPagination()
        paginated_data = paginator.paginate_queryset(response_data, request)

        return paginator.get_paginated_response(paginated_data)



class OrderProgression(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        pipeline_id = request.data.get("pipeline_id")
        stage_id = request.data.get("new_stage_id")
        order_ids = request.data.get("order_ids", [])

        if not pipeline_id or not stage_id or not order_ids:
            return Response(
                {"message": "Pipeline ID, Stage ID, and Order IDs are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Ensure pipeline and stage are valid
        try:
            stage_instance = OrderStage.objects.get(
                id=stage_id, pipeline_id=pipeline_id
            )
        except OrderStage.DoesNotExist:
            return Response(
                {"message": "Invalid pipeline ID or stage ID"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        invalid_orders = []

        # Validate each order ID
        for order_id in order_ids:
            try:
                Order.objects.get(id=order_id)
            except Order.DoesNotExist:
                invalid_orders.append(order_id)

        if invalid_orders:
            return Response(
                {
                    "message": "One or more order IDs are invalid.",
                    "invalid_orders": invalid_orders,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        updated_orders = []

        # Process each order
        for order_id in order_ids:
            try:
                order = Order.objects.get(id=order_id)
                old_stage = order.current_stage
                order.current_stage = stage_instance
                if stage_instance.name == "Completed":
                    order.is_delivered = True
                    order.status = "closed"
                order.save()

                # Update trail
                trail_entry = {
                    "event": "progression",
                    "timestamp": timezone.now().isoformat(),
                    "stage_name": stage_instance.name,
                    # "moved_by": request.user.get_full_name() if request.user.is_authenticated else "System"
                }
                order.trail["events"].append(trail_entry)
                order.save()

                # Send email notification if enabled for the stage
                if stage_instance.email_notification_enabled:
                    self.send_stage_notification(stage_instance, order)

                updated_orders.append(order_id)
            except Order.DoesNotExist:
                # This should not happen as we already validated all order IDs
                continue

        return Response(
            {
                "message": f"Orders have been moved to {stage_instance.name} stage",
                "updated_orders": updated_orders,
            },
            status=status.HTTP_200_OK,
        )


class AcceptOrderView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        order_ids = request.data.get("order_ids", [])
        
        if not order_ids:
            return Response({"error": "No order IDs provided"}, status=status.HTTP_400_BAD_REQUEST)

        success_orders = []
        failed_orders = []

        for order_id in order_ids:
            try:
                order = Order.objects.get(id=order_id)
                current_stage = order.current_stage

                next_stage = (
                    OrderStage.objects.filter(
                        pipeline=current_stage.pipeline, position__gt=current_stage.position
                    )
                    .order_by("position")
                    .first()
                )

                if next_stage:
                    order.current_stage = next_stage

                    # Update trail
                    trail_entry = {
                        "event": "order accepted",
                        "timestamp": timezone.now().isoformat(),
                        "stage_name": next_stage.name,
                    }
                    order.trail["events"].append(trail_entry)
                    order.save()
                    success_orders.append(order_id)
                else:
                    failed_orders.append({"order_id": order_id, "error": "No next stage found"})

            except Order.DoesNotExist:
                failed_orders.append({"order_id": order_id, "error": "Invalid order ID"})
            except Exception as e:
                failed_orders.append({"order_id": order_id, "error": str(e)})

        return Response(
            {
                "success_orders": success_orders,
                "failed_orders": failed_orders,
            },
            status=status.HTTP_200_OK
        )



class CancelOrderView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        order_ids = request.data.get("order_ids", [])

        if not order_ids:
            return Response({"error": "No order IDs provided"}, status=status.HTTP_400_BAD_REQUEST)

        success_orders = []
        failed_orders = []

        for order_id in order_ids:
            try:
                order = Order.objects.get(id=order_id)
                cancelled_stage = OrderStage.objects.get(name="Cancelled", pipeline=order.current_stage.pipeline)

                order.current_stage = cancelled_stage

                # Update trail
                trail_entry = {
                    "event": "order cancelled",
                    "timestamp": timezone.now().isoformat(),
                    "stage_name": cancelled_stage.name,
                }
                order.trail["events"].append(trail_entry)
                order.is_cancelled = True
                order.save()

                success_orders.append(order_id)

            except Order.DoesNotExist:
                failed_orders.append({"order_id": order_id, "error": "Invalid order ID"})
            except OrderStage.DoesNotExist:
                failed_orders.append({"order_id": order_id, "error": "Cancelled stage does not exist"})
            except OrderStage.MultipleObjectsReturned:
                failed_orders.append({"order_id": order_id, "error": 'Multiple "Cancelled" stages returned'})
            except Exception as e:
                failed_orders.append({"order_id": order_id, "error": str(e)})

        return Response(
            {
                "success_orders": success_orders,
                "failed_orders": failed_orders,
            },
            status=status.HTTP_200_OK
        )



class PaymentRecordView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = PaymentRecordSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RequestPaymentAPIView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        # Get the list of order IDs from the request
        order_ids = request.data.get('order_ids', [])

        if not order_ids or not isinstance(order_ids, list):
            return Response({"error": "Please provide a list of order IDs."}, status=status.HTTP_400_BAD_REQUEST)

        # Track success and failure for each order
        success_orders = []
        failed_orders = []

        for order_id in order_ids:
            try:
                order = Order.objects.get(pk=order_id)
                
                if not order.payment_requested:
                    order.request_payment() 
                    success_orders.append(order_id)
                else:
                    failed_orders.append({
                        "order_id": order_id,
                        "message": "Payment request has already been sent."
                    })
            except Order.DoesNotExist:
                failed_orders.append({
                    "order_id": order_id,
                    "message": "Order not found."
                })

        if not success_orders and not failed_orders:
            return Response({"message": "No valid orders found."}, status=status.HTTP_400_BAD_REQUEST)

        # Prepare response data
        response_data = {
            "success": success_orders,
            "failed": failed_orders,
        }

        return Response(response_data, status=status.HTTP_200_OK)
            

class WhatsAppOrderNotificationLinkView(APIView):
    # permission_classes = [IsAuthenticated]
    # authentication_classes = [CustomUserAuthentication]

    def get(self, request):
        from cart_management.reusables import send_generated_message

        order_id = request.query_params.get("order_id")

        if not order_id:
            return Response(
                {"error": True, "message": "Order Id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            message_link = send_generated_message(order_id=order_id)
            if "error" in message_link:
                return Response(
                    {
                        "error": True,
                        "message": message_link.get("message", "Order does not exist for this order id"),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {"error": False, "message": message_link}, status=status.HTTP_200_OK
            )


class GetFilterOrderList(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    
    pagination_class = CustomPagination
    serializer_class = OrderListSerializer

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
    )

    filterset_fields = ("id", "product_name", "orders__payment_status")
    search_fields = ("product_name", "product_description")

    def get_queryset(self):
        branch_id = self.request.query_params.get("branch_id")
        if not branch_id:
            raise ValidationError({"message": "branch_id missing parameter"})

        if not is_valid_uuid(branch_id):
            raise ValidationError({"message": "enter a valid branch_id"})

        queryset = OrderProduct.objects.filter(
            orders__current_stage__pipeline__branch__id=branch_id
        ).order_by("-created_at")
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        if queryset.exists():
            response_data = {
                "count": paginator.page.paginator.count,
                "next": paginator.get_next_link(),
                "previous": paginator.get_previous_link(),
                "results": serializer.data,
            }
            return Response(response_data, status=status.HTTP_200_OK)

        else:
            response_data = {"count": 0, "next": None, "previous": None, "results": []}
            return Response(response_data, status=status.HTTP_200_OK)


class GetOrderProductTransactionDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_class = OrderFilter
    search_fields = ["buyer__first_name", "buyer__last_name", "order_id"]
    pagination_class = CustomDashboardPagination

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")

        if not branch_id:
            return Response(
                {"message": "branch_id missing parameter"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not is_valid_uuid(branch_id):
            return Response(
                {"message": "enter a valid branch_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        ONLINE_TRANSACTIONS = ["transfer", "card"]
        OFFLINE_TRANSACTIONS = ["cod", "ussd"]

        # Initial queryset
        queryset = Order.objects.filter(current_stage__pipeline__branch__id=branch_id)

        # Apply filters
        for backend in self.filter_backends:
            queryset = backend().filter_queryset(self.request, queryset, self)

        total_transactions = queryset.count()
        
        # Use reverse relationship 'orderproduct_set' to filter by payment_option
        online_transactions = queryset.filter(
            orderproduct__payment_option__in=ONLINE_TRANSACTIONS
        ).count()
        offline_transactions = queryset.filter(
            orderproduct__payment_option__in=OFFLINE_TRANSACTIONS
        ).count()

        # Pagination
        paginator = self.pagination_class()
        paginated_transactions = paginator.paginate_queryset(queryset, request)

        transactions_data = []
        for transaction in paginated_transactions:
            transactions_data.append(
                {
                    "amount": transaction.total_price,
                    "customer": f"{transaction.buyer.first_name} {transaction.buyer.last_name}",
                    "order": transaction.order_id,
                    "transaction_type": (
                        transaction.orderproduct_set.first().payment_option
                        if transaction.orderproduct_set.exists()
                        else None
                    ),
                    "date": transaction.order_date,
                    "status": transaction.payment_status,
                }
            )

        return paginator.get_paginated_response(
            {
                "total_transactions": total_transactions,
                "online_transactions": online_transactions,
                "offline_transactions": offline_transactions,
                "transactions": transactions_data,
            }
        )



class GetOrderCustomersDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def get(self, request, *args, **kwargs):
        branch_id = self.request.query_params.get("branch_id")
        if not branch_id:
            return Response(
                {"message": "branch_id missing parameter"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not is_valid_uuid(branch_id):
            return Response(
                {"message": "enter a valid branch_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        base_query = OrderProduct.objects.filter(
            orders__current_stage__pipeline__branch__id=branch_id
        )

        # Get the unique count of buyers
        total_customers_count = (
            base_query.values("orders__buyer").distinct().count() or 0
        )
        active_customers_count = (
            base_query.filter(orders__buyer__status="active")
            .values("orders__buyer")
            .distinct()
            .count()
            or 0
        )
        inactive_customers_count = (
            base_query.filter(orders__buyer__status="inactive")
            .values("orders__buyer")
            .distinct()
            .count()
            or 0
        )

        return Response(
            {
                "total_customer": total_customers_count,
                "active_customer": active_customers_count,
                "inactive_customer": inactive_customers_count,
            },
            status=status.HTTP_200_OK,
        )


class OrderVerifyTransferAPIView(APIView):

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        order_id = request.query_params.get("order_id")
        order_transaction = Order.objects.filter(
            company=company,
            branch=branch,
            order_id=order_id,
        ).first()
        if order_transaction is None:
            return Response(
                {
                    "status": False,
                    "message": "invalid order transaction",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        payment_received = CoreBankingCallback.objects.filter(
            request_reference=order_transaction.order_id
        ).first()
        if payment_received is None:
            data = {
                "status": False,
                "message": "awaiting payment.",
                "amount": 0.0,
            }
        else:
            data = {
                "status": True,
                "message": "payment received successfully.",
                "amount": float(payment_received.amount),
            }
        return Response(data=data, status=status.HTTP_200_OK)


class IncompleteOrderRecordAPIView(APIView):
    # permission_classes = [IsAuthenticated]
    # authentication_classes = [CustomUserAuthentication]
    parser_classes = [JSONParser]

    def post(self, request):
        serializer = IncompleteOrderRecordSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FetchIncompleteOrderRecordAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]
    parser_classes = [JSONParser]
    def get(self, request):
        """
        Fetch incomplete order records based on the company and optionally branch.
        The `company_id` is mandatory and `branch_id` is optional.
        """
        company_id = request.query_params.get('company_id')
        branch_id = request.query_params.get('branch_id')

        if not company_id:
            return Response({"error": "company_id is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"error": "Company not found."}, status=status.HTTP_404_NOT_FOUND)

        if branch_id:
            try:
                branch = Branch.objects.get(id=branch_id, company=company)
            except Branch.DoesNotExist:
                return Response({"error": "Branch not found for the provided company."}, status=status.HTTP_404_NOT_FOUND)

            try:
                instant_web_store = InstantWeb.objects.get(company=company, branch=branch)
            except InstantWeb.DoesNotExist:
                return Response({"error": "InstantWeb store not found for this branch."}, status=status.HTTP_404_NOT_FOUND)
        else:
            try:
                instant_web_store = InstantWeb.objects.get(company=company, branch__isnull=True)
            except InstantWeb.DoesNotExist:
                return Response({"error": "InstantWeb store not found for this company."}, status=status.HTTP_404_NOT_FOUND)

        records = IncompleteOrderRecord.objects.filter(web_store=instant_web_store)

        if not records.exists():
            return Response({"message": "No incomplete orders found."}, status=status.HTTP_404_NOT_FOUND)

        response_data = []
        current_time = timezone.now()

        for record in records:
            products = record.products if isinstance(record.products, list) else [record.products]
            total_value = sum(product.get('subTotal', 0) for product in products)

            response_data.append({
                "name": record.name,
                "phone": record.phone,
                "created_at": record.created_at,
                "days_in_table": (current_time - record.created_at).days,
                "products": products,
                "total_value": total_value,
                "company": company.company_name,
                "branch": branch.name if branch_id else None,
                "web_store": instant_web_store.store_url if instant_web_store else None,
            })

        return Response({
            "total_records": len(response_data),
            "records": response_data
        }, status=status.HTTP_200_OK)



