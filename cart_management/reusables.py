def send_generated_message(order_id): 
    from cart_management.models import Order
    from urllib import parse
    from django.db.models import F
    from django.core.exceptions import ValidationError

    try:
        order_instance = Order.objects.filter(order_id=order_id).last()
    except ValidationError:
        return {
            "error": True,
            "message": "Invalid order id provided"
        }

    if order_instance is None:
        return {
            "error": True,
            "message": "Invalid order id provided"
        }
    # elif order_instance.vendor_whatsapp_url:
    #     return order_instance.vendor_whatsapp_url
    else:
        order_items_list = list(
            order_instance.orderproduct_set.annotate(
            item_name=F("product__name"), qty=F("quantity"),
            item_price=F("price")
        ).values("item_name", "qty", "item_price")
        )

        order_items_list = [
            f"{index+1}. {items.get('qty')}X {items.get('item_name')} - N{items.get('item_price')}" for index, items in enumerate(order_items_list)
        ]
        order_items_list_text = "\n".join(order_items_list)    

        # last_product = order_instance.products.last()
        company = order_instance.company
        delivery_address = order_instance.buyer.address
        customer_name = (order_instance.buyer.first_name or "") + " " + (order_instance.buyer.last_name or "")
        vendor_phone = company.user.phone_no
        vendor_name = (company.user.first_name or "") + " " + (company.user.last_name or "")
        order_number = order_instance.order_id
        total_amount = order_instance.amount_paid or 0.00

        if vendor_phone is None:
            return {
            "error": True,
            "message": "Vendor should provide a phone number"
        }

        text = (
            f"""Hello {vendor_name},\n\nThis is to confirm my order #{order_number}:\n{order_items_list_text} \n\n*Total:* ₦{total_amount:,} \n*Delivery Address:* {delivery_address}\n\nKindly confirm receipt of this order.\n\nThank you,\n{customer_name}"""
            )

        encoded_message = parse.quote(text.strip())
        whatsapp_url = f"https://wa.me/{vendor_phone}?text={encoded_message}"
        order_instance.vendor_whatsapp_url = whatsapp_url
        order_instance.save()

    return whatsapp_url


