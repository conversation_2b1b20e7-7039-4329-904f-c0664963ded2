import random
import string

from django.conf import settings
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils import timezone
from decouple import config

from core.models import BaseModel
from core.tasks import send_email
from helpers.reusable_functions import send_sms
from instant_web.models import InstantWeb
from sales_app.helper.enums import MeansOfPaymentChoices
from stock_inventory.models import Product
from stock_inventory.models import Company, Branch


def default_trail():
    return {"events": []}


def generate_random_order_id(length=6):
    return "".join(random.choices(string.ascii_uppercase + string.digits, k=length))


# Create your model(s) here.
class Buyer(BaseModel):
    STATUS_CHOICES = [
        ("active", "Active"),
        ("inactive", "Inactive"),
    ]

    first_name = models.CharField(max_length=255, null=True, blank=True)
    middle_name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    last_name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    country = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255, null=True, blank=True)
    state = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField()
    phone_number = models.CharField(max_length=20)
    address = models.CharField(max_length=255, null=True, blank=True)
    postal_code = models.CharField(max_length=10, null=True, blank=True)
    # status = models.CharField(max_length=20, default="active")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="active",
    )
    ship_to_different_address = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.first_name} {self.last_name}"


class Cart(BaseModel):
    buyer = models.OneToOneField(Buyer, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        if self.buyer:
            return f"Cart {self.id} for {self.buyer.first_name}"
        else:
            return f"Cart {self.id} for session {self.session_id}"

    @classmethod
    def get_cart_for_session(cls, session_id):
        return cls.objects.filter(session_id=session_id)


class CartItem(BaseModel):
    cart = models.ForeignKey(Cart, related_name="items", on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()

    def __str__(self):
        return f"{self.product.name} (x{self.quantity})"


class OrderPipeline(BaseModel):
    name = models.CharField(max_length=100)
    is_default = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)

    def __str__(self):
        return self.name


class OrderStage(BaseModel):
    name = models.CharField(max_length=100)
    email_subject = models.CharField(max_length=100, blank=True, null=True)
    email_body = models.TextField(blank=True, null=True)
    email_text = models.TextField(blank=True, null=True)
    email_notification_enabled = models.BooleanField(default=False)
    order_count = models.IntegerField(blank=True, null=True)
    position = models.PositiveIntegerField()
    pipeline = models.ForeignKey(
        OrderPipeline, related_name="stages", on_delete=models.CASCADE
    )

    class Meta:
        ordering = ["position"]

    def __str__(self):
        return self.name


class Order(BaseModel):
    STATUS_CHOICES = [
        ("open", "Open"),
        ("closed", "Closed"),
    ]

    PAYMENT_STATUS_CHOICES = [
        ("paid", "Paid"),
        ("unpaid", "Unpaid"),
    ]

    CHANNELS = [
        ("website", "Website"),
    ]

    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    branch = models.ForeignKey(
        Branch,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    order_id = models.CharField(
        max_length=20,
        unique=True,
        default=generate_random_order_id,
        editable=False,
    )
    amount_paid = models.DecimalField(max_digits=13, decimal_places=2, default=0.0)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="open")
    payment_status = models.CharField(
        max_length=10, choices=PAYMENT_STATUS_CHOICES, default="unpaid"
    )
    channels = models.CharField(max_length=10, choices=CHANNELS, default="website")
    buyer = models.ForeignKey(Buyer, on_delete=models.CASCADE)
    order_date = models.DateField(auto_now_add=True)
    order_time = models.TimeField(auto_now_add=True)
    price = models.DecimalField(max_digits=13, decimal_places=2, default=0.0)
    current_stage = models.ForeignKey(
        "OrderStage",
        related_name="orders",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    trail = models.JSONField(default=dict, blank=True)
    additional_information = models.TextField(blank=True, null=True)
    ship_to_different_address = models.BooleanField(default=False)
    shipping = models.FloatField(default=0.0)
    discount = models.FloatField(default=0.0)
    tax = models.FloatField(default=0.0)
    total_price = models.DecimalField(max_digits=13, decimal_places=2, default=0.0)
    contact = JSONField(blank=True, null=True)
    vendor_whatsapp_url = models.TextField(null=True, blank=True)
    is_delivered = models.BooleanField(default=False)
    is_cancelled = models.BooleanField(default=False)
    payment_requested = models.BooleanField(default=False)
    payment_request_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return self.order_id

    def request_payment(self):
        # Email Logic
        # Define the subject and template directory
        subject = f"Payment Request for Order {self.order_id}"
        template_dir = "payment_request.html"

        # Prepare the dynamic substitutions for the template
        substitute = {
            "buyer_name": f"{self.buyer.first_name} {self.buyer.last_name}",
            "order_id": self.order_id,
            "order_date": self.order_date,
            "total_price": self.total_price,
            "payment_status": self.payment_status,
            "company_name": self.company.company_name,
        }

        # Send the email via your custom send_email function
        send_email(
            recipient=self.buyer.email,
            subject=subject,
            template_dir=template_dir,
            use_template=True,
            buyer_name=f"{self.buyer.first_name} {self.buyer.last_name}",
            order_id=self.order_id,
            order_date=self.order_date,
            total_price=self.total_price,
            payment_status=self.payment_status,
            company_name=self.company.company_name,
        )

        # SMS Logic using WhisperSMS
        # sms_message = f"Dear {self.buyer.first_name}, your order {self.order_id} placed on {self.order_date} for a total of {self.total_price} is still unpaid. Please complete your payment to {self.company.company_name} at your earliest convenience. Thank you!"
        # receiver = self.buyer.phone_number
        # template_id = "7adfe108-84e1-45df-9896-60b871b03a9e"
        # api_key = config("PAYBOX_WHISPER_SMS_API_KEY")
        # print("api_key--", api_key)
        # # Call the send_sms function
        # sms_response = send_sms(receiver, sms_message, template_id, api_key)

        # if sms_response:
        #     print("SMS sent successfully:", sms_response)
        # else:
        #     print("Failed to send SMS")

        # Update Order object to mark that payment has been requested
        self.payment_requested = True
        self.payment_request_date = timezone.now()
        self.save()


class OrderProduct(BaseModel):
    orders = models.ForeignKey(Order, on_delete=models.CASCADE)
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    product_name = models.CharField(max_length=255)
    product_description = models.TextField(blank=True, null=True)
    product_img = models.URLField(max_length=200, blank=True, null=True)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    sub_total = models.DecimalField(max_digits=10, decimal_places=2)
    payment_option = models.CharField(
        max_length=25, choices=MeansOfPaymentChoices.choices
    )

    def __str__(self):
        return self.product_name


class PaymentRecord(BaseModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    mode_of_transaction = models.CharField(
        max_length=25, choices=MeansOfPaymentChoices.choices
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    transaction_date = models.DateField(auto_now_add=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Update the order's payment status
        self.order.payment_status = "paid"
        self.order.save()

    def __str__(self):
        return f"Payment of {self.amount} for Order {self.order.order_id}"


class IncompleteOrderRecord(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True, blank=True)
    web_store = models.ForeignKey(InstantWeb, on_delete=models.CASCADE, null=True, blank=True)
    products = models.JSONField()
    name = models.CharField(max_length=225)
    phone = models.CharField(max_length=13)

    def __str__(self):
        return self.name

    @classmethod
    def create_incomplete_order_record(cls, company, branch, web_store, products, name, phone):
        return cls.objects.create(
            company=company,
            branch=branch,
            web_store=web_store,
            products=products,
            name=name,
            phone=phone,
        )
