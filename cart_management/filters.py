import django_filters

from .models import Order


class OrderFilter(django_filters.FilterSet):
    customer = django_filters.CharFilter(
        field_name="buyer__first_name", lookup_expr="icontains"
    )
    order = django_filters.CharFilter(field_name="order_id", lookup_expr="icontains")
    status = django_filters.CharFilter(
        field_name="payment_status", lookup_expr="icontains"
    )
    transaction_type = django_filters.CharFilter(
        field_name="products__payment_option", lookup_expr="icontains"
    )

    class Meta:
        model = Order
        fields = [
            "customer",
            "order",
            "status",
            "transaction_type",
        ]
