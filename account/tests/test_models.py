import json
from django.test import TestCase
from django.contrib.auth import get_user_model
from account.models import (
    AccountSystem,
    AccountCreationFailure,
    Wallet,
    DebitCreditRecordOnAccount,
    TransactionMetaData,
    CoreBankingCallback,
)
from requisition.models import Company
from stock_inventory.models import Branch

User = get_user_model()


class AccountSystemTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.company = Company.objects.create(
            company_name="Test Company", user=self.user
        )
        self.branch = Branch.objects.create(
            name="Test Branch",
            vat=2,
            created_by=self.user,
            address="abc 123, wall street, yaba",
            company=self.company,
        )
        self.account = AccountSystem.objects.create(
            user=self.user,
            company=self.company,
            branch=self.branch,
            account_provider="VFD",
            account_number="**********",
            account_name="Test Account",
            account_type="SPEND_MGMT",
            bank_name="Test Bank",
            bank_code="123",
            is_test=False,
            is_active=True,
            updated=False,
            available_balance=1000.0,
            payload=json.dumps({"key": "value"}),
        )

    def test_account_creation(self):
        self.assertEqual(self.account.user, self.user)
        self.assertEqual(self.account.company, self.company)
        self.assertEqual(self.account.branch, self.branch)
        self.assertEqual(self.account.account_provider, "VFD")
        self.assertEqual(self.account.account_number, "**********")
        self.assertEqual(self.account.account_name, "Test Account")
        self.assertEqual(self.account.account_type, "SPEND_MGMT")
        self.assertEqual(self.account.bank_name, "Test Bank")
        self.assertEqual(self.account.bank_code, "123")
        self.assertFalse(self.account.is_test)
        self.assertTrue(self.account.is_active)
        self.assertFalse(self.account.updated)
        self.assertEqual(self.account.available_balance, 1000.0)
        self.assertEqual(self.account.payload, json.dumps({"key": "value"}))

    def test_account_match(self):
        self.assertTrue(AccountSystem.account_match(self.account, "SPEND_MGMT"))
        self.assertFalse(AccountSystem.account_match(self.account, "OTHER_TYPE"))
        

class AccountCreationFailureTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.account_failure = AccountCreationFailure.objects.create(
            user=self.user,
            account_type="SPEND_MGMT",
            account_provider="VFD",
            is_test=False,
            payload=json.dumps({"key": "value"}),
            request_payload=json.dumps({"request": "data"}),
        )

    def test_account_creation_failure(self):
        self.assertEqual(self.account_failure.user, self.user)
        self.assertEqual(self.account_failure.account_type, "SPEND_MGMT")
        self.assertEqual(self.account_failure.account_provider, "VFD")
        self.assertFalse(self.account_failure.is_test)
        self.assertEqual(self.account_failure.payload, json.dumps({"key": "value"}))
        self.assertEqual(
            self.account_failure.request_payload, json.dumps({"request": "data"})
        )


class WalletTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.account = AccountSystem.objects.create(
            user=self.user,
            account_provider="VFD",
            account_number="**********",
            account_name="Test Account",
            account_type="SPEND_MGMT",
            bank_name="Test Bank",
            bank_code="123",
            is_test=False,
            is_active=True,
            updated=False,
            available_balance=1000.0,
            payload=json.dumps({"key": "value"}),
        )
        self.wallet = Wallet.objects.create(
            user=self.user,
            account=self.account,
            balance=1000.0,
            previous_balance=1000.0,
            wallet_type="SPEND_MGMT",
            is_active=True,
        )

    def test_wallet_creation(self):
        self.assertEqual(self.wallet.user, self.user)
        self.assertEqual(self.wallet.account, self.account)
        self.assertEqual(self.wallet.balance, 1000.0)
        self.assertEqual(self.wallet.previous_balance, 1000.0)
        self.assertEqual(self.wallet.wallet_type, "SPEND_MGMT")
        self.assertTrue(self.wallet.is_active)

    def test_wallet_str(self):
        self.assertEqual(str(self.wallet), f"wallet-{self.wallet.id}")

    def test_wallet_save(self):
        self.wallet.balance = 2000.0
        self.wallet.save()
        self.assertEqual(self.wallet.previous_balance, 1000.0)

    def test_get_wallet_instance(self):
        wallet_instance = Wallet.get_wallet_instance(self.user, self.account)
        self.assertEqual(wallet_instance, self.wallet)

    def test_get_wallet_balance(self):
        balance = Wallet.get_wallet_balance(self.user, self.account)
        self.assertEqual(balance, 1000.0)


class DebitCreditRecordOnAccountTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.wallet = Wallet.objects.create(
            user=self.user,
            balance=1000.0,
            previous_balance=500.0,
            wallet_type="SPEND_MGMT",
            is_active=True,
        )
        self.debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=self.user,
            entry="CREDIT",
            requisition_type="SPEND_MGMT",
            wallet=self.wallet,
            balance_before=500.0,
            balance_after=1000.0,
            amount=500.0,
            type_of_trans="Test Transaction",
            transaction_instance_id="123456",
        )

    def test_debit_credit_record_creation(self):
        self.assertEqual(self.debit_credit_record.user, self.user)
        self.assertEqual(self.debit_credit_record.entry, "CREDIT")
        self.assertEqual(self.debit_credit_record.requisition_type, "SPEND_MGMT")
        self.assertEqual(self.debit_credit_record.wallet, self.wallet)
        self.assertEqual(self.debit_credit_record.balance_before, 500.0)
        self.assertEqual(self.debit_credit_record.balance_after, 1000.0)
        self.assertEqual(self.debit_credit_record.amount, 500.0)
        self.assertEqual(self.debit_credit_record.type_of_trans, "Test Transaction")
        self.assertEqual(self.debit_credit_record.transaction_instance_id, "123456")

    def test_debit_credit_record_str(self):
        self.assertEqual(str(self.debit_credit_record), "credit - spend_mgmt")


class TransactionMetaDataTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.transaction_meta_data = TransactionMetaData.objects.create(
            user=self.user,
            verification_ref="123456",
            payout_payload=json.dumps({"payout": "data"}),
            fund_wallet_payload=json.dumps({"fund_wallet": "data"}),
            payout_result=json.dumps({"payout_result": "data"}),
            float_verification_result=json.dumps({"float_verification": "data"}),
            internal_verification_result=json.dumps({"internal_verification": "data"}),
            commission_payload=json.dumps({"commission": "data"}),
            commission_result=json.dumps({"commission_result": "data"}),
            commission_ver_result=json.dumps({"commission_ver": "data"}),
            account_enquiry=json.dumps({"account_enquiry": "data"}),
        )

    def test_transaction_meta_data_creation(self):
        self.assertEqual(self.transaction_meta_data.user, self.user)
        self.assertEqual(self.transaction_meta_data.verification_ref, "123456")
        self.assertEqual(
            self.transaction_meta_data.payout_payload, json.dumps({"payout": "data"})
        )
        self.assertEqual(
            self.transaction_meta_data.fund_wallet_payload,
            json.dumps({"fund_wallet": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.payout_result,
            json.dumps({"payout_result": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.float_verification_result,
            json.dumps({"float_verification": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.internal_verification_result,
            json.dumps({"internal_verification": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.commission_payload,
            json.dumps({"commission": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.commission_result,
            json.dumps({"commission_result": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.commission_ver_result,
            json.dumps({"commission_ver": "data"}),
        )
        self.assertEqual(
            self.transaction_meta_data.account_enquiry,
            json.dumps({"account_enquiry": "data"}),
        )


class CoreBankingCallbackTestCase(TestCase):
    def setUp(self):
        self.core_banking_callback = CoreBankingCallback.objects.create(
            one_time=True,
            request_reference="123456",
            company="Test Company",
            sub_company="Test Sub Company",
            sub_company_email="<EMAIL>",
            sub_company_unique_id="**********",
            recipient_account_name="Recipient Name",
            recipient_account_number="**********",
            amount=1000.0,
            fee=10.0,
            amount_payable=990.0,
            reference="REF123456",
            transaction_type="Test Transaction",
            payer_account_name="Payer Name",
            payer_account_number="**********",
            payer_bank_code="123",
            paid_at="2023-01-01",
            narration="Test Narration",
            session_id="SESSION123456",
            transaction_reference="TXN123456",
            settlement_status=True,
            currency="USD",
            payload=json.dumps({"key": "value"}),
        )

    def test_core_banking_callback_creation(self):
        self.assertTrue(self.core_banking_callback.one_time)
        self.assertEqual(self.core_banking_callback.request_reference, "123456")
        self.assertEqual(self.core_banking_callback.company, "Test Company")
        self.assertEqual(self.core_banking_callback.sub_company, "Test Sub Company")
        self.assertEqual(
            self.core_banking_callback.sub_company_email, "<EMAIL>"
        )
        self.assertEqual(self.core_banking_callback.sub_company_unique_id, "**********")
        self.assertEqual(
            self.core_banking_callback.recipient_account_name, "Recipient Name"
        )
        self.assertEqual(
            self.core_banking_callback.recipient_account_number, "**********"
        )
        self.assertEqual(self.core_banking_callback.amount, 1000.0)
        self.assertEqual(self.core_banking_callback.fee, 10.0)
        self.assertEqual(self.core_banking_callback.amount_payable, 990.0)
        self.assertEqual(self.core_banking_callback.reference, "REF123456")
        self.assertEqual(
            self.core_banking_callback.transaction_type, "Test Transaction"
        )
        self.assertEqual(self.core_banking_callback.payer_account_name, "Payer Name")
        self.assertEqual(self.core_banking_callback.payer_account_number, "**********")
        self.assertEqual(self.core_banking_callback.payer_bank_code, "123")
        self.assertEqual(self.core_banking_callback.paid_at, "2023-01-01")
        self.assertEqual(self.core_banking_callback.narration, "Test Narration")
        self.assertEqual(self.core_banking_callback.session_id, "SESSION123456")
        self.assertEqual(self.core_banking_callback.transaction_reference, "TXN123456")
        self.assertTrue(self.core_banking_callback.settlement_status)
        self.assertEqual(self.core_banking_callback.currency, "USD")
        self.assertEqual(
            self.core_banking_callback.payload, json.dumps({"key": "value"})
        )

    def test_core_banking_callback_str(self):
        self.assertEqual(str(self.core_banking_callback), "Test Sub Company")

    def test_register_wema_inflow(self):
        data = {
            "one_time": True,
            "request_reference": "23456",
            "company": "Test Company",
            "sub_company": "Test Sub Company",
            "sub_company_email": "<EMAIL>",
            "sub_company_unique_id": "**********",
            "recipient_account_name": "Recipient Name",
            "recipient_account_number": "**********",
            "amount": 1000.0,
            "fee": 10.0,
            "amount_payable": 990.0,
            "reference": "REF23456",
            "transaction_type": "Test Transaction",
            "payer_account_name": "Payer Name",
            "payer_account_number": "**********",
            "payer_bank_code": "123",
            "paid_at": "2023-01-01",
            "narration": "Test Narration",
            "session_id": "SESSION23456",
            "transaction_reference": "TXN23456",
            "settlement_status": True,
            "currency": "USD",
            "payload": json.dumps({"key": "value"}),
        }
        response = CoreBankingCallback.register_wema_inflow(data)
        self.assertEqual(response, {"message": "event notification received."})
