import json
from uuid import uuid4

from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient, APITestCase

from account.enums import AccountType, DebitCreditEntry
from account.models import AccountSystem, DebitCreditRecordOnAccount, Wallet
from requisition.models import Company

User = get_user_model()


class WalletHistoryAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.company = Company.objects.create(
            company_name="Test Company", user=self.user
        )

        self.account = AccountSystem.objects.create(
            user=self.user,
            company=self.company,
            account_provider="VFD",
            account_number="**********",
            account_name="Test Account",
            account_type=AccountType.PAYROLL,
            bank_name="Test Bank",
            bank_code="0999999",
            is_active=True,
            payload=json.dumps({"key": "value"}),
        )
        # Create a Wallet
        self.wallet = Wallet.objects.create(user=self.user, account=self.account)

        # Create some sample wallet records
        self.record1 = DebitCreditRecordOnAccount.objects.create(
            user=self.user,
            wallet=self.wallet,
            amount=100.0,
            requisition_type=AccountType.PAYROLL,
            entry=DebitCreditEntry.CREDIT,
        )
        self.record2 = DebitCreditRecordOnAccount.objects.create(
            user=self.user,
            wallet=self.wallet,
            amount=50.0,
            requisition_type=AccountType.PAYROLL,
            entry=DebitCreditEntry.DEBIT,
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.url = reverse("wallet-history")

    def test_wallet_history_list(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], True)
        self.assertEqual(len(response.data["results"]), 2)
        self.assertEqual(response.data["count"], 2)

    def test_wallet_history_filtering(self):

        response = self.client.get(self.url, {"entry": "CREDIT"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["entry"], "CREDIT")


class AccountDetailsAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>", password="password"
        )
        self.company = Company.objects.create(
            company_name="Test Company", user=self.user
        )

        self.account = AccountSystem.objects.create(
            user=self.user,
            company=self.company,
            account_provider="VFD",
            account_number="**********",
            account_name="Test Account",
            account_type=AccountType.PAYROLL,
            bank_name="Test Bank",
            bank_code="0999999",
            is_active=True,
            payload=json.dumps({"key": "value"}),
        )
        # Create a Wallet
        self.wallet = Wallet.objects.create(user=self.user, account=self.account)

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.url = reverse("account-details")

    def test_wallet_history_list(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], True)


# class AccountTests(APITestCase):
#     def setUp(self):
#         IPWhitelist.objects.create(ip_address="12.34.523.53", allowed=True)
#         self.client = APIClient()
#         self.user = User.objects.create_user(
#             email="<EMAIL>",
#             password="testpassword123",
#             bvn_number="**********1",
#             is_staff=True
#         )
#         self.client.force_authenticate(user=self.user)
#         self.account = AccountSystem.objects.create(
#             user=self.user, account_number="**********", account_name="Test Account"
#         )

#     def test_send_money(self):
#         url = reverse("send-money")
#         data = {
#             "amount": 100.0,
#             "bank_name": "Test Bank",
#             "bank_code": "123",
#             "account_number": "**********",
#             "account_name": "Test Account",
#             "narration": "Test Narration",
#             "transaction_pin": "1234",
#         }
#         response = self.client.post(url, data, format="json")
#         self.assertEqual(response.status_code, status.HTTP_200_OK)

#     def test_fund_wallet_callback(self):
#         url = reverse("fund-wallet-callback")
#         data = {
#             "benficiary_account_number": "**********",
#             "source_nuban": "**********",
#             "source_account_name": "Source Account",
#             "source_bank_code": "321",
#             "unique_reference": "unique_ref_123",
#             "timestamp": "2023-10-01T00:00:00Z",
#             "narration": "Test Narration",
#             "received_amount": 100.0,
#         }
#         headers = {
#             'HTTP_X_FORWARDED_FOR': '12.34.523.53',
#         }
#         response = self.client.post(url, data, format="json", **headers)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data["message"], "Payment received")

#     def test_core_banking_callbacks(self):
#         url = reverse("core-banking-callbacks")
#         data = {}  # TODO: figure out what data to be sent
#         response = self.client.post(url, data, format="json")
#         self.assertEqual(response.status_code, status.HTTP_200_OK)

#     def test_check_transaction_status_vfd(self):
#         url = reverse("verify-trnx-ref")
#         data = {"request_data": "transaction_id_123"}
#         response = self.client.post(url, data, format="json")
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data["message"], "Success")

#     def test_check_account_details_vfd(self):
#         url = reverse("acct-details")
#         data = {"request_data": "**********"}
#         response = self.client.post(url, data, format="json")
#         print("Check account details", response.content)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data["message"], "Success")

#     def test_create_account_manually(self):
#         url = reverse("create-acct")
#         data = {"bvn_no": "**********1"}
#         response = self.client.post(url, data, format="json")
#         print("Create account manually", response.content)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data["message"], "Account created Successfully")

#     def test_admin_create_account_manually(self):
#         url = reverse("staff-create-acct")
#         data = {"bvn_no": "**********1"}
#         response = self.client.post(url, data, format="json")
#         print("Admin create", response.content)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data["message"], "Account created Successfully")

#     def test_admin_create_specific_account(self):
#         url = reverse("admin-create-specific-acct")
#         data = {"bvn_no": "**********1", "acct_type": "PERSONAL_PAYROLL"}
#         response = self.client.post(url, data, format="json")
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data["message"], "Account created Successfully")

#     def test_admin_update_account(self):
#         url = reverse("update-account")
#         data = {"nuban": "**********", "nin": "**********1", "dob": "1990-01-01"}
#         response = self.client.get(url, data, format="json")
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIn("bvn", response.data)
