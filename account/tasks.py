from datetime import timed<PERSON><PERSON>
import json
import math
import time

from celery import shared_task
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
import requests

from account.enums import (
    AccountType,
    AcctProvider,
    TransactionStatus,
    TransactionType,
    TransferStage,
)
from account.helpers.vfd import VfdBank
from core.helpers.apis.request_cls import LibertyPayPlus
from core.helpers.func import format_date_time_obj_to_year_month_day
from requisition.models import Company


all_corporate_auth_token = settings.LIBERTY_PAY_CORPORATE_TOKEN
base_url = settings.LIBERTY_PAY_PLUS_BASE_URL
environ = settings.ENVIRONMENT
logger = settings.LOGGER


User = get_user_model()


@shared_task
def create_non_corporate_company_wallet(user_id, account_type: str, company_id):
    """
    Celery task to create a non-corporate company wallet.

    This task creates an individual wallet for a non-corporate company by calling
    the `create_vfd_individual_wallet` method from the Wallet model.

    Args:
        user_id (UUID): The UUID of the user for whom the wallet is being created.
        account_type (str): The type of account for the wallet.
        company_id (int): The ID of the company associated with the wallet.

    Returns:
        Wallet: The created Wallet instance.
    """
    from account.models import Wallet

    return Wallet.create_vfd_individual_wallet(
        user_id=user_id, account_type=account_type, company_id=company_id
    )


@shared_task
def vfd_send_money(transaction_id):
    from account.enums import TransferStage
    from account.models import Transaction

    try:
        transaction_instance = Transaction.objects.get(
            id=transaction_id,
            transfer_stage=TransferStage.DEBIT,
            transfer_provider=AcctProvider.VFD,
        )
    except Transaction.DoesNotExist:
        transaction_instance = None

    if transaction_instance is not None:
        if transaction_instance.float_to_internal_ref:
            result = (
                transaction_instance.vfd_initiate_payout_float_to_internal_account()
            )
        elif transaction_instance.float_to_external_ref:
            result = (
                transaction_instance.vfd_initiate_payout_float_to_beneficiary_account()
            )
    else:
        result = transaction_instance
    return result


@shared_task
def vfd_failed_transaction_reversal():
    from account.models import Transaction, Wallet

    two_days_ago = timezone.now() - timedelta(days=2)
    failed_transaction_queryset = Transaction.objects.filter(
        Q(status="FAILED") | Q(status="REVERSED"),
        date_created__gte=two_days_ago,
        transfer_provider=AcctProvider.VFD,
        transaction_type=TransactionType.BANK_TRANSFER,
        is_reversed=False,
    )
    reversal_list = []
    for transaction_instance in failed_transaction_queryset:
        if transaction_instance.transfer_stage == TransferStage.FLOAT_TO_INTERNAL:
            reference = transaction_instance.float_to_internal_ref
        elif transaction_instance.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
            reference = transaction_instance.internal_to_outwards
        elif transaction_instance.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL:
            reference = transaction_instance.float_to_external_ref
        else:
            return None

        reverify_status = transaction_instance.vfd_verify_update_transaction(
            reference=reference
        )
        if isinstance(reverify_status, dict):
            reversal_status = [TransactionStatus.FAILED, TransactionStatus.REVERSED]
            if reverify_status.get("status") in reversal_status:
                if (
                    transaction_instance.transfer_stage
                    == TransferStage.INTERNAL_TO_OUTWARDS
                ):
                    send_funds_to_float = (
                        transaction_instance._vfd_initiate_payout_internal_account_to_float()
                    )
                    if send_funds_to_float is True:

                        reversal_result = Wallet.wallet_reversal(
                            transaction_instance=transaction_instance
                        )
                    else:
                        reversal_result = send_funds_to_float
                else:

                    reversal_result = Wallet.wallet_reversal(
                        transaction_instance=transaction_instance
                    )
                reversal_list.append(reversal_result)
    return reversal_list


@shared_task
def one_off_vfd_failed_transaction_reversal():
    from account.models import Transaction, Wallet

    failed_transaction_queryset = Transaction.objects.filter(
        Q(status="FAILED") | Q(status="REVERSED"),
        transaction_type=TransactionType.BANK_TRANSFER,
        transfer_provider=AcctProvider.VFD,
        is_reversed=False,
    )
    reversal_list = []
    for transaction_instance in failed_transaction_queryset:
        if transaction_instance.transfer_stage == TransferStage.FLOAT_TO_INTERNAL:
            reference = transaction_instance.float_to_internal_ref
        elif transaction_instance.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
            reference = transaction_instance.internal_to_outwards
        elif transaction_instance.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL:
            reference = transaction_instance.float_to_external_ref
        else:
            return None

        reverify_status = transaction_instance.vfd_verify_update_transaction(
            reference=reference
        )
        if isinstance(reverify_status, dict):
            reversal_status = [TransactionStatus.FAILED, TransactionStatus.REVERSED]
            if reverify_status.get("status") in reversal_status:
                if (
                    transaction_instance.transfer_stage
                    == TransferStage.INTERNAL_TO_OUTWARDS
                ):
                    send_funds_to_float = (
                        transaction_instance._vfd_initiate_payout_internal_account_to_float()
                    )
                    if send_funds_to_float is True:

                        reversal_result = Wallet.wallet_reversal(
                            transaction_instance=transaction_instance
                        )
                    else:
                        reversal_result = send_funds_to_float
                else:

                    reversal_result = Wallet.wallet_reversal(
                        transaction_instance=transaction_instance
                    )
                reversal_list.append(reversal_result)
        return reversal_list


@shared_task
def debited_or_pending_bank_transfer_reversal_without_initiating_payout():
    """
    This method helps to reverify transactions that for
    some reasons payout wasn't initiated
    """
    from account.models import Transaction, Wallet

    dabited_transactions = Transaction.objects.filter(
        Q(transfer_stage=TransferStage.DEBIT)
        | Q(
            transfer_stage=TransferStage.FLOAT_TO_EXTERNAL,
        ),
        status=TransactionStatus.PENDING,
        transaction_type=TransactionType.BANK_TRANSFER,
        is_reversed=False,
    )

    reversal_list = []
    for debit_transaction in dabited_transactions:
        if debit_transaction.minutes_ago > 60:
            reverify_status = debit_transaction.vfd_verify_update_transaction()
            if isinstance(reverify_status, dict):
                reversal_status = [TransactionStatus.FAILED, TransactionStatus.REVERSED]
                if reverify_status.get("status") in reversal_status:
                    reversal_result = Wallet.wallet_reversal(
                        transaction_instance=debit_transaction
                    )
                    reversal_list.append(reversal_result)
    return reversal_list


@shared_task
def transfer_commission(transaction_id):
    from account.models import Transaction

    try:
        commission_transaction = Transaction.objects.get(
            id=transaction_id,
            status=TransactionStatus.PENDING,
            transfer_provider=AcctProvider.VFD,
            transaction_type=TransactionType.COMMISSION,
        )
    except Transaction.DoesNotExist:
        commission_transaction = None

    if not commission_transaction:
        return False
    else:
        return commission_transaction.vfd_initiate_payout_float_to_beneficiary_account()


@shared_task
def on_board_corporate_user_task(company_id):
    url = f"{base_url}/agency/onboard_corporate/"
    company_instance = Company.objects.get(pk=company_id)

    incorp_date = format_date_time_obj_to_year_month_day(
        str(company_instance.registration_date)
    )

    payload = json.dumps(
        {
            "user_email": company_instance.user.email,
            "rc_number": company_instance.cac_num,
            "company_name": company_instance.company_name,
            "incorp_date": incorp_date,
        }
    )

    headers = {
        "Authorization": f"token {all_corporate_auth_token}",
        "Content-Type": "application/json",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    status_code = response.status_code

    if status_code == 200:
        res = response.json()
        # {
        #     "status": "success",
        #     "message": "Corporate Account Created",
        #     "data": {
        #         "corporate_id": "22216E"
        #     }
        # }
        company_instance.corporate_id = res.get("data")["corporate_id"]
        company_instance.on_boarded_corporate_user = True
        company_instance.save()
    else:
        logger.error(f"Error {status_code} while onboarding company")

    return "ON BOARDED USER SUCCESSFULLY ON LIBERTY PAY AS CORPORATE USER"


@shared_task
def create_corporate_account_task(company_id):
    from account.models import AccountSystem

    company_instance = Company.objects.get(pk=company_id)
    url = f"{base_url}/accounts/create_corporate_account/"
    user = company_instance.user
    payload = json.dumps(
        {
            "rc_number": company_instance.cac_num,
            "company_name": company_instance.company_name,
            "for_user_email": user.email,
        }
    )
    headers = {
        "Authorization": f"token {all_corporate_auth_token}",
        "Content-Type": "application/json",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    status_code = response.status_code

    if status_code == 200:
        res = response.json()
        # {
        #     "message": {
        #         "status": "success",
        #         "message": "account successfully created",
        #         "account_provider": "VFD",
        #         "user_id": 246,
        #         "data": {
        #             "account_number": "**********",
        #             "account_name": "EMEKA INCOR",
        #             "account_type": "COLLECTION",
        #             "vfd_account_type": "CORPORATE",
        #             "bank_name": "VFD Microfinance Bank",
        #             "bank_code": "999999",
        #             "is_test": true,
        #             "is_active": false,
        #             "timestamp": "2023-06-03T07:19:50.758857Z"
        #         }
        #     }
        # }
        message = res.get("message")
        data = message.get("data")
        if data is not None:
            account = AccountSystem.objects.create(
                user=user,
                account_provider=message.get("account_provider"),
                account_number=data.get("account_number"),
                account_name=data.get("account_name"),
                account_type=data.get("account_type"),
                vfd_account_type=data.get("vfd_account_type"),
                bank_name=data.get("bank_name"),
                bank_code=data.get("bank_code"),
                is_test=data.get("is_test"),
                is_active=data.get("is_active"),
                date_created_on_liberty_pay=data.get("timestamp"),
                payload=res,
            )
            company_instance.account = account
            company_instance.corporate_account_created = True
            company_instance.save()
    else:
        logger.error(f"Error {status_code} while onboarding company")

    return "CREATED CORPORATE ACCOUNT SUCCESSFULLY ON LIBERTY PAY"


@shared_task
def create_corporate_account_wallet_handler(
    company_id, branch_id=None, account_type=None
):
    """
    Celery task to handle the creation or update of a corporate wallet for a company.

    This task initiates the process of creating or updating a corporate wallet for a given company.
    It triggers the `vfd_create_update_company_corporate_wallet` method from the `Wallet` model,
    which manages the actual wallet creation or update.

    Args:
        company_id (int): The ID of the company for which the wallet is to be created or updated.
        branch_id (int, optional): The ID of the branch associated with the corporate wallet, if applicable. Defaults to None.
        account_type (str, optional): The type of account to be created or updated. Defaults to None.

    Returns:
        dict: The result from `vfd_create_update_company_corporate_wallet` method that processes the corporate wallet creation.
    """
    from account.models import Wallet

    # Call the method to create or update the corporate wallet
    return Wallet.vfd_create_update_company_coporate_wallet(
        company_id=company_id, branch_id=branch_id, account_type=account_type
    )


REVERSAL_STATUS_CODE = [
    "03",
    "05",
    "06",
    "07",
    "08",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "25",
    "30",
    "57",
    "58",
    "61",
    "65",
    "68",
    "69",
    "70",
    "71",
    "91",
    "92",
    "94",
    "96",
    "97",
    "98",
    "99",
]


@shared_task
def move_funds_to_float_account(transaction_id: int, account_id: int):
    from core.models import ConstantTable

    """
    The move funds to float moves the money immediately it received
    it is moved to the central account which is the float account

    """
    from account.models import AccountSystem, Transaction, TransactionMetaData

    existing_transaction_to_move_to_float = Transaction.objects.get(id=transaction_id)
    # const = ConstantTable.get_constant_instance()
    # stamp_duty = const.vfd_stamp_duty
    # amount = existing_transaction_to_move_to_float.amount - stamp_duty

    amount = existing_transaction_to_move_to_float.amount

    vfd_bank = VfdBank()

    _account_system = AccountSystem.objects.get(id=account_id)
    _user = existing_transaction_to_move_to_float.user
    float_beneficiary_nuban = settings.FLOAT_ACCOUNT_NUMBER_VFD
    reference = Transaction.create_unique_transaction_ref(
        suffix=_account_system.account_type
    )

    # create Transaction
    _transaction_instance = Transaction.objects.create(
        user=_user,
        company_name=existing_transaction_to_move_to_float.company_name,
        user_full_name=existing_transaction_to_move_to_float.user_full_name,
        user_email=existing_transaction_to_move_to_float.user_email,
        payout_type=existing_transaction_to_move_to_float.payout_type,
        amount=amount,
        # total_amount_charged=provider_fee,
        debit_amount=amount,
        # provider_fee=provider_fee,
        beneficiary_account_number=float_beneficiary_nuban,
        # beneficiary_account_name=account_name,
        bank_name="VFD",
        narration="SEND MONEY TO FLOAT",
        bank_code="999999",
        transfer_provider="VFD",
        source_account_name=_account_system.account_name,
        source_account_number=_account_system.account_number,
        transaction_type="SEND_MONEY_TO_FLOAT",
    )

    payout_payload = {
        "beneficiary_nuban": float_beneficiary_nuban,
        "beneficiary_bank_code": "999999",
        "narration": "SEND MONEY TO FLOAT",
        "amount": amount,
        "transfer_type": "intra",
        "user_bvn": _user.bvn_number,
        "reference": reference,
        "source_account": f"{_account_system.account_number}",
    }

    payout_response = vfd_bank.initiate_payout(**payout_payload)

    send_money_metadata = TransactionMetaData.objects.create(
        user=_user,
        transaction=_transaction_instance,
        payout_payload=payout_payload,
        payout_result=payout_response,
    )
    if payout_response.get("error") == "No Recipient":
        _transaction_instance.status = "FAILED"
        _transaction_instance.save()
        return False

    if environ == "prod":
        verify_float_to_internal_transaction = (
            vfd_bank.vfd_transaction_verification_handler(reference)
        )
    elif environ == "dev":
        verify_float_to_internal_transaction = {
            "status": "00",
            "data": {"transactionStatus": "00"},
        }

    send_money_metadata.verification_ref = reference
    send_money_metadata.float_verification_result = verify_float_to_internal_transaction
    send_money_metadata.save()

    if (
        verify_float_to_internal_transaction.get("status") == "00"
        and verify_float_to_internal_transaction.get("data").get("transactionStatus")
        == "00"
    ):

        _transaction_instance.status = "SUCCESSFUL"
        _transaction_instance.save()

        return True

        # Reversals
    elif (
        verify_float_to_internal_transaction.get("status") == "00"
        and verify_float_to_internal_transaction.get("data").get("transactionStatus")
        in REVERSAL_STATUS_CODE
    ):

        _transaction_instance.status = "REVERSED"
        _transaction_instance.save()
        return False

    # {'status': '108', 'message': 'No Transaction!'}
    elif verify_float_to_internal_transaction.get("status") == "108":
        _transaction_instance.status = "FAILED"
        _transaction_instance.save()
        return False

    # Pending transactions
    elif verify_float_to_internal_transaction.get(
        "status"
    ) == "00" and verify_float_to_internal_transaction.get("data").get(
        "transactionStatus"
    ) in [
        "01",
        "02",
        "09",
        "19",
    ]:

        return False

    # insufficient float balance
    elif (
        verify_float_to_internal_transaction.get("status") == "00"
        and verify_float_to_internal_transaction.get("data").get("transactionStatus")
        == "51"
    ):
        return False


@shared_task
def fund_wallet_update(account_id, transaction_id):
    from account.models import Wallet

    fund_float = move_funds_to_float_account(
        transaction_id=transaction_id, account_id=account_id
    )
    if fund_float:
        Wallet.fund_wallet(
            account_id=account_id,
            transaction_id=transaction_id,
            webhook_notification=True,
        )
    return f"float notification status: {fund_float}"


@shared_task
def verify_pending_float_to_internal():
    from account.models import Transaction, TransactionMetaData

    two_days_ago = timezone.now() - timedelta(days=2)
    _pending_transactions = Transaction.objects.filter(
        transfer_stage="FLOAT_TO_INTERNAL",
        status="PENDING",
        date_created__gte=two_days_ago,
    )

    vfd_bank = VfdBank()
    for transaction in _pending_transactions:

        transaction_ref = transaction.float_to_internal_ref
        if transaction_ref:
            # verify transactions
            verify_float_to_internal_transaction = (
                vfd_bank.vfd_transaction_verification_handler(transaction_ref)
            )

            transaction_metadata = TransactionMetaData.objects.filter(
                transaction=transaction
            ).first()
            transaction_metadata.float_verification_result = (
                verify_float_to_internal_transaction
            )
            transaction_metadata.save()

            if (
                verify_float_to_internal_transaction.get("status") == "00"
                and verify_float_to_internal_transaction.get("data").get(
                    "transactionStatus"
                )
                == "00"
            ):

                transaction.status = "SUCCESSFUL"
                transaction.save()

            # Reversals
            elif (
                verify_float_to_internal_transaction.get("status") == "00"
                and verify_float_to_internal_transaction.get("data").get(
                    "transactionStatus"
                )
                in REVERSAL_STATUS_CODE
            ):

                transaction.status = "REVERSED"
                transaction.save()

            # {'status': '108', 'message': 'No Transaction!'}
            elif verify_float_to_internal_transaction.get("status") == "108":
                transaction.status = "FAILED"
                transaction.save()

            # Pending transactions
            elif verify_float_to_internal_transaction.get(
                "status"
            ) == "00" and verify_float_to_internal_transaction.get("data").get(
                "transactionStatus"
            ) in [
                "01",
                "02",
                "09",
                "19",
            ]:
                continue

                # insufficient float balance
            elif (
                verify_float_to_internal_transaction.get("status") == "00"
                and verify_float_to_internal_transaction.get("data").get(
                    "transactionStatus"
                )
                == "51"
            ):
                continue


@shared_task
def verify_add_fund_by_libertypay(transaction_id):
    from account.models import Transaction

    trans = Transaction.objects.get(id=transaction_id)

    verify_trans = LibertyPayPlus().verify_requisition_transaction(
        transaction_ref=trans.transaction_ref
    )
    if verify_trans.get("status") == "SUCCESSFUL":
        if (
            verify_trans.get("transaction_leg") == "EXTERNAL"
            and verify_trans.get("is_reversed") == False
        ):
            amount = verify_trans.get("amount")

            trans.status = "SUCCESSFUL"
            trans.total_amount_received = amount
            trans.save()

    if verify_trans.get("status") == "PENDING":
        if (
            verify_trans.get("transaction_leg") == "TEMP_EXTERNAL"
            and verify_trans.get("is_reversed") == False
        ):
            trans.status = "PENDING"
            trans.save()

    if verify_trans.get("status") == "FAILED":
        trans.status = "FAILED"
        trans.save()

    # if verify_trans.get("status") == "REVERSED":
    #     trans.status = "REVERSED"
    #     trans.save()

    return "SEND MONEY FROM LIBERTYPAY VERIFICATION"


@shared_task
def create_wema_sales_account(branch_id: str):
    """
    NOTE:
    - it is expected that every single branch of a company is registered as a
        sub-company for 'Paybox360' on the 'Core Banking Service (Wema Bank Plc)'.
    - the registered sub-companies can then utilize the service for sales collections
        via transfer.
    """
    from account.models import Wallet
    from stock_inventory.models import Branch

    branch = Branch.objects.filter(id=branch_id).first()
    if branch is None:
        return "PROVIDE A VALID BRANCH ID TO CREATE SALES ACCOUNT."
    Wallet.create_wema_virtual_account(
        account_type=AccountType.SALES,
        user=branch.company.user,
        company=branch.company,
        branch=branch,
        company_name=f"{branch.company.company_name} - {branch.name}",
        company_email=f"{str(branch.id)[-4:]}+{branch.company.user.email}",
        company_phone=f"{branch.company.user.phone_no[:9]}{str(branch.id)[:2]}",
        unique_id=str(branch.id),
        company_address=branch.address,
    )
    return "SUCCESSFULLY SENT BRANCH DETAILS FOR SALES ACCOUNT CREATION."


@shared_task
def create_wema_sms_account(company_id: str):
    """
    NOTE:
    - it is expected that every single company has an SMS virtual account.
    """
    from account.models import Wallet

    company = Company.objects.filter(id=company_id).first()
    if company is None:
        return "PROVIDE A VALID COMPANY ID TO CREATE SMS ACCOUNT."
    Wallet.create_wema_virtual_account(
        account_type=AccountType.SMS,
        user=company.user,
        company=company,
        company_name=company.company_name,
        company_email=f"{str(company.id)[-4:]}+{company.user.email}",
        company_phone=f"{company.user.phone_no[:9]}{str(company.id)[:2]}",
        unique_id=str(company.id),
    )
    return "SUCCESSFULLY SENT COMPANY DETAILS FOR SMS ACCOUNT CREATION."


@shared_task
def handle_company_wema_inflow(inflow_id: str):
    from account import enums, models
    from helpers.emqx_mqtt import EMQXHandler
    from sales_app.models import ConstantVariable, SalesTransaction
    from sales_app.tasks import update_sales_transaction

    constants = ConstantVariable.objects.first()
    inflow_transaction = models.CoreBankingCallback.objects.filter(id=inflow_id).last()
    if inflow_transaction is None:
        return f"INVALID INFLOW TRANSACTION ID: {inflow_id} WAS SUPPLIED."
    # Check for duplicate fund transaction.
    fund_transaction = models.Transaction.objects.filter(
        bank_deposit_ref=inflow_transaction.reference
    ).last()
    if fund_transaction is not None:
        return f"DUPLICATE FUND TRANSACTION FOR INFLOW ID: {inflow_id}."
    # Fetch necessary details.
    one_time = inflow_transaction.one_time
    request_reference = inflow_transaction.request_reference
    recipient_account_number = inflow_transaction.recipient_account_number
    amount_payable = float(inflow_transaction.amount_payable)

    if one_time and request_reference is not None:
        sales_transaction = SalesTransaction.objects.filter(
            batch_id=request_reference
        ).last()
        if sales_transaction is None:
            return f"NO ASSOCIATED SALES TRANSACTION FOR THE INFLOW WITH THE REFERENCE: {request_reference}."
        else:
            associated_company = sales_transaction.company
            associated_branch = sales_transaction.branch
            account_details = models.AccountSystem.objects.filter(
                company=associated_company,
                branch=associated_branch,
                account_type=enums.AccountType.SALES,
            ).last()
            if account_details is None:
                return "NO ASSOCIATED ACCOUNT TO BE UPDATED WITH THE INFLOW."
    else:
        # Wema account number check.
        account_details = models.AccountSystem.objects.filter(
            account_number=recipient_account_number
        ).last()
        if account_details is None:
            # Fidelity account number check.
            fidelity_details = models.AccountServiceProvider.objects.filter(
                account_number=recipient_account_number
            ).last()
            if fidelity_details is not None:
                account_details = models.AccountSystem.objects.filter(
                    service_provider=fidelity_details
                ).last()
                if account_details is None:
                    return "NO ASSOCIATED ACCOUNT TO BE UPDATED WITH THE INFLOW."
    associated_user = account_details.user
    meta_data_instance = models.TransactionMetaData.objects.create(
        user=associated_user,
        fund_wallet_payload=json.dumps(inflow_transaction.payload),
    )
    deposit_amount = amount_payable
    if account_details.account_type == enums.AccountType.SALES:
        associated_branch = account_details.branch
        sales_charge = float(constants.sales_charge)
        sales_charge_cap = float(constants.sales_charge_cap)
        applicable_charges = math.ceil(amount_payable * sales_charge)
        if applicable_charges > sales_charge_cap:
            applicable_charges = sales_charge_cap
        deposit_amount = amount_payable - applicable_charges
        # commission wallet funding
        commission_wallet = models.AccountSystem.objects.filter(
            account_number=constants.commission_wallet
        ).last()
        commission_instance = models.Transaction.objects.create(
            user=commission_wallet.user,
            company_name=commission_wallet.company.company_name,
            beneficiary_account_number=commission_wallet.account_number,
            beneficiary_account_name=commission_wallet.account_name,
            bank_code=inflow_transaction.payer_bank_code,
            source_account_name=inflow_transaction.payer_account_name,
            source_account_number=inflow_transaction.payer_account_number,
            user_full_name=commission_wallet.user.full_name,
            user_email=commission_wallet.user.email,
            transaction_type=enums.TransactionType.COMMISSION,
            amount=applicable_charges,
            total_amount_received=applicable_charges,
            bank_deposit_ref=inflow_transaction.id,
            status=enums.TransactionStatus.PENDING,
            transfer_provider=enums.AcctProvider.WEMA,
            narration=inflow_transaction.narration,
            payout_type=commission_wallet.account_type,
            company_id=commission_wallet.company.id,
            date_credited=inflow_transaction.paid_at,
        )
        models.Wallet.fund_wallet(
            account_id=commission_wallet.id,
            transaction_id=commission_instance.id,
        )
    transaction_instance = models.Transaction.objects.create(
        user=associated_user,
        company_name=account_details.company.company_name,
        beneficiary_account_number=inflow_transaction.recipient_account_number,
        beneficiary_account_name=inflow_transaction.recipient_account_name,
        bank_code=inflow_transaction.payer_bank_code,
        source_account_name=inflow_transaction.payer_account_name,
        source_account_number=inflow_transaction.payer_account_number,
        user_full_name=associated_user.full_name,
        user_email=associated_user.email,
        transaction_type=enums.TransactionType.DEPOSIT,
        amount=deposit_amount,
        total_amount_received=inflow_transaction.amount,
        bank_deposit_ref=inflow_transaction.reference,
        status=enums.TransactionStatus.PENDING,
        transfer_provider=enums.AcctProvider.WEMA,
        narration=inflow_transaction.narration,
        payout_type=account_details.account_type,
        company_id=account_details.company.id,
        date_credited=inflow_transaction.paid_at,
    )
    meta_data_instance.transaction = transaction_instance
    meta_data_instance.save()
    # Actual wallet funding
    models.Wallet.fund_wallet(
        account_id=account_details.id,
        transaction_id=transaction_instance.id,
    )
    transaction_instance.refresh_from_db()
    # # real-time notification handler.
    # handler = EMQXHandler()
    # handler.connect()
    # message = {
    #     "subject": "MONEY IN",
    #     "body": {
    #         "wallet_name": f"{account_details.account_name}",
    #         "payer_name": f"{inflow_transaction.payer_account_name}",
    #         "date": f"{str(transaction_instance.date_created)}",
    #         "transaction_type": f"{transaction_instance.transaction_type}",
    #         "amount": f"{float(transaction_instance.amount)}",
    #         "balance_before": f"{float(transaction_instance.balance_before)}",
    #         "balance_after": f"{float(transaction_instance.balance_after)}",
    #         "payment_channel": "TRANSFER",
    #         "reference": f"{transaction_instance.bank_deposit_ref}",
    #         "narration": f"{transaction_instance.narration}",
    #         "status": f"{transaction_instance.status}",
    #     },
    # }
    # handler.publish(
    #     topic=f"branch/alert/{associated_branch.id}",
    #     message=json.dumps(message),
    # )
    update_sales_transaction.delay(
        amount=float(inflow_transaction.amount),
        inflow_id=inflow_id,
        batch_id=request_reference,
    )
    return "SUCCESSFULLY PROCESSED WEMA BANK INFLOW TRANSACTION."


@shared_task
def wema_send_money(transaction_id: str):
    from account.enums import TransactionStatus
    from account.models import Transaction
    from account.helpers.core_banking import CoreBankingService
    from sales_app.models import ConstantVariable, WalletWithdrawal

    debit_transaction = Transaction.objects.filter(id=transaction_id).last()
    if debit_transaction is None:
        return f"INVALID DEBIT TRANSACTION ID: {transaction_id}"
    associated_withdrawal = WalletWithdrawal.objects.filter(
        id=debit_transaction.transaction_ref
    ).last()
    paybox_wallet = ConstantVariable.objects.first()
    associated_withdrawal.status = TransactionStatus.IN_PROGRESS
    associated_withdrawal.save()
    wema_handler = CoreBankingService.send_money(
        source_account=paybox_wallet.paybox_wema_nuban,
        account_name=debit_transaction.beneficiary_account_name,
        account_number=debit_transaction.beneficiary_account_number,
        bank_code=debit_transaction.bank_code,
        reference=str(debit_transaction.transaction_ref),
        amount=debit_transaction.amount,
        narration=debit_transaction.narration,
    )
    return str(wema_handler)


@shared_task
@transaction.atomic
def wema_verify_send_money(transaction_ref: str):
    from account.enums import (
        AccountType,
        AcctProvider,
        TransactionStatus,
        TransactionType,
    )
    from account.models import AccountSystem, Transaction, Wallet
    from account.helpers.core_banking import CoreBankingService
    from sales_app.models import WalletWithdrawal

    withdrawal_transaction = Transaction.objects.filter(
        transaction_ref=transaction_ref
    ).last()
    if withdrawal_transaction is None:
        return "NO SALES WITHDRAWAL RECORD FOUND."
    associated_transaction = WalletWithdrawal.objects.filter(id=transaction_ref).last()

    verify_response = CoreBankingService.verify_send_money(
        request_reference=str(transaction_ref)
    )
    initial_status = None
    refund = False
    if verify_response.get("status") and verify_response.get("status_code") == 200:
        transactions_response = (
            verify_response.get("data").get("data").get("transactions")
        )
        if len(transactions_response) >= 1:
            initial_status = transactions_response[-1]
            transaction_status = initial_status.get("transaction_status")
        if initial_status and transaction_status == TransactionStatus.SUCCESSFUL:
            withdrawal_transaction.status = TransactionStatus.SUCCESSFUL
            withdrawal_transaction.save()

            associated_transaction.status = TransactionStatus.SUCCESSFUL
            associated_transaction.save()
        if initial_status and transaction_status == TransactionStatus.FAILED:
            refund = True
        if transactions_response == []:
            refund = True
        if refund:
            withdrawal_transaction.status = TransactionStatus.FAILED
            withdrawal_transaction.save()

            associated_transaction.status = TransactionStatus.FAILED
            associated_transaction.refunded = True
            associated_transaction.save()

            account_details = AccountSystem.objects.filter(
                company=associated_transaction.company,
                branch=associated_transaction.branch,
                account_type=AccountType.SALES,
            ).last()
            if account_details is None:
                return "NO ASSOCIATED BRANCH ACCOUNT TO BE REFUNDED."

            deposit_amount = float(withdrawal_transaction.amount) + float(
                withdrawal_transaction.commission
            )
            transaction_instance = Transaction.objects.create(
                user=account_details.user,
                company_name=account_details.company.company_name,
                beneficiary_account_number=account_details.account_number,
                beneficiary_account_name=account_details.account_name,
                bank_code=account_details.bank_code,
                source_account_name=account_details.account_name,
                source_account_number=account_details.account_number,
                user_full_name=account_details.user.full_name,
                user_email=account_details.user.email,
                transaction_type=TransactionType.REVERSAL,
                amount=deposit_amount,
                total_amount_received=deposit_amount,
                status=TransactionStatus.PENDING,
                transfer_provider=AcctProvider.WEMA,
                narration="Failed sales wallet withdrawal.",
                payout_type=account_details.account_type,
                company_id=account_details.company.id,
            )
            reversal_funding = Wallet.fund_wallet(
                account_id=account_details.id,
                transaction_id=transaction_instance.id,
            )


@shared_task
def notify_liberty_credit_float_deduction(
    amount, account_id=None, transaction_ref=None, reason=None
):
    """
    Celery task to notify Liberty Credit of float account deductions

    Args:
        account_id (str): The float account identifier
        amount (float): Amount deducted from float account
        transaction_ref (str): Unique transaction reference
        reason (str): Reason for the deduction

    Returns:
        dict: Response from Liberty Credit notification
    """
    from account.helpers.liberty_credi import LibertyCrediMgr

    liberty = LibertyCrediMgr()
    notification_result = liberty.notify_float_deduction(
        account_id=account_id,
        amount=amount,
        transaction_ref=transaction_ref,
        reason=reason,
    )

    return notification_result


@shared_task
def run_scheduled_transfer_task():
    from account.models import BulkTransfer

    """ Handle periodic scheduled transfer expected to run 30min or every 3 hours"""
    BulkTransfer.process_scheduled_transfers()
    return "success"
