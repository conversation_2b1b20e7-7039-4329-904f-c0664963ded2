from django.core.management.base import (
    BaseCommand,
    CommandParser,
)

from account.tasks import handle_company_wema_inflow


class Command(BaseCommand):
    help = "MANUAL TOP UP FOR FAILED INFLOW TRANSACTIONS."

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("inflow_id")
        return super().add_arguments(parser)

    def handle(self, *args, **kwargs):
        inflow_id = kwargs["inflow_id"]
        handle_company_wema_inflow.delay(inflow_id)
