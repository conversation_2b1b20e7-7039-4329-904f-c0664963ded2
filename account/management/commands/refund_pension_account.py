
# from django.db import transaction
from django.contrib.auth import get_user_model
from django.core.management import BaseCommand
from account.models import AccountSystem, DebitCreditRecordOnAccount, Transaction, Wallet
from payroll_app.models import SendPensionData
User = get_user_model()


class Command(BaseCommand):
    def handle(self, *args, **options):
        pass
        # with transaction.atomic():
        # sender_account = AccountSystem.objects.get(id="2ec37d7b-80b7-4501-93cd-dcc7f17d87ec") # Liberty Assured Account
        # receiver_account = AccountSystem.objects.get(id="6a3ca941-f7fa-4328-92c9-f5e7f74d4551") # PayBox360 Account
        # sender_wallet_type = "PENSION"
        # sender_wallet_instance = Wallet.objects.filter(
        #     account=sender_account, wallet_type=sender_wallet_type
        # ).first()
        # if sender_wallet_instance is None:
        #     print("Liberty Assured Sender account does not match")

        # user = sender_wallet_instance.user
        # # verify receiver account
        # receiver_wallet_type = "PENSION"

        # receiver_wallet_instance = Wallet.objects.filter(
        #     account=receiver_account, wallet_type=receiver_wallet_type
        # ).first()
        # if receiver_wallet_instance is None:
        #     print("PayBox360 Receiver account does not match")

        # provider_fee = 0.0

        # total_amount_to_debit = 249300.0
        # company_name = (
        #     sender_account.company.company_name if sender_account.company else None
        # )
        # transaction = Transaction.objects.create(
        #     user=user,
        #     user_uuid=user.id,
        #     wallet_id=sender_wallet_instance.id,
        #     wallet_type=sender_wallet_type,
        #     transaction_type="BUDDY",
        #     user_full_name=user.full_name,
        #     user_email=user.email,
        #     payout_type=sender_wallet_type,
        #     amount=total_amount_to_debit,
        #     debit_amount=total_amount_to_debit,
        #     commission=0,
        #     provider_fee=provider_fee,
        #     narration="REFUND FROM Liberty Assured TO PayBox360 PENSION Wallet",
        #     status="PENDING",
        #     company_name=company_name,
        # )

        # # Charge wallet
        # debit_wallet = Wallet.deduct_balance(
        #     user=user,
        #     amount=total_amount_to_debit,
        #     account=sender_account,
        # )

        # if debit_wallet.get("succeeded") is True:

        #     reference = Transaction.create_unique_transaction_ref(suffix=sender_wallet_type)
        #     fund_buddy_reference = Transaction.create_unique_transaction_ref(
        #         suffix=sender_wallet_type
        #     )

        #     wallet_instance = debit_wallet.get("wallet")
        #     balance_after = debit_wallet.get("amount_after")
        #     balance_before = debit_wallet.get("amount_before")
        #     transaction.balance_before = balance_before
        #     transaction.internal_to_internal_ref = reference
        #     transaction.balance_after = balance_after
        #     transaction.fund_internal_buddy_ref = fund_buddy_reference
        #     transaction.transfer_stage = "INTERNAL_TO_INTERNAL"
        #     transaction.beneficiary_wallet_id = receiver_wallet_instance.id
        #     transaction.beneficiary_wallet_type = receiver_wallet_type
        #     transaction.status = "SUCCESSFUL"
        #     transaction.save()

        #     debit_record = DebitCreditRecordOnAccount.objects.create(
        #         user=user,
        #         entry="DEBIT",
        #         requisition_type=sender_wallet_type,
        #         wallet=wallet_instance,
        #         balance_before=balance_before,
        #         amount=total_amount_to_debit,
        #         balance_after=balance_after,
        #         transaction_instance_id=transaction.id,
        #     )

        #     send_money_to_buddy = Wallet.fund_wallet_pay_buddy(
        #         sender_wallet=sender_wallet_instance,
        #         receiver_wallet=receiver_wallet_instance,
        #         amount=total_amount_to_debit,
        #         sender_transaction_ins=transaction,
        #         payout_type=receiver_wallet_type,
        #     )
        #     print("Refund from FROM Liberty Assured TO PayBox360 PENSION SUCCESSFUL")

        #     all_pension_data = [
        #         "d70edfff-1035-453a-ad61-de817531ab59", 
        #         "36b8272b-bfb0-46a8-b73d-56791aec8bc3", 
        #         "e16f7936-fee1-47ea-80ef-14706f68d9df", 
        #         "d9f1c94d-7828-473c-8d0f-543ff310a2bb",
        #         "224aa053-ffbf-49bc-91c7-ac8bec78b286",
        #         "863a737a-57e5-4432-877b-a2b30042d1c5"
        #     ]
        #     for send_pension_id in all_pension_data:
        #         pfa_data = SendPensionData.objects.filter(id=send_pension_id).first()
        #         get_pfa = pfa_data.pfa
        #         total_amount = sum(item["pension_amount"] for item in pfa_data.pension_list)  
        #         send_money = Transaction.vfd_funds_transfer(
        #             bank_code=get_pfa.pfa_bank_code,
        #             bank_name=get_pfa.pfa_bank_name,
        #             account_name=get_pfa.pfa_account_name,
        #             account_number=get_pfa.pfa_account_number,
        #             narration=f"{receiver_account.company.company_name} Pension Schedule",
        #             amount=total_amount,
        #             user=receiver_account.user,
        #             account=receiver_account,
        #             pension_data_id=send_pension_id
        #         )

        #     print("Send PayBox360 PENSION TO PFA's")

        # else:
        #     print("Liberty Assured Pension Wallet Insufficient Balance")