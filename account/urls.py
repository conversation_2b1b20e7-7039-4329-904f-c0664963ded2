from django.urls import path

from account import views

# Register your url pattern(s) here.
send_money = [
    path("send-money/", views.SendMoney.as_view(), name="send-money"),
]

fund_wallet = [
    path(
        "fund-wallet/call-back",
        views.LibertyCrediFundWalletApiView.as_view(),
        name="fund-wallet-callback",
    ),
    path(
        "core_banking_callbacks/",
        views.CoreBankingCallBacks.as_view(),
        name="core-banking-callbacks",
    ),
]

vfd_checks = [
    path(
        "verify_trnx_ref/",
        views.CheckTransactionStatusVfd.as_view(),
        name="verify-trnx-ref",
    ),
    path(
        "acct_details/",
        views.CheckAccountDetailsVfd.as_view(),
        name="acct-details",
    ),
]

accounts = [
    path("update_account/", views.AdminUpdateAcct.as_view(), name="update-account"),
    path(
        "account_details/", views.AccountSystemDetails.as_view(), name="account-details"
    ),
    path("wallet_history/", views.WalletHistory.as_view(), name="wallet-history"),
    path(
        "wallet/transaction/history/",
        views.WalletTransactionHistory.as_view(),
        name="wallet-transaction-history",
    ),
    path(
        "bank-transfer/beneficiary/",
        views.BeneficiaryListAPIView.as_view(),
        name="bank_beneficiary",
    ),
    path(
        "bulk-create/beneficiary/",
        views.BulkCreateBeneficiaryAPIView.as_view(),
        name="bank_beneficiary",
    ),
    path(
        "bulk-transfer/beneficiary/",
        views.BulkTransferAPIView.as_view(),
        name="bank_beneficiary",
    ),
    path(
        "scheduled-transfer-items/",
        views.GetScheduledTransferItemsAPIView.as_view(),
        name="get_scheduled_items",
    ),
]

urlpatterns = [
    *send_money,
    *fund_wallet,
    *vfd_checks,
    *accounts,
]
