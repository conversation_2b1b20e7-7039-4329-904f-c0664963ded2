from datetime import timedelta

from django.conf import settings
from django.contrib import admin
from django.db.models.query import QuerySet
from django.utils import timezone
from import_export.admin import ImportExportModelAdmin

from account import models, resources
from account.enums import (
    AccountType,
    AcctProvider,
    TransactionStatus,
    TransactionType,
    TransferStage,
)
from account.helpers.vfd import VfdBank
from account.tasks import handle_company_wema_inflow
from core.models import ConstantTable


environ = settings.ENVIRONMENT


# Register your model(s) here.
class AccountServiceProviderAdmin(ImportExportModelAdmin):
    resource_class = resources.AccountServiceProviderResource
    search_fields = [
        "company__company_name",
        "company__user__email",
        "account_name",
        "account_number",
    ]
    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountSystemResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AccountSystemResource
    search_fields = ["id", "user__email", "company__company_name", "account_number"]
    list_filter = ["account_type", "is_test", "is_active"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_balance(self, request, queryset: QuerySet[models.AccountSystem]):
        message = "Invalid selection"
        for acct_instance in queryset:
            if acct_instance.account_provider == "VFD":
                acct_number = acct_instance.account_number
                vfdobj = VfdBank()
                request_balance = vfdobj.vfd_account_enquiry(account_number=acct_number)
                if request_balance.get("status") == "00":
                    bal = request_balance.get("data").get("accountBalance")
                    message = f"Balance: {bal}"
                else:
                    message = str(request_balance)

            else:
                message = f"Acct provider: {acct_instance.account_provider}"

        self.message_user(request, str(message))

    def float_reconciliation(self, request, queryset: QuerySet[models.AccountSystem]):

        message = "Invalid selection"
        for acct_instance in queryset:
            if acct_instance.account_provider == "VFD":
                acct_number = acct_instance.account_number
                vfdobj = VfdBank()
                request_balance = vfdobj.vfd_account_enquiry(account_number=acct_number)
                if request_balance.get("status") == "00":
                    const = ConstantTable.get_constant_instance()
                    stamp_duty_base_amount = const.stamp_duty_base_amount
                    vfd_stamp_duty = const.vfd_stamp_duty

                    balance = request_balance.get("data").get("accountBalance")
                    # from core.models import ConstantTable
                    # create transaction
                    float_beneficiary_nuban = settings.FLOAT_ACCOUNT_NUMBER_VFD
                    reference = models.Transaction.create_unique_transaction_ref(
                        suffix=acct_instance.account_type
                    )
                    narration = (
                        f"Manually transfer {balance} to the float account due to previously undeposited amounts.",
                    )
                    if balance > 0:
                        user = acct_instance.user
                        amount = (
                            balance
                            if balance < stamp_duty_base_amount
                            else balance - vfd_stamp_duty
                        )
                        txn_instance = models.Transaction.objects.create(
                            user=user,
                            beneficiary_account_number=float_beneficiary_nuban,
                            bank_code="999999",
                            source_account_name=acct_instance.account_name,
                            source_account_number=acct_instance.account_number,
                            user_full_name=user.full_name,
                            user_email=user.email,
                            transaction_type=TransactionType.FLOAT_RECONCILIATION,
                            amount=amount,
                            debit_amount=amount,
                            status=TransactionStatus.PENDING,
                            transfer_provider=AcctProvider.VFD,
                            narration=narration,
                            payout_type=acct_instance.account_type,
                            bank_name="VFD",
                        )
                        payout_payload = {
                            "beneficiary_nuban": float_beneficiary_nuban,
                            "beneficiary_bank_code": "999999",
                            "narration": narration,
                            "amount": amount,
                            "transfer_type": "intra",
                            "user_bvn": user.bvn_number,
                            "reference": reference,
                            "source_account": f"{acct_instance.account_number}",
                        }
                        payout_response = vfdobj.initiate_payout(**payout_payload)

                        send_money_metadata = models.TransactionMetaData.objects.create(
                            user=user,
                            transaction=txn_instance,
                            payout_payload=payout_payload,
                            payout_result=payout_response,
                        )

                        verification_response = (
                            vfdobj.vfd_transaction_verification_handler(reference)
                        )

                        txn_instance.handle_failed_and_reversed_txn_status(
                            response=payout_response,
                            verification_response=verification_response,
                        )

                        success = txn_instance.handle_vdf_success_txn(
                            verification_response=verification_response
                        )
                        if success:
                            message = narration
                        else:
                            message = f"{payout_response}-----------------{verification_response}"

                    else:
                        message = f"insufficient balance: bal{balance}"
                else:
                    message = f"{request_balance}"
        self.message_user(request, str(message))

    def vfd_create_update_account(
        self, request, queryset: QuerySet[models.AccountSystem]
    ):

        message = "Invalid selection"
        for account_instance in queryset:
            message = models.Wallet.create_vfd_individual_wallet(
                user_id=account_instance.user.id,
                account_type=account_instance.account_type,
                company_id=(
                    account_instance.company.id if account_instance.company else None
                ),
            )
        self.message_user(request, str(message))

    float_reconciliation.short_description = "Float Reconciliation"
    float_reconciliation.allow_tags = True

    get_balance.short_description = "Get Account Balance"
    get_balance.allow_tags = True

    vfd_create_update_account.short_description = "VFD: create update account"
    vfd_create_update_account.allow_tags = True

    actions = [get_balance, vfd_create_update_account]


class DebitCreditRecordOnAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.DebitCreditRecordOnAccountResource
    search_fields = ["user__email", "wallet__account__account_number"]
    list_filter = ["requisition_type", "date_created", "entry"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionResource
    search_fields = [
        "id",
        "user__email",
        "company_name",
        "user_full_name",
        "transaction_ref",
        "float_to_internal_ref",
        "internal_to_outwards_ref",
        "bank_deposit_ref",
    ]

    list_filter = [
        "status",
        "transfer_stage",
        "transaction_type",
        "date_created",
        "payout_type",
    ]
    if environ != "dev":
        readonly_fields = ["amount"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("user")
        # data.remove("amount")
        # data.remove("total_amount_received")
        # data.remove("balance_before")
        # data.remove("balance_after")
        # data.remove("debit_amount")

        # data.insert(6, "total_sum")
        # data.insert(8, "closing_balance")
        # data.insert(9, "opening_balance")
        # data.insert(12, "amount_charged")
        # data.insert(14, "amount_received")

        return data

    def internal_to_float_reversal(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        """
        This method performs reversal of pending transactions to a user's wallet as a result of vfd inconsistent response on the actual reversal that was made which made the user's reference account balance 0.0 and initially returned a bad response. It updates the status of the pending transactions,
        sets the reversal flags, and updates related transaction records and wallet balances accordingly.

        Args:
            request (HttpRequest): The request object.
            queryset (QuerySet[Transaction]): A queryset of Transaction objects representing pending transactions to be reversed.

        Returns:
            None

        Side Effects:
            Updates the status, reversal flags, and related transaction records and wallet balances.

        """
        response = "Invalid selection"
        for pending_reversal_to_float_txn in queryset:
            two_days_ago = timezone.now() - timedelta(days=2)

            if not pending_reversal_to_float_txn.date_created <= two_days_ago:
                response = f"could not resolve txn {pending_reversal_to_float_txn.id},transaction created date is > 2 days ago"
                continue

            if pending_reversal_to_float_txn.reversal_to_float:
                response = f"could not resolve txn {pending_reversal_to_float_txn.id}, due to existing reversal"
                continue

            # Fetching the reversed or failed transaction instance
            reversed_or_failed_txn_instance = models.Transaction.objects.get(
                id=pending_reversal_to_float_txn.reversal_for
            )

            # Fetching the user account instance based on source account number and payout type
            user_acct_instance = models.AccountSystem.objects.filter(
                account_number=pending_reversal_to_float_txn.source_account_number,
                account_type=pending_reversal_to_float_txn.payout_type,
            ).first()

            # Updating status and reversal flags for both transactions
            pending_reversal_to_float_txn.status = "SUCCESSFUL"
            pending_reversal_to_float_txn.reversal_to_float = True
            reversed_or_failed_txn_instance.reversal_to_float = True

            # Saving the pending reversal transaction
            pending_reversal_to_float_txn.save()

            # Performing wallet reversal
            wallet_reversal = models.Wallet.reversal(
                account=user_acct_instance,
                initial_transaction_record=reversed_or_failed_txn_instance,
            )

            # Updating balance fields in the pending reversal transaction
            pending_reversal_to_float_txn.balance_before = wallet_reversal.get(
                "previous_balance"
            )
            pending_reversal_to_float_txn.balance_after = wallet_reversal.get(
                "updated_balance"
            )
            pending_reversal_to_float_txn.is_reversed = True

            # Saving the pending reversal transaction again
            pending_reversal_to_float_txn.save()

            # Constructing response message
            response = f"sum of {pending_reversal_to_float_txn.amount} was reversed to {pending_reversal_to_float_txn.user_email} {pending_reversal_to_float_txn.payout_type} wallet"

        # Sending response message to user
        self.message_user(request, str(response))

    def debit_wallet(self, request, queryset: QuerySet[models.Transaction]):
        message = "Invalid action"
        for txn_instance in queryset:
            bank_deposit_ref = f"charge_{txn_instance.id}"
            if models.Transaction.objects.filter(
                bank_deposit_ref=bank_deposit_ref
            ).exists():
                message = "Debit transaction exist"
                continue

            if txn_instance.status != "SUCCESSFUL":
                message = "Trnsction wasn't successful"
                continue

            account_instance = models.AccountSystem.objects.filter(
                user=txn_instance.user,
                account_number=txn_instance.beneficiary_account_number,
            ).first()

            charge_transaction_record = models.Transaction.objects.create(
                user=txn_instance.user,
                company_name=txn_instance.company_name,
                user_full_name=txn_instance.user_full_name,
                user_uuid=txn_instance.user_uuid,
                user_email=txn_instance.user_email,
                payout_type=txn_instance.payout_type,
                transaction_type="RETRIEVAL",
                bank_deposit_ref=bank_deposit_ref,
                amount=txn_instance.amount,
                debit_amount=txn_instance.debit_amount,
                provider_fee=txn_instance.provider_fee,
                beneficiary_account_number=txn_instance.beneficiary_account_number,
                beneficiary_account_name=txn_instance.beneficiary_account_name,
                bank_name=txn_instance.bank_name,
                narration="Reversal to correct the erroneous credit and ensures the account balance reflects accurate transactions",
                bank_code=txn_instance.bank_code,
                commission=txn_instance.commission,
                source_account_name=txn_instance.source_account_name,
                source_account_number=txn_instance.source_account_number,
                transfer_provider=txn_instance.transfer_provider,
            )
            models.Wallet.charge_wallet(
                transaction_instance=charge_transaction_record, account=account_instance
            )
            message = "Success"
        self.message_user(request, message=message)

    def float_to_internal_verification(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        for txn_instance in queryset:
            _ref = f"{txn_instance.float_to_internal_ref}"
            vfd_obj = VfdBank()
            message = vfd_obj.vfd_transaction_verification_handler(reference=_ref)
        self.message_user(request, message=message)

    def internal_to_external_verification(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        for txn_instance in queryset:
            _ref = f"{txn_instance.internal_to_outwards_ref}"
            vfd_obj = VfdBank()
            message = vfd_obj.vfd_transaction_verification_handler(reference=_ref)

        self.message_user(request, message=str(message))

    def float_to_external_verification(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        for txn_instance in queryset:
            _ref = f"{txn_instance.float_to_external_ref}"
            vfd_obj = VfdBank()
            message = vfd_obj.vfd_transaction_verification_handler(reference=_ref)

        self.message_user(request, message=str(message))

    def verify_update_float_to_internal(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        for txn_instance in queryset:
            if txn_instance.transfer_stage == TransferStage.FLOAT_TO_INTERNAL:
                _ref = f"{txn_instance.float_to_internal_ref}"
                result = txn_instance.vfd_verify_update_transaction(reference=_ref)
                message = result
            else:
                message = f"Invalid Transfer Stage {txn_instance.transfer_stage}"

        self.message_user(request, message=str(message))

    def verify_update_internal_to_external(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        for txn_instance in queryset:
            if txn_instance.transfer_stage == TransferStage.INTERNAL_TO_OUTWARDS:
                _ref = f"{txn_instance.internal_to_outwards_ref}"
                result = txn_instance.vfd_verify_update_transaction(reference=_ref)
                message = result
            else:
                message = f"Invalid Transfer Stage {txn_instance.transfer_stage}"

        self.message_user(request, message=str(message))

    def verify_update_float_to_external(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        for txn_instance in queryset:
            if txn_instance.transfer_stage == TransferStage.FLOAT_TO_EXTERNAL:
                _ref = f"{txn_instance.float_to_external_ref}"
                result = txn_instance.vfd_verify_update_transaction(reference=_ref)
                message = result
            else:
                message = f"Invalid Transfer Stage {txn_instance.transfer_stage}"

        self.message_user(request, message=str(message))

    def initiate_internal_to_external(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        if request.user.email not in ["<EMAIL>"]:
            message = "You do not have permission to perform this action"
            self.message_user(request, message=str(message))
            return

        for txn_instance in queryset:
            user = request.user
            # is_staff = user.is_staff
            admin_email = settings.MANUAL_PAYOUT_USER_EMAIL
            user_email = user.email
            stage = f"{txn_instance.transfer_stage}".replace("_", "")
            if (
                txn_instance.transfer_stage != TransferStage.FLOAT_TO_INTERNAL
                or not txn_instance.float_to_internal
            ):
                message = f"Cannot Transfer {stage}"
                continue
            elif (
                user_email in admin_email
                and txn_instance.transfer_stage == TransferStage.FLOAT_TO_INTERNAL
                and txn_instance.float_to_internal is True
            ):
                message = (
                    txn_instance._vfd_initiate_payout_internal_account_to_external()
                )
            else:
                message = (
                    "You do not have the necessary permission to perform this action"
                )

        self.message_user(request, message=str(message))

    def vfd_reversal_handler(self, request, queryset: QuerySet[models.Transaction]):
        message = "Invalid action"
        if request.user.email not in ["<EMAIL>"]:
            message = "You do not have permission to perform this action"
            self.message_user(request, message=str(message))
            return

        for instance in queryset:
            reversal_result = instance.reverse_vfd_failed_or_reversed_transaction()
            message = reversal_result[1]

        self.message_user(request, message=str(message))

    def reinitiate_pension_transaction(
        self, request, queryset: QuerySet[models.Transaction]
    ):
        message = "Invalid action"
        if request.user.email not in ["<EMAIL>"]:
            message = "You do not have permission to perform this action"
            self.message_user(request, message=str(message))
            return

        for instance in queryset:

            try:
                txn = models.Transaction.objects.get(id=instance.reversal_for)
            except models.Transaction.DoesNotExist:
                message = "Transaction not found."
                continue

            if instance.payout_type != AccountType.PENSION:
                message = f"invalid payout type {instance.payout_type}"
                continue
            if not instance.is_reversed:
                message = "This transaction is not a reversed transaction and cannot be transferred."
                continue

            account = models.AccountSystem.objects.filter(
                account_number=txn.source_account_number
            ).first()

            existing_transaction = models.Transaction.objects.filter(
                source_account_number=txn.source_account_number,
                beneficiary_account_number=txn.beneficiary_account_number,
                status=TransactionStatus.SUCCESSFUL,
                pension_data_id=txn.pension_data_id,
                transaction_type=txn.transaction_type,
            ).first()

            if existing_transaction and existing_transaction.txn_id == txn.id:
                message = "The transfer already exists."
                self.message_user(request, message=str(message))
                return

            bank_code = txn.bank_code
            bank_name = txn.bank_name
            account_name = txn.beneficiary_account_name
            account_number = txn.beneficiary_account_number
            narration = txn.narration
            amount = txn.amount

            transaction_unique_ref = models.Transaction.vfd_funds_transfer(
                bank_code=bank_code,
                bank_name=bank_name,
                account_name=account_name,
                account_number=account_number,
                narration=narration,
                amount=amount,
                account=account,
                user=txn.user,
                charge_fee=None,
                company_owner=None,
                pension_data_id=txn.pension_data_id,
            )

            # Get the new transaction instance using the unique reference
            # new_transaction = models.Transaction.objects.filter(transaction_ref=transaction_unique_ref).first()

            # # new_transaction.pension_data_id = txn.pension_data_id
            # new_transaction.txn_id = txn.id
            # new_transaction.save(update_fields=["pension_data_id", "txn_id"])

            message = transaction_unique_ref

        self.message_user(request, message=str(message))

    def vfd_stamp_duty_refund(self, request, queryset: QuerySet[models.Transaction]):
        message = "Invalid action"
        if request.user.email not in ["<EMAIL>"]:
            message = "You do not have permission to perform this action"
            self.message_user(request, message=str(message))
            return

        for instance in queryset:

            if instance.payout_type != AccountType.PENSION:
                message = f"invalid payout type {instance.payout_type}"
                continue
            if instance.transaction_type != TransactionType.STAMP_DUTY_REFUND:
                message = "This transaction is not a stamp duty refund."
                continue

            message = instance.vfd_initiate_payout_float_to_beneficiary_account()

        self.message_user(request, message=str(message))

    internal_to_float_reversal.short_description = "manual update on pending reversals"
    internal_to_float_reversal.allow_tags = True

    debit_wallet.short_description = "charge wallet"
    debit_wallet.allow_tags = True

    float_to_internal_verification.short_description = (
        "VFD: Float to internal verification request"
    )
    float_to_internal_verification.allow_tags = True

    internal_to_external_verification.short_description = (
        "VFD: Internal to External verification request"
    )
    internal_to_external_verification.allow_tags = True

    float_to_external_verification.short_description = (
        "VFD: Float to External verification request"
    )
    float_to_external_verification.allow_tags = True

    verify_update_float_to_internal.short_description = (
        "VFD: Verify/Update Float to Internal"
    )
    verify_update_float_to_internal.allow_tags = True

    verify_update_internal_to_external.short_description = (
        "VFD: Verify/Update Internal to External"
    )
    verify_update_internal_to_external.allow_tags = True

    verify_update_float_to_external.short_description = (
        "VFD: Verify/Update Float to External"
    )
    verify_update_float_to_external.allow_tags = True

    initiate_internal_to_external.short_description = (
        "VFD: Initiate Internal to External"
    )
    initiate_internal_to_external.allow_tags = True

    vfd_reversal_handler.short_description = "VFD: One off reversal"
    vfd_reversal_handler.allow_tags = True

    reinitiate_pension_transaction.short_description = (
        "VFD: Reinitiate pension transaction"
    )
    reinitiate_pension_transaction.allow_tags = True

    # vfd_stamp_duty_refund.short_description = "VFD: Refund stamp duty"
    # vfd_stamp_duty_refund.allow_tags = True

    actions = [
        debit_wallet,
        internal_to_external_verification,
        float_to_internal_verification,
        float_to_external_verification,
        verify_update_float_to_internal,
        verify_update_internal_to_external,
        verify_update_float_to_external,
        initiate_internal_to_external,
        vfd_reversal_handler,
        reinitiate_pension_transaction,
        # vfd_stamp_duty_refund,
    ]


class WalletResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.WalletResource
    search_fields = ["id", "account__id", "account__account_number", "user__email"]
    list_filter = ["wallet_type", "is_active"]

    if environ == "prod":
        readonly_fields = ["balance", "previous_balance"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AccountCreationFailureResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.AccountCreationFailureResource
    search_fields = ["user__email"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionMetaDataResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.TransactionMetaDataResource
    search_fields = ["user__email"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CoreBankingCallbackAdmin(ImportExportModelAdmin):
    resource_class = resources.CoreBankingCallbackResource
    search_fields = [
        "recipient_account_number",
        "request_reference",
        "sub_company_unique_id",
        "payer_account_number",
        "session_id",
    ]
    list_filter = [
        "one_time",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    @admin.action(description="RESOLVE SALES INFLOWS: resolve pending inflow.")
    def resolve_corebanking_inflow(
        self,
        request,
        queryset: QuerySet[models.CoreBankingCallback],
    ):
        for obj in queryset:
            handle_company_wema_inflow(obj.id)

    actions = [
        resolve_corebanking_inflow,
    ]


class BeneficiaryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BeneficiaryResource
    search_fields = [
        "account_number",
        "user__email",
    ]
    # list_filter = [

    # ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BulkTransferResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BulkTransferResource
    search_fields = ["user__email", "reference_id"]
    list_filter = ["status", "charge_wallet", "is_active", "is_scheduled"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def run_scheduled_transfer(self, request, queryset: QuerySet[models.BulkTransfer]):
        message = "Invalid action"
        if request.user.email not in ["<EMAIL>", "<EMAIL>"]:
            message = "You do not have permission to perform this action"
            self.message_user(request, message=str(message))
            return

        for instance in queryset:
            models.BulkTransfer.process_scheduled_transfers(transfer_id=instance.id)
            message = "success"

        self.message_user(request, message=str(message))

    run_scheduled_transfer.short_description = "Run Scheduled Transfer"
    run_scheduled_transfer.allow_tags = True

    actions = [
        run_scheduled_transfer,
    ]


class BulkTransferItemResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BulkTransferItemResource
    # search_fields = [
    #     "account_number",
    #     "user__email",
    # ]
    # list_filter = [

    # ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BulkTransferItemTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BulkTransferItemTransactionResource
    # search_fields = [
    #     "account_number",
    #     "user__email",
    # ]
    # list_filter = [

    # ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields] + [
            "transaction_status"
        ]


class ScheduledTransferLogResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ScheduledTransferLogResource
    search_fields = [
        "bulk_transfer__reference_id",
        "bulk_transfer__user__email",
    ]
    list_filter = ["status"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.AccountServiceProvider, AccountServiceProviderAdmin)
admin.site.register(models.AccountSystem, AccountSystemResourceAdmin)
admin.site.register(
    models.DebitCreditRecordOnAccount, DebitCreditRecordOnAccountResourceAdmin
)
admin.site.register(models.Transaction, TransactionResourceAdmin)
admin.site.register(models.Wallet, WalletResourceAdmin)
admin.site.register(models.AccountCreationFailure, AccountCreationFailureResourceAdmin)
admin.site.register(models.TransactionMetaData, TransactionMetaDataResourceAdmin)
admin.site.register(models.CoreBankingCallback, CoreBankingCallbackAdmin)
admin.site.register(models.Beneficiary, BeneficiaryResourceAdmin)
admin.site.register(models.BulkTransfer, BulkTransferResourceAdmin)
admin.site.register(models.BulkTransferItem, BulkTransferItemResourceAdmin)
admin.site.register(models.ScheduledTransferLog, ScheduledTransferLogResourceAdmin)
admin.site.register(
    models.BulkTransferItemTransaction, BulkTransferItemTransactionResourceAdmin
)
