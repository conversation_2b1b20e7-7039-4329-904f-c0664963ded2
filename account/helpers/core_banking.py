from datetime import <PERSON><PERSON><PERSON>
import json
from typing import Optional

from decouple import config
from django.conf import settings
import redis
from requests import exceptions, request


core_banking_db = redis.StrictRedis(
    host="localhost",
    port=6379,
    decode_responses=True,
    encoding="utf-8",
)


if settings.ENVIRONMENT == "prod":
    BASE_URL = "https://banking.libertypayng.com"
else:
    BASE_URL = "https://dev.banking.libertypayng.com"


# Core Banking utility manager.
class CoreBankingService:
    headers = {"Content-Type": "application/json"}

    @classmethod
    def request_handler(cls, request_type: str, params: dict):
        """ """
        try:
            response = request(request_type, **params)
            try:
                data = response.json()
            except json.JSONDecodeError:
                data = response.text
            return {
                "status_code": response.status_code,
                "status": True,
                "data": data,
                "error": None,
            }
        except exceptions.RequestException as error:
            return {
                "status_code": 500,
                "status": False,
                "data": None,
                "error": str(error),
            }

    @classmethod
    def authenticate(cls):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/companies/auth/login/"
        payload = json.dumps(
            {
                "email": config("CORE_BANKING_USER"),
                "password": config("CORE_BANKING_PASS"),
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response

    @classmethod
    def login(cls):
        """ """
        bearer_token = core_banking_db.get("CORE_BANKING_TOKEN")
        if bearer_token is None:
            authenticator = cls.authenticate()
            if authenticator.get("status"):
                response = authenticator.get("data")
                bearer_token = response.get("data").get("access")
                core_banking_db.set(
                    "CORE_BANKING_TOKEN",
                    bearer_token,
                    ex=timedelta(
                        seconds=86400,
                    ),
                )
                return bearer_token
            return None
        return bearer_token

    @classmethod
    def create_virtual_account(
        cls,
        first_name: str,
        last_name: str,
        email: str,
        phone_number: Optional[str] = None,
    ):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/wema/virtual_accounts/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "first_name": first_name,
                "last_name": last_name,
                "email": email,
                "phone": phone_number,
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response

    @classmethod
    def register_sub_company(
        cls,
        company_name: str,
        company_email: str,
        company_phone: str,
        unique_id: str,
        company_address: Optional[str] = None,
    ):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/companies/sub_companies/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "company_name": company_name,
                "company_email": company_email,
                "company_phone": company_phone,
                "unique_id": unique_id,
                "company_address": company_address,
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response

    @classmethod
    def get_one_time_account(
        cls,
        request_reference: str,
        sub_company: str = None,
    ):
        """ """
        absolute_url = f"{BASE_URL}/api/v1/core/one_time_account/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "request_reference": request_reference,
                "provider": "FIDELITY",
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response
    
    @classmethod
    def send_money(
        cls,
        source_account: str,
        account_name: str,
        account_number: str,
        bank_code: str,
        reference: str,
        amount: int,
        narration: str,
    ):
        """ """
        absolute_url = f"{BASE_URL}/accounts/transfer_money/"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps(
            {
                "source_account": source_account,
                "mode": "LIVE",
                "account_name": account_name,
                "account_number": account_number,
                "bank_code": bank_code,
                "request_reference": reference,
                "amount": amount,
                "narration": narration,
            }
        )
        response = cls.request_handler(
            "POST",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            ),
        )
        return response

    @classmethod
    def verify_send_money(cls, request_reference: str):
        """ """
        absolute_url = f"{BASE_URL}/accounts/verify_transfer?search={request_reference}"
        bearer_token = cls.login()
        if bearer_token is None:
            return "NO BEARER TOKEN"
        cls.headers["Authorization"] = f"Bearer {bearer_token}"
        payload = json.dumps({})
        response = cls.request_handler(
            "GET",
            dict(
                url=absolute_url,
                headers=cls.headers,
                data=payload,
            )
        )
        return response
