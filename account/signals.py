from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.dispatch import receiver
from account.models import Transaction

from payroll_app.models import SendPensionData

User = get_user_model()

@receiver(post_save, sender=Transaction)
def signal_company_employee_list_model(sender, instance, created, **kwargs):

    """This function updates the pension transaction and automatically
        send receipts and schedule to the appropriate Pension Fund Administrator
    """
    if created:
        pass
    else:
        if instance.payout_type == "PENSION":
            if instance.status == "SUCCESSFUL":
                send_pension_data = SendPensionData.update_send_pension_data(transaction_ins=instance)