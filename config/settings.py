from datetime import timed<PERSON><PERSON>
import logging
import os
from pathlib import Path
from datetime import datetime, timedelta


from decouple import Csv, config
from django.core.management.utils import get_random_secret_key
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration


LOGGER = logging.getLogger("__name__")

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY", default=get_random_secret_key(), cast=str)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", default=False, cast=bool)

ALLOWED_HOSTS = config("ALLOWED_HOSTS", default="IP", cast=Csv())

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Local.
    "core",
    "clock_app",
    "account",
    "leave_management",
    "requisition",
    "payroll_app",
    "stock_inventory",
    "sales_app",
    "finance_system",
    "invoicing",
    "cart_management",
    "instant_web",
    "performance_sales_metrics_dashboard",
    "linkshortener",
    "subscription_and_invoicing",
    "performance_management",
    "accounting",
    # Third parties.
    "corsheaders",
    "django_celery_beat",
    "django_celery_results",
    "django_filters",
    "drf_yasg",
    "import_export",
    "rest_framework",
    "storages",
    "dashboard",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "subscription_and_invoicing.middleware.SubscriptionAccessMiddleware",
]
ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# CORS
CORS_ALLOW_ALL_ORIGINS = True

WSGI_APPLICATION = "config.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("DATABASE_NAME"),
        "USER": config("DATABASE_USER"),
        "PASSWORD": config("DATABASE_PASSWORD"),
        "HOST": config("DATABASE_HOST"),
        "PORT": config("DATABASE_PORT"),
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Lagos"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# MEDIA_URL = '/media/'
# MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Extra lookup directories for collectstatic to find static files
STATICFILES_DIRS = []

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Django User Config
AUTH_USER_MODEL = "core.User"

# response
TOKEN_ERROR_RESPONSE = {
    "detail": "Given token not valid for any token type",
    "code": "token_not_valid",
    "messages": [
        {
            "token_class": "AccessToken",
            "token_type": "access",
            "message": "Token is invalid or expired",
        }
    ],
}

BITLY_ACCESS_TOKEN = config("BITLY_ACCESS_TOKEN")
BREVO_API_KEY = config("BREVO_API_KEY")

# CELERY
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_BROKER_URL = "redis://127.0.0.1:6379"
CELERY_RESULT_BACKEND = "django-db"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE

# .ENV
ENVIRONMENT = config("ENVIRONMENT")

if ENVIRONMENT == "prod":
    VFD_ACCESS_TOKEN = config("VFD_ACCESS_TOKEN_LIVE")
    sentry_sdk.init(
        dsn=config("SENTRY_DSN"),
        integrations=[DjangoIntegration()],
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=0.001,
        # To set a uniform sample rate
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production,
        profiles_sample_rate=1.0,
    )

else:
    VFD_ACCESS_TOKEN = config("VFD_ACCESS_TOKEN_TEST")

AGENCY_BANKING_BASE_URL = config("AGENCY_BANKING_BASE_URL")
AGENCY_SECRETE_KEY = config("AGENCY_SECRETE_KEY")

LIBERTY_PAY_PLUS_DEV_BASE_URL = config("LIBERTY_PAY_PLUS_DEV_BASE_URL")

if ENVIRONMENT == "prod":
    LIBERTY_PAY_PLUS_BASE_URL = config("LIBERTY_PAY_PLUS_PROD_BASE_URL")

else:
    LIBERTY_PAY_PLUS_BASE_URL = config("LIBERTY_PAY_PLUS_DEV_BASE_URL")


LIBERTY_PAY_CORPORATE_TOKEN = config("LIBERTY_PAY_CORPORATE_TOKEN")

# s3bucket
AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = "requisition-space"
AWS_DEFAULT_ACL = "public-read"
AWS_LOCATION = "prod_docs" if ENVIRONMENT == "prod" else "dev_docs"
AWS_S3_ENDPOINT_URL = "https://nyc3.digitaloceanspaces.com"
AWS_S3_OBJECT_PARAMETERS = {"CacheControl": "max-age=86400"}
# public media settings
DEFAULT_FILE_STORAGE = "config.storage_backends.MediaStorage"
PUBLIC_MEDIA_LOCATION = "media"
MEDIA_URL = f"https://{AWS_S3_ENDPOINT_URL}/{PUBLIC_MEDIA_LOCATION}/"

BASE_URL = config("BASE_URL")

COMMISSION_ACCOUNT_NUMBER_VFD = config("COMMISSION_ACCOUNT_NUMBER_VFD")

FLOAT_ACCOUNT_NUMBER_VFD = config("FLOAT_ACCOUNT_NUMBER_VFD")

GOOGLE_API_KEY = config("GOOGLE_API_KEY", default=get_random_secret_key())

# instant wage account details
INSTANT_WAGE_ACCOUNT_ID = config("INSTANT_WAGE_ACCOUNT_ID")
INSTANT_WAGE_USER_ID = config("INSTANT_WAGE_USER_ID")

MAILGUN_APIKEY = config("MAILGUN_APIKEY")
MAILGUN_URL = config("MAILGUN_URL")
MERCHANT_TRANSACTION_TARGET_PER_MONTH = 300
MONO_WEBHOOK_SECRET = config("MONO_WEBHOOK_SECRET")

OPENAI_API_KEY = config("OPENAI_API_KEY")
# OTP Manager Secret
OTP_SECRET = config("OTP_SECRET")

PAYBOX_PAYMENT_LINK = config("PAYBOX_PAYMENT_LINK")
PAYBOX_WHISPER_SMS_API_KEY = config("PAYBOX_WHISPER_SMS_API_KEY")
PAYBOX_WHISPER_SMS_TOKEN = config("PAYBOX_WHISPER_SMS_TOKEN")
PAYSTACK_SECRET_KEY = config("PAYSTACK_SECRET_KEY")
PHONENUMBER_DEFAULT_REGION = "NG"

REMINDER_TEMPLATE = config("REMINDER_TEMPLATE")
REQUISITION_DISBURSEMENT_TEMPLATE = config("REQUISITION_DISBURSEMENT_TEMPLATE")
REQUISITION_INSUFFICIENT_PURSE_BAL = config("REQUISITION_INSUFFICIENT_PURSE_BAL")

SECRET_WEB_TOKEN = config("SECRET_WEB_TOKEN")

WHISPER_KEY = config("WHISPER_KEY")
WHISPER_TRANSACTIONAL_URL = config("WHISPER_TRANSACTIONAL_URL")

VFD_ACCESS_TOKEN_LIVE = config("VFD_ACCESS_TOKEN_LIVE")
VFD_ACCESS_TOKEN_TEST = config("VFD_ACCESS_TOKEN_TEST")
VFD_WALLET_CREDENTIALS_LIVE = config("VFD_WALLET_CREDENTIALS_LIVE")
VFD_WALLET_CREDENTIALS_TEST = config("VFD_WALLET_CREDENTIALS_TEST")

YOU_VERIFY_COMPANY_TOKEN = config("YOU_VERIFY_COMPANY_TOKEN")
MANUAL_PAYOUT_USER_EMAIL = config("MANUAL_PAYOUT_USER_EMAIL")

# Authentication
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "COERCE_DECIMAL_TO_STRING": False,
}

SIMPLE_JWT = {
    # 'ACCESS_TOKEN_LIFETIME': timedelta(seconds=10),
    # 'ACCESS_TOKEN_LIFETIME': timedelta(minutes=50),
    "ACCESS_TOKEN_LIFETIME": (
        timedelta(days=5) if ENVIRONMENT == "prod" else timedelta(days=100)
    ),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=5),
}

# Email settings
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "mail.libertyng.com"
EMAIL_PORT = 587  # Use EMAIL_PORT 465 for SSL
EMAIL_USE_TLS = True

SUPPLIER_INVITE_FRONTEND_URL = config(
    "SUPPLIER_INVITE_FRONTEND_URL", "https://liberty-paybox-suppliers.vercel.app"
)
SUPPLIER_FRONTEND_LOGIN_URL = ""

# Chat GPT API
CHAT_GPT_API = config("CHAT_GPT_API")

SEEDS_PENNIES_SMS_WHISPER_API_KEY = config("SEEDS_PENNIES_SMS_WHISPER_API_KEY")

# CACHING
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
    }
}

DEFAULT_ACCESS_DAYS = 14

SUBSCRIPTION_FREE_TRIAL = 14


LOGS_DIR = os.path.join(BASE_DIR, "logs")  # Define the logs directory
os.makedirs(LOGS_DIR, exist_ok=True)  # Ensure the logs directory exists

LOG_FILE = os.path.join(
    LOGS_DIR, f"errors_{datetime.now().strftime('%Y-%m-%d')}.log"
)  # Log file with today's date

LOGGING = {
    "version": 1,  # Version of the logging configuration
    "disable_existing_loggers": False,  # Keep existing loggers active
    "formatters": {  # Define log message formats
        "verbose": {  # Detailed format including timestamp, module, and message
            "format": "{levelname} {asctime} {module} {message}",
            "style": "{",
        },
        "simple": {  # Simple format with only log level and message
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {  # Define how logs are handled
        "console": {  # Logs to the console (terminal)
            "level": "DEBUG",  # Capture logs at DEBUG level and above
            "class": "logging.StreamHandler",  # Outputs logs to the console
            "formatter": "simple",  # Uses the simple formatter
        },
        "file": {  # Logs errors to a file
            "level": "ERROR",  # Capture only ERROR level logs and above
            "class": "logging.FileHandler",  # Writes logs to a file
            "filename": LOG_FILE,  # File path for the log file (with date)
            "formatter": "verbose",  # Uses the verbose formatter
        },
    },
    "loggers": {  # Define loggers for specific parts of Django
        "django": {  # Django’s main logger
            "handlers": ["console", "file"],  # Send logs to console and file
            "level": "INFO",  # Capture INFO and above (so runserver logs show)
            "propagate": True,  # Allow logs to propagate to the root logger
        },
        "django.utils.autoreload": {  # Disable autoreload logs
            "handlers": [],  # No handlers, so logs are ignored
            "level": "CRITICAL",  # Capture only CRITICAL logs
            "propagate": False,  # Prevent propagation
        },
    },
    "root": {  # Root logger (handles all unhandled logs)
        "handlers": ["console", "file"],  # Logs to console and file
        "level": "DEBUG",  # Capture everything at DEBUG level and above
    },
}
