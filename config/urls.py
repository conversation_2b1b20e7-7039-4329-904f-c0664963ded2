from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.permissions import AllowAny

# Swagger-UI.
schema_view = get_schema_view(
    openapi.Info(
        title="Liberty Tech X - Paybox360",
        default_version="v1",
        description="API Master",
        terms_of_service="https://www.libertyassured.com/whisper-terms-conditions-1-1",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[],
)

admin.site.site_header = "Paybox360"
urlpatterns = [
    path("admin/", admin.site.urls),
    path("clock-app/", include("clock_app.urls")),
    path("leave-management/", include("leave_management.urls")),
    path(
        "docs/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("req/", include("requisition.urls")),
    path("payroll/", include("payroll_app.urls")),
    path("core/", include("core.urls")),
    path("orders/", include("cart_management.urls")),
    path("instant_web/", include("instant_web.urls")),
    path("psmd/", include("performance_sales_metrics_dashboard.urls")),
    path("api/v1/stock/", include("stock_inventory.urls")),
    path("acct/", include("account.urls")),
    path("api/v1/sales/", include("sales_app.urls")),
    path("api/v1/finance/", include("finance_system.urls")),
    path("api/v1/invoicing/", include("invoicing.urls")),
    path("api/v1/accounting/", include("accounting.urls")),
    path("subscription_and_invoicing/", include("subscription_and_invoicing.urls")),
    path("lk/", include("linkshortener.urls")),
    path("management/", include("performance_management.urls")),
    path("dashboard/", include("dashboard.urls")),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
