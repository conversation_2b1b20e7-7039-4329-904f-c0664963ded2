from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.dispatch import receiver

from payroll_app.models import CompanyEmployeeList
from requisition.models import TeamMember
from subscription_and_invoicing.task import paystack_payment_confirmation
from core.models import  PaystackPayment

from .tasks import send_new_user_onboarding_email, update_kyc_details

User = get_user_model()


@receiver(post_save, sender=User)
def update_user_team(sender, instance, created, **kwargs):
    if created:

        update_kyc_details.delay(user_id=instance.id)

        team_member = TeamMember.member_exists(email=instance.email)
        # print(team_member)
        if team_member is not None:
            # print(team_member, "team member is not none")
            team_membership = team_member.filter(is_registered=False)
            for membership in team_membership:
                membership.is_registered = True
                membership.status = "ACTIVE"
                membership.member_id = instance.id
                membership.save()

        payroll_user = CompanyEmployeeList.employee_exist(email=instance.email)
        if payroll_user is not None:
            payroll_members = payroll_user.filter(
                is_active=False, is_deleted=False, is_suspended=False
            )
            for members in payroll_members:
                if members.is_invited is False:
                    members.is_active = True
                    members.employee = instance
                    members.employee_status = "ACTIVE"
                    members.save()
        
        send_new_user_onboarding_email.delay(user_id=instance.id)



@receiver(post_save, sender= PaystackPayment)
def update_invoice_payment_status(sender, instance, created, **kwargs):
    if created:
        paystack_payment_confirmation.delay(str(instance.id))

