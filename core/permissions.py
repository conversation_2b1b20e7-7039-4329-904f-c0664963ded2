from rest_framework import permissions, status
from rest_framework.exceptions import PermissionDenied, APIException

from core.models import IPWhitelist, BlackListEntry


class IpWhiteListPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        # Get the client's IP address
        client_ip = self.get_client_ip(request)
        # print(client_ip, "\n\n")
        try:
            IPWhitelist.objects.get(ip_address=client_ip, allowed=True)
        except IPWhitelist.DoesNotExist:
            try:
                IPWhitelist.objects.get(ip_address=client_ip, allowed=False)
                raise PermissionDenied()
            except IPWhitelist.DoesNotExist:
                IPWhitelist.objects.create(ip_address=client_ip, allowed=False)
                raise PermissionDenied()

        return True

    @staticmethod
    def get_client_ip(request):
        """
        Retrieve the client's IP address from the request, handling multiple scenarios.
        """
        # Check for the 'X-Forwarded-For' header (used by proxies/load balancers)
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            # Extract the first IP address in the chain
            ip = x_forwarded_for.split(",")[0].strip()
            return ip

        # Check for the 'X-Real-IP' header (used by some proxies, e.g., Nginx)
        ip = request.META.get("HTTP_X_REAL_IP")
        if ip:
            return ip

        # # Check for other headers that might contain the IP
        # ip = (
        #     request.META.get("HTTP_CF_CONNECTING_IP")  # Cloudflare
        #     or request.META.get("HTTP_TRUE_CLIENT_IP")  # Akamai
        #     or request.META.get("HTTP_X_CLIENT_IP")  # Custom headers
        # )
        # if ip:
        #     return ip

        # Fallback to REMOTE_ADDR (direct connection or unconfigured proxy)
        return request.META.get("REMOTE_ADDR")


class BlackListEntryException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "User is blacklisted and cannot perform this action. "
        "Please contact support for further assistance.",
    }
    default_code = "Not permitted"


class IsUSerBlackListed(permissions.BasePermission):

    def has_permission(self, request, view):
        user_instance = request.user
        email = user_instance.email

        if BlackListEntry.is_blacklisted(email=email):
            raise BlackListEntryException

        else:
            return True
