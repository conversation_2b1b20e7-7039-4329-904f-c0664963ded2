from datetime import datetime

from django.contrib.auth import get_user_model, authenticate
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from rest_framework import serializers
from rest_framework.exceptions import APIException
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView

from core import models
from core.helpers.func import calculate_pages
from core.helpers.enums import SecretQuestionChoices, Employee_Headcount
from requisition.models import Company
from stock_inventory.models import Branch


User = get_user_model()


class CustomAuthenticationError(APIException):
    status_code = 400  # You can customize the status code
    default_detail = {"status_code": "103", "message": "Invalid username/password"}
    default_code = "authentication_failed"

    def __init__(self, detail=None, code=None):
        # Use the provided detail if present, otherwise fallback to default_detail
        if detail is None:
            detail = self.default_detail
        super().__init__(detail, code)


class CustomTokenObtainSerializer(TokenObtainPairSerializer):
    username_field = "username"

    def validate(self, attrs):
        username = attrs.get(self.username_field)
        password = attrs.get("password")

        """
        Checking if the user exists by getting the email(username field) from authentication_kwargs.
        If the user exists we check if the user account is active.
        If the user account is not active we raise the exception and pass the message.
        Thus stopping the user from getting authenticated altogether.

        And if the user does not exist at all we raise an exception with a different error message.
        Thus stopping the execution right there.
        """
        try:
            user = User.objects.get(username=username)

            if not user.default_company:
                raise CustomAuthenticationError(
                    {
                        "status_code": "105",
                        "message": "User does not belong to a company",
                    }
                )
            if not user.default_branch:
                raise CustomAuthenticationError(
                    {
                        "status_code": "106",
                        "message": "User does not belong to a branch",
                    }
                )
            if user.sales_is_suspended:
                raise CustomAuthenticationError(
                    {"status_code": "101", "message": "User suspended, contact admin"}
                )
            if not user.password:
                return {"status_code": "102", "message": "User should reset password"}
            if not user.check_password(password):
                raise CustomAuthenticationError(
                    {"status_code": "103", "message": "Invalid username/password"}
                )

        except User.DoesNotExist:
            raise CustomAuthenticationError(
                {"status_code": "104", "message": "Invalid username/password"}
            )

        user = authenticate(email=user.email, password=password)
        if user is None:
            raise CustomAuthenticationError(
                {"status_code": "103", "message": "Invalid username/password"}
            )
            # Create the token pair

        refresh = self.get_token(user)
        this_time = timezone.localtime()
        user.last_login = this_time
        user.save()
        # Add custom data to the token response if needed
        data = {
            "status_code": "100",
            "branch": user.default_branch.id,
            "company": user.default_company.id,
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "username": username,
            "first_name": user.first_name,
            "last_name": user.last_name,
        }

        return data

    @classmethod
    def get_token(cls, user):
        # Create a refresh token
        token = RefreshToken.for_user(user)

        # Add custom claims if necessary
        token["username"] = user.username

        return token


class SalesCustomObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainSerializer


class CompanyCustomPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    # default_error_messages = {
    #     'does_not_exist': 'Invalid company ID: {pk_value}. Please provide a valid ID.',
    #     'incorrect_type': 'Incorrect type. Expected a valid company ID, but got {data_type}.',
    # }

    default_error_messages = {
        "does_not_exist": "Please provide a valid Company ID.",
        "incorrect_type": "Incorrect type. Expected a valid company ID, but got {data_type}.",
    }

    def to_internal_value(self, data):
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail("does_not_exist", pk_value=data)
        except (TypeError, ValueError):
            self.fail("incorrect_type", data_type=type(data).__name__)


class BranchCustomPrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    # default_error_messages = {
    #     'does_not_exist': 'Invalid company ID: {pk_value}. Please provide a valid ID.',
    #     'incorrect_type': 'Incorrect type. Expected a valid company ID, but got {data_type}.',
    # }

    default_error_messages = {
        "does_not_exist": "Please provide a valid Branch ID.",
        "incorrect_type": "Incorrect type. Expected a valid branch ID, but got {data_type}.",
    }

    def to_internal_value(self, data):
        try:
            return super().to_internal_value(data)
        except ObjectDoesNotExist:
            self.fail("does_not_exist", pk_value=data)
        except (TypeError, ValueError):
            self.fail("incorrect_type", data_type=type(data).__name__)

class DateCharField(serializers.CharField):
    def __init__(self, *args, **kwargs):
        # Set default options for null and required
        kwargs.setdefault("required", False)
        kwargs.setdefault("allow_null", True)
        super().__init__(*args, **kwargs)

    def to_internal_value(self, value):
        if value in (None, ""):
            return None  # Allow null or empty string inputs
        try:
            this_date = datetime.strptime(value, "%d-%m-%Y %H:%M")
            return value
        except ValueError:
            raise serializers.ValidationError("Date format must be DD-MM-YYYY HH:MM")

    def to_representation(self, value):
        if value is None:
            return None  # Return None for null values
        if isinstance(value, datetime):
            return value.strftime("%d-%m-%Y %H:%M")
        raise serializers.ValidationError("Invalid datetime value")
    
# Create your serializer(s) here.
class SearchUsersSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "email",
            "phone_no",
            "first_name",
            "last_name",
        )


class MirrorLibertyPaySignInSerializer(serializers.Serializer):
    email = serializers.CharField()
    password = serializers.CharField(max_length=6, min_length=6)


class CategoriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CategoryList
        fields = "__all__"


class CategoriesSerializer(serializers.Serializer):
    category = serializers.DictField()


class WaitListSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.WaitList
        fields = "__all__"


class VerifyOtpSerializer(serializers.Serializer):
    otp = serializers.CharField(max_length=6, min_length=6)


class NotificationSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.Notification
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = (
            instance.branch.location if instance.branch is not None else None
        )
        return representation


class NotificationUpdateSerializer(serializers.Serializer):
    company = serializers.UUIDField()
    id = serializers.ListField(
        child=serializers.UUIDField(),
        required=True,
        allow_empty=False
    ) 
    def validate(self, attrs):
        ids = attrs.get('id', [])
        company = attrs.get('company')

        # Query the database for all matching notifications in one query
        existing_ids = models.Notification.objects.filter(
            id__in=ids,
            company=company
        ).values_list('id', flat=True)

        # Find missing IDs
        missing_ids = set(ids) - set(existing_ids)

        if missing_ids:
            raise serializers.ValidationError({
                'id': f"The following IDs do not exist for the company: {list(missing_ids)}"
            })

        return attrs

        


class CreateSenderIdSerializer(serializers.Serializer):
    company = CompanyCustomPrimaryKeyRelatedField(queryset=Company.objects.all())
    sender_id = serializers.CharField(max_length=11, min_length=5)
    sample_message = serializers.CharField(max_length=200)


class SendCampaignMessageSerializer(serializers.Serializer):
    company = CompanyCustomPrimaryKeyRelatedField(queryset=Company.objects.all())
    sender_id = serializers.PrimaryKeyRelatedField(
        queryset=models.CampaignSenderId.objects.all()
    )
    message = serializers.CharField(max_length=745, min_length=1)
    contacts = serializers.ListField()
    priority_route = serializers.BooleanField(default=False)
    flash_route = serializers.BooleanField(default=False)
    send_later = serializers.BooleanField(default=False)
    send_date = DateCharField()
    campaign_name = serializers.CharField(max_length=100)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        send_later = attrs.get("send_later")
        send_date = attrs.get("send_date")
        if send_later:
            if not send_date:
                raise serializers.ValidationError({"message": "send_date is required"})
        attrs["page_number"] = calculate_pages(len(attrs.get("message")))
        return super().validate(attrs)


class ListCampaignSenderId(serializers.ModelSerializer):
    class Meta:
        model = models.CampaignSenderId
        fields = ("id", "sender_id", "sample_message")


class ListCampaignBatch(serializers.ModelSerializer):
    class Meta:
        model = models.CampaignBatch
        fields = (
            "id",
            "sender",
            "message",
            "priority_route",
            "campaign_name",
            "flash_route",
            "send_later",
            "created_at",
            "send_date",
            "total_amount",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["sender"] = instance.sender.sender_id
        contacts = []
        all_receivers = models.CampaignMessage.objects.filter(
            campaign_batch=instance, company=instance.company
        )
        for receiver in all_receivers:
            contacts.append(
                {
                    "phone_number": receiver.phone_number,
                    "sender_id": receiver.sender.sender_id,
                    "campaign_name": receiver.campaign_name,
                    "status": receiver.status,
                    "priority_route": receiver.priority_route,
                    "flash_route": receiver.flash_route,
                    "send_later": receiver.send_later,
                    "send_date": receiver.send_date,
                }
            )
        representation["recipients"] = len(contacts)
        representation["status"] = "COMPLETED"
        representation["contacts"] = contacts
        return representation


class SetDefaultCompanySerializer(serializers.Serializer):
    company = CompanyCustomPrimaryKeyRelatedField(queryset=Company.objects.all())


class SetDefaultBranchSerializer(serializers.Serializer):
    branch = serializers.PrimaryKeyRelatedField(queryset=Branch.objects.all())


class SalesLoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True)


class SalesUserRegistrationSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    # email = serializers.EmailField(required=True)
    company = CompanyCustomPrimaryKeyRelatedField(queryset=Company.objects.all())
    branch = BranchCustomPrimaryKeyRelatedField(queryset=Branch.objects.all())

    def validate(self, attrs):
        username = attrs.get("username")
        # email = attrs.get('email')
        company = attrs.get("company")
        branch = attrs.get("branch")
        get_branch = Branch.retrieve_company_branch(id=branch.id, company=company)
        if not company.user:
            raise serializers.ValidationError(
                {"message": f"company does not have an account registered"}
            )
        if not get_branch:
            raise serializers.ValidationError(
                {
                    "message": f"{branch.name} branch does not belong to {company.company_name} company"
                }
            )
        # if User.user_exist(email, phone_no=None):
        #     raise serializers.ValidationError({"message": f"{email} already exists"})
        if User.username_exist(username):
            raise serializers.ValidationError({"message": f"{username} already exists"})

        return attrs


class ResetSalesDefaultPasscodeSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    default_password = serializers.CharField(required=True)
    password = serializers.CharField(required=True, min_length=6, max_length=6)

    def validate(self, attrs):
        password = attrs.get("password")
        if not password.isnumeric():
            raise serializers.ValidationError(
                {"message": "password can only contain numeric characters"}
            )
        return attrs


class ListSalesUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "email",
            "username",
            "first_name",
            "last_name",
            "sales_passcode",
            "last_login",
        )


class StartSalesShiftSerializer(serializers.Serializer):
    cash_in_hand = serializers.FloatField(min_value=0)


class EndSalesShiftSerializer(serializers.Serializer):
    cash_in_hand = serializers.FloatField(min_value=0)


class ListSalesAttendanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SalesTeamShift
        fields = (
            "user",
            "company",
            "branch",
            "start_cash_in_hand",
            "end_cash_in_hand",
            "work_duration",
            "start_time",
            "end_time",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = (
            instance.branch.name if instance.branch is not None else None
        )
        return representation


class ListSalesBreakSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SalesTeamBreakShift
        fields = (
            "user",
            "company",
            "branch",
            "break_duration",
            "start_time",
            "end_time",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["company"] = instance.company.company_name
        representation["branch"] = (
            instance.branch.name if instance.branch is not None else None
        )
        return representation


class AdminResetSalesDefaultPasscodeSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)


class SecretQuestionSerializer(serializers.Serializer):
    username = serializers.CharField(max_length=255)
    secret_question = serializers.ChoiceField(choices=SecretQuestionChoices.choices)
    secret_answer = serializers.CharField(max_length=2250)


class VfdMeetingSerializer(serializers.Serializer):
    work_email = serializers.CharField()
    full_name = serializers.CharField()
    phone_number = serializers.CharField()
    country = serializers.CharField()
    employee_headcount = serializers.ChoiceField(choices=Employee_Headcount.choices)
    availability = serializers.CharField()
    meeting_link = serializers.CharField()
    product_vertical = serializers.ListField()
    company_name = serializers.CharField()


class SalesAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SalesAgents
        fields = [
            "name",
            "email",
            "phone_number",
        ]


class CampaignLeadSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.CampaignLead
        fields = "__all__"



############# Settings & Profile #####################

class ProfilePictureSerializer(serializers.Serializer):
    profile_picture = serializers.ImageField(required=True)


class ResetPasswordSerializer(serializers.Serializer):
    old_pin = serializers.CharField(max_length=4, required=True)
    new_pin = serializers.CharField(max_length=4, required=True)
    new_pin_retry = serializers.CharField(max_length=4, required=True)

    def validate(self, data):
        if data["new_pin"] != data["new_pin_retry"]:
            raise serializers.ValidationError({"new_pin_retry": "New PINs do not match."})
        return data


class VerifyBVNSerializer(serializers.Serializer):
    bvn_number = serializers.CharField(max_length=11, min_length=11, required=True)
    email = serializers.CharField(required=True)


class VerifyNINSerializer(serializers.Serializer):
    nin_number = serializers.CharField(max_length=11, min_length=11, required=True)
    email = serializers.CharField(max_length=256, required=True)


class ImageSerializer(serializers.Serializer):
    email = serializers.CharField(max_length=256, required=True)
    user_photo = serializers.ImageField(required=True)


class ConfirmationSerializer(serializers.Serializer):
    email = serializers.CharField(max_length=256, required=True)
    

class GuarantorFormSerializer(serializers.Serializer):
    email = serializers.CharField(max_length=255)
    guarantor_name = serializers.CharField(max_length=255)
    guarantor_phone_number = serializers.CharField(max_length=20)
    guarantor_email = serializers.EmailField()
    guarantor_occupation = serializers.CharField(max_length=255)
    guarantor_address = serializers.CharField(max_length=255)
    next_of_kin_name = serializers.CharField(max_length=255)
    next_of_kin_relationship = serializers.CharField(max_length=255)
    next_of_kin_phone_number = serializers.CharField(max_length=20)
    next_of_kin_address = serializers.CharField(max_length=255)


class OfflineApplicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.OfflineApplication
        fields = [
            "app_type",
            "version",
            "app",
            "uploaded_by",
        ]
        read_only_fields = ["uploaded_by",]

class DeleteSalesAgentSerializer(serializers.Serializer):
    name = serializers.CharField()

class ConstantTableSalesTeamViewSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ConstantTable
        fields = [
           "sales_team_emails",
        ]

class UpdatePreferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["news_and_update", "tips_and_tutorial", "user_research", "transaction_notification", "reminder_update"]

class UpdateNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["get_credit_alert_notification", 
                  "get_debit_alert_notification", 
                  "get_approved_stock_notification", 
                  "get_cancelled_stock_notification", 
                  "get_new_stock_notification",
                  "get_create_team_notification",
                  "get_create_branch_notification",
                  "get_approved_loans_notification",
                  "get_approved_instant_wage_notification",
                  "get_unapproved_instant_wage_notification",
                  "get_paid_invoice_notification",
                  "get_due_invoice_notification",
                  "get_sent_invoice_notification",
                  "get_create_company_notification",
                  ]