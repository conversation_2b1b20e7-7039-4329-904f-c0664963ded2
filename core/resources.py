from django.contrib.auth import get_user_model
from import_export import resources

from core import models


User = get_user_model()


# Create your resource(s) here.
class UserResource(resources.ModelResource):
    class Meta:
        model = User


class ConstantTableResource(resources.ModelResource):
    class Meta:
        model = models.ConstantTable


class KycDetailsResource(resources.ModelResource):
    class Meta:
        model = models.KycDetails


class IPWhitelistResource(resources.ModelResource):
    class Meta:
        model = models.IPWhitelist


class CategoryListResource(resources.ModelResource):
    class Meta:
        model = models.CategoryList


class WaitListResource(resources.ModelResource):
    class Meta:
        model = models.WaitList


class BlackListEntryResource(resources.ModelResource):
    class Meta:
        model = models.BlackListEntry


class OTPResource(resources.ModelResource):
    class Meta:
        model = models.OTP


class NotificationResource(resources.ModelResource):
    class Meta:
        model = models.Notification


class PaystackPaymentResource(resources.ModelResource):
    class Meta:
        model = models.PaystackPayment


class CampaignSenderIdResource(resources.ModelResource):
    class Meta:
        model = models.CampaignSenderId


class CampaignBatchResource(resources.ModelResource):
    class Meta:
        model = models.CampaignBatch


class CampaignMessageResource(resources.ModelResource):
    class Meta:
        model = models.CampaignMessage


class CampaignSmsTransactionResource(resources.ModelResource):
    class Meta:
        model = models.CampaignSmsTransaction


class SalesTeamShiftResource(resources.ModelResource):
    class Meta:
        model = models.SalesTeamShift


class SalesTeamBreakShiftResource(resources.ModelResource):
    class Meta:
        model = models.SalesTeamBreakShift


class SalesUserRegistrationDumpResource(resources.ModelResource):
    class Meta:
        model = models.SalesUserRegistrationDump


class IssueLogResource(resources.ModelResource):
    class Meta:
        model = models.IssueLog


class VfdMeetingResource(resources.ModelResource):
    class Meta:
        model = models.VfdMeeting


class SalesAgentResource(resources.ModelResource):
    class Meta:
        model = models.SalesAgents


class CampaignLeadResource(resources.ModelResource):
    class Meta:
        model = models.CampaignLead


class OfflineApplicationResource(resources.ModelResource):
    class Meta:
        model = models.OfflineApplication
