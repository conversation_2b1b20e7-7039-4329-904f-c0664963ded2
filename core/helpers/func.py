import base64
from datetime import datetime
import os
import json

import cryptography
from cryptography.fernet import Ferne<PERSON>
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework.permissions import BasePermission
import requests


SECRET_KEY = settings.SECRET_KEY
User = get_user_model()


class ByteHelper:
    @classmethod
    def convert_byte_to_string(cls, byte_text):
        return str(byte_text).replace("b'", "").replace("'", "")

    @classmethod
    def convert_string_to_byte(cls, text_str):
        return bytes(text_str, "utf-8")


def upload_path(instance, filename):
    """Set document upload path"""
    filename, ext = os.path.splitext(filename)
    filename = f"{filename}{ext}"

    if ext == ".jpg" or ext == ".jpeg" or ext == ".png":
        return f"req_docs/{instance.user.first_name}_{instance.user.liberty_pay_id}/images/{filename}"
    else:
        return f"req_docs/{instance.user.first_name}_{instance.user.liberty_pay_id}/documents/{filename}"


def generate_fernet_key():
    # Concatenate the secret key and email
    key = SECRET_KEY
    key = key.encode()
    # Generate the SHA256 hash of the key
    hashed_key = base64.urlsafe_b64encode(key[:32])
    return hashed_key


def encrypt_string(pass_code):
    pass_code = str(pass_code)
    fernet_key_generated_with_secrete_key = generate_fernet_key()
    fernet = Fernet(fernet_key_generated_with_secrete_key)
    encrypted_str = ByteHelper.convert_byte_to_string(fernet.encrypt(pass_code.encode()))
    return encrypted_str


def decrypt_string(string):
    fernet_key_generated_with_secrete_key = generate_fernet_key()
    fernet = Fernet(fernet_key_generated_with_secrete_key)

    try:
        decrypted_auth_code = fernet.decrypt(ByteHelper.convert_string_to_byte(string)).decode()
    except cryptography.fernet.InvalidToken:
        return {"valid": False, "super_token": None}
    return {"valid": True, "super_token": decrypted_auth_code}


def format_date_time_obj_to_year_month_day(date_string):
    datetime_obj = datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S%z")
    return datetime_obj.strftime("%Y-%m-%d")


class CanAccessDisbursementView(BasePermission):
    def has_permission(self, request, view):
        if request.user.is_authenticated:
            # Check if the user is either an ADMIN or SUB_ADMIN
            return request.user.role in ['ADMIN', 'SUB_ADMIN']
        return False


def calculate_percentage(amount, total_amount):
    result = (int(amount) / int(total_amount)) * 100
    percentage = round(result, 1)
    return percentage


def is_valid_date_format(date_string):
    format_string = "%Y-%m-%d"
    try:
        date_object = datetime.strptime(date_string, format_string)
    except ValueError:
        return False
    return date_object.date()


def save_to_brevo(data):
    """
    When a user joins the Paybox waitlist. The contact
    info is also update on Brevo CRM
    """

    first_name = data.get("first_name", "")
    last_name = data.get("last_name", "")
    email = data.get("email", "")
    phone_number = User.format_number_from_back_add_234(data.get("phone_number", ""))
    company_name = data.get("company_name", "")
    company_size = data.get("company_size", "")
    product_interest = data.get("product_interest", 0)

    brevo_base_url = "https://api.brevo.com/v3"
    create_contact_url = "%s/contacts" %brevo_base_url
    brevo_headers = {
        "api-key": "%s" %settings.BREVO_API_KEY,
        "content-type": "application/json"
    }

    contact_data = {
        "listIds": [6],
        "email": email,
        "attributes": {
                       "sms": phone_number,
                       "LASTNAME": last_name,
                       "FIRSTNAME": first_name,
                       "COMPANYNAME": company_name,
                       "COMPANYSIZE": company_size,
                       "PRODUCTINTEREST": product_interest
                       }
                }

    resp = requests.request(
            "POST", url=create_contact_url, 
            headers=brevo_headers,
            data=json.dumps(contact_data)
            )
    return resp.text


def calculate_pages(num_characters):
    characters_per_page = 149
    if num_characters <= 0:
        return 0
    return (num_characters + characters_per_page - 1) // characters_per_page


def calculate_sms_charges(priority_route, page_num, receiver_count):
    from core.models import ConstantTable

    CONST = ConstantTable.get_constant_instance()

    priority = CONST.priority_campaign_sms_charge
    normal = CONST.normal_campaign_sms_charge
    if priority_route:
        sms_charges = priority * page_num * receiver_count
    else:
        sms_charges = normal * page_num * receiver_count
    return sms_charges


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        from datetime import datetime, date
        
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)


def search_liberty_pay_username(token, username):
    from core.helpers.apis.request_cls import LibertyPayPlus

    get_user_name = LibertyPayPlus.request_username_exist(auth_token=token, username=username)
     
    return get_user_name.get("status")


def local_store_upload(instance, filename):
    """Set application upload path"""
    filename, ext = os.path.splitext(filename)
    filename = f"{filename}{ext}"
    
    if ext == ".dmg":
        return f"offline_apps/macos/{filename}"
    if ext == ".apk":
        return f"offline_apps/android/{filename}"
    if ext == ".exe":
        return f"offline_apps/windows/{filename}"
