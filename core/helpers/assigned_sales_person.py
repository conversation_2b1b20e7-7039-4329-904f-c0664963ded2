import json
import requests
import base64
from decouple import config

class AssignedSalesPerson:
    @classmethod
    def send_assigned_sales_person_data(self, **kwargs):
        url = f"{config('LIBERTY_PAY_MARKETING_BASE_URL')}/api/schedule_meeting_slack_update/"
        
        headers = {
            "Authorization": f"No Auth",
            "Content-Type": "application/json"
        }

        payload = kwargs

        response = requests.post(url=url, headers=headers, json=payload)

        print('STATUS_CODE:::::::', response, "\n\n\n\n")

        return response


class SlackWaitList:
    @classmethod
    def send_waitlist_to_slack(self, **kwargs):
        url = f"{config('LIBERTY_PAY_MARKETING_BASE_URL')}/api/waitlist_slack_update/"

        headers = {
            "Content-Type": "application/json"
        }

        payload = kwargs

        response = requests.post(url=url, headers=headers, json=payload)

        print('STATUS_CODE:::::::', response, "\n\n\n\n")
    
        return response