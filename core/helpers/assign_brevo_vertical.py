from itertools import combinations
from core.tasks import add_contact_to_brevo

def chosen_vertical(full_name, phone_number, email, product_vertical:list):
    pairs = list(combinations(product_vertical, 2))

    for vertical_one in product_vertical:
        if vertical_one == "Spend_Management":
            vertical = 35
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e)
            
        elif vertical_one == "Stock_and_Inventory":
            vertical = 36
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e)
        elif vertical_one == "Sales_Management":
            vertical = 36
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e)
            
        elif vertical_one == "HR_Solution":
            vertical = 34
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e) 
        elif vertical_one == "Payroll":
            vertical = 34
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e)
            
    for vertical_one, vertical_two in pairs:
        if vertical_one == "Stock_and_Inventory" and vertical_two == "Sales_Management":
            vertical = 36
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e)
            

        if vertical_one == "HR_Solution" and vertical_two == "Payroll":
            vertical = 34
            try:
                add_contact_to_brevo.delay(
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    product_vertical=vertical,
                )
            except Exception as e:
                return str(e)
"""
        These are the vertical list IDs on brevo
        1) HR Management Solution = 34
        2) Spend Management Solution = 35
        3) Sales, Stock & Inventory Solution = 36
"""

# Account for a combination of all the product verticals