from datetime import datetime, <PERSON><PERSON><PERSON>
import json
from typing import Optional
import uuid

from django.conf import settings
from django.contrib.auth.hashers import check_password, make_password
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinV<PERSON>ueValidator
from django.db import models
from django.utils.translation import gettext as _
import pytz
from rest_framework import status
from rest_framework.response import Response

from core.helpers.enums import (
    NotificationTypes,
    SecretQuestionChoices,
    Employee_Headcount,
    SupportedAppChoices,
)
from core.managers import OTPManager, UserManager
from core.services import Paystack, WhisperSms
from helpers.enums import Currency

TRANS_NOTIFICATION_CHOICES = [
    ("CREDIT", "CREDIT"),
    ("DEBIT", "DEBIT"),
    ("ALL", "ALL"),
]

NOTIFICATION_REMINDER = [
    ("NO_REMINDER", "NO_REMINDER"),
    ("IMPORTANT_REMINDER", "IMPORTANT_REMINDER"),
    ("ALL_REMINDER", "ALL_REMINDER"),
]

# Create your model(s) here.
class BaseModel(models.Model):
    """Base model for reuse.
    Args:
        models (Model): Django's model class.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)

    class Meta:
        abstract = True


class DeleteHandler(models.Model):
    """Instance delete handler for reuse.
    Args:
        models (Model): Django's model class.
    """

    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        abstract = True

    def soft_delete(self):
        """
        Soft deletes the object by setting the 'is_active' attribute to False
        and the 'is_deleted' attribute to True.
        NOTE:
        - it marks the object as inactive and deleted without removing it from the database.
        """
        self.is_active = False
        self.is_deleted = True
        self.save()


class User(AbstractUser, BaseModel):
    """User model."""

    CHANNEL = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS", "POS"),
    ]

    default_company = models.ForeignKey(
        "requisition.Company",
        on_delete=models.SET_NULL,
        related_name="default_company",
        null=True,
        blank=True,
    )
    default_branch = models.ForeignKey(
        "stock_inventory.Branch",
        on_delete=models.SET_NULL,
        related_name="default_company",
        null=True,
        blank=True,
    )
    is_superuser = models.BooleanField(default=False, editable=False)
    is_staff = models.BooleanField(default=False, editable=False)
    username = models.CharField(max_length=255, null=True, blank=True)
    liberty_pay_id = models.IntegerField(default=0)
    liberty_pay_customer_id = models.CharField(max_length=255, null=True, blank=True)
    phone_no = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)

    # bvn
    bvn_number = models.CharField(max_length=255, null=True, blank=True)
    bvn_first_name = models.CharField(max_length=255, null=True, blank=True)
    bvn_last_name = models.CharField(max_length=255, null=True, blank=True)
    bvn_middle_name = models.CharField(max_length=255, null=True, blank=True)
    date_of_birth = models.CharField(max_length=255, null=True, blank=True)
    bvn_residentialAddress = models.CharField(max_length=500, null=True, blank=True)
    bvn_lgaOfResidence = models.CharField(max_length=255, null=True, blank=True)
    bvn_stateOfResidence = models.CharField(max_length=255, null=True, blank=True)
    state = models.CharField(max_length=255, null=True, blank=True)
    street = models.CharField(max_length=500, null=True, blank=True)
    lga = models.CharField(max_length=255, null=True, blank=True)
    nearest_landmark = models.CharField(max_length=255, null=True, blank=True)
    gender = models.CharField(max_length=255, null=True, blank=True)

    account_no = models.CharField(max_length=255, null=True, blank=True)
    account_name = models.CharField(max_length=255, null=True, blank=True)
    kyc_level = models.CharField(max_length=255, null=True, blank=True)
    referral_code = models.CharField(max_length=255, null=True, blank=True)
    bank = models.CharField(max_length=255, null=True, blank=True)
    bank_code = models.CharField(max_length=255, null=True, blank=True)

    liberty_pay_customer_status = models.BooleanField(default=False)
    liberty_pay_user_type = models.CharField(max_length=255, null=True, blank=True)
    password = models.CharField(max_length=125, null=True, blank=True)
    ip_address = models.CharField(max_length=1000, null=True, blank=True)
    is_payroll = models.BooleanField(default=False)
    has_company = models.BooleanField(default=False)
    is_consent = models.BooleanField(default=False)
    payroll_id = models.CharField(max_length=255, null=True, blank=True)
    channel = models.CharField(
        max_length=200, choices=CHANNEL, default="WEB", null=True, blank=True
    )
    agency_banking_transaction_pin = models.CharField(
        max_length=255, null=True, blank=True, editable=False
    )
    requisition_transaction_pin = models.CharField(
        max_length=255, null=True, blank=True, editable=False
    )
    liberty_pay_user_hash_passkey = models.CharField(
        max_length=255, null=True, blank=True, editable=False
    )
    kyc_updated = models.BooleanField(default=False)
    sales_passcode = models.CharField(max_length=255, null=True, blank=True)
    sales_is_suspended = models.BooleanField(default=False)
    sales_is_deleted = models.BooleanField(default=False)
    last_login = models.DateTimeField(null=True, blank=True)
    secret_question = models.TextField(
        null=True, blank=True, choices=SecretQuestionChoices.choices
    )
    secret_answer = models.TextField(null=True, blank=True)
    is_supplier = models.BooleanField(default=False)
    wema_account_number = models.CharField(max_length=255, null=True, blank=True)
    wema_account_name = models.CharField(max_length=255, null=True, blank=True)

    #notification checker
    news_and_update = models.BooleanField(default=False)
    tips_and_tutorial = models.BooleanField(default=False)
    user_research = models.BooleanField(default=False)
    transaction_notification = models.CharField(max_length=50, null=True, blank=True, choices=TRANS_NOTIFICATION_CHOICES, default="ALL")

    #reminders
    reminder_update = models.CharField(max_length=50, null=True, blank=True, choices=NOTIFICATION_REMINDER, default="ALL_REMINDER")

    #notification on wallets
    get_credit_alert_notification = models.BooleanField(default=False)
    get_debit_alert_notification = models.BooleanField(default=False)

    #notification on Stocks
    get_approved_stock_notification = models.BooleanField(default=False)
    get_cancelled_stock_notification = models.BooleanField(default=False)
    get_new_stock_notification = models.BooleanField(default=False)
    get_create_team_notification = models.BooleanField(default=False)
    get_create_branch_notification = models.BooleanField(default=False)

    #notification on payroll
    get_approved_loans_notification = models.BooleanField(default=False)
    get_approved_instant_wage_notification = models.BooleanField(default=False)
    get_unapproved_instant_wage_notification = models.BooleanField(default=False)

    #notification on invoice
    get_paid_invoice_notification = models.BooleanField(default=False)
    get_due_invoice_notification = models.BooleanField(default=False)
    get_sent_invoice_notification = models.BooleanField(default=False)

    #notification on spend management
    get_create_company_notification = models.BooleanField(default=False)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self) -> str:
        return str(self.email)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "USER PROFILE"
        verbose_name_plural = "USER PROFILES"

    @property
    def identifier(self):
        """
        NOTE:
        - check membership status at inception (first company).
        - else check user for company ownership status.
        - default Liberty Assured Limited Agents to Seeds & Pennies Branch.
        Returns:
        - default company and branch for the user.
        """

        from requisition.helpers.enums import UserStatus
        from requisition.models import Company, TeamMember
        from stock_inventory.models import Branch

        if self.default_company is None or self.default_branch is None:
            # Seeds & Pennies defaulting.
            if self.liberty_pay_user_type in [
                "AJO_AGENT",
                "DMO_AGENT",
                "STAFF_AGENT",
            ]:
                constant = ConstantTable.get_constant_instance()
                if constant.bnpl_head_office is None:
                    company = None
                    branch = None
                else:
                    bnpl_branch = Branch.objects.filter(
                        id=constant.bnpl_head_office
                    ).last()
                    if bnpl_branch is None:
                        company = None
                        branch = None
                    else:
                        company = bnpl_branch.company
                        branch = bnpl_branch
            else:
                membership = TeamMember.objects.filter(
                    member__id=self.id,
                    status=UserStatus.ACTIVE,
                    is_active=True,
                ).last()
                if membership is not None:
                    company = membership.team.company
                    branch = membership.team.branch
                    if branch is None:
                        branch = Branch.objects.filter(company=company).last()
                else:
                    user_company = Company.objects.filter(user__id=self.id).last()
                    if user_company is not None:
                        company = user_company
                    else:
                        company = None
                    if company is not None:
                        branch = Branch.objects.filter(company=company).last()
                    else:
                        branch = None
            # set user defaults
            self.default_company = company
            self.default_branch = branch
            self.save()
        return {
            "id": self.id,
            "username": self.username,
            "company": self.default_company,
            "branch": self.default_branch,
            "secret_question": self.secret_question,
            "secret_answer": self.secret_answer,
        }

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"

    @property
    def bvn_full_name(self):
        return f"{self.bvn_first_name} {self.bvn_last_name}"

    @property
    def full_address(self):
        return f"{self.street} {self.lga} {self.state}"

    @property
    def blacklist_status(self):
        is_genral_blacklist = BlackListEntry.is_blacklisted(email=self.email)
        is_sendmoney_blacklist = BlackListEntry.can_send_money(email=self.email)
        if not is_genral_blacklist and not is_sendmoney_blacklist:
            blacklist_status = "None"
        elif is_genral_blacklist and not is_sendmoney_blacklist:
            blacklist_status = "GENERAL"
        elif is_sendmoney_blacklist and not is_genral_blacklist:
            blacklist_status = "FUND TRANSFER"
        else:
            blacklist_status = "[GENERAL, FUND TRANSFER]"

        return blacklist_status

    @classmethod
    def user_exist(cls, email, phone_no):
        if email and not phone_no:
            try:
                user = cls.objects.get(email=email)
            except cls.DoesNotExist:
                return None

            return user

        if phone_no and not email:
            try:
                user = cls.objects.get(phone_no=phone_no)
            except cls.DoesNotExist:
                return None
            return user

        if phone_no and email:
            user = cls.objects.filter(
                models.Q(email=email) | models.Q(phone_no=phone_no)
            ).first()
            if user:
                return user
            else:
                return None

    @staticmethod
    def format_number_from_back_add_234(phone) -> str:
        if phone is None:
            return None

        formatted_num = phone[-10:]
        if formatted_num[0] == "0":
            return None
        else:
            return "234" + formatted_num

    @staticmethod
    def format_number_from_back_234(phone_number: str) -> str:
        phone_number = phone_number.replace("+", "")
        if phone_number.startswith("235") and len(phone_number) == 13:
            return phone_number

        formatted_num = phone_number[-10:]

        if len(formatted_num) != 10 or formatted_num[0] == "0":
            return None
        else:
            return "234" + formatted_num

    @staticmethod
    def create_payroll_pin(user, pincode) -> bool:
        """
        Collect pin and hash it
        """
        if not user.requisition_transaction_pin:
            hashed_pin = make_password(pincode)
            user.requisition_transaction_pin = hashed_pin
            user.save()
            return True
        else:
            return False

    @staticmethod
    def check_sender_payroll_pin(user, pincode) -> bool:
        """
        Check if entered pin is correct
        """
        return check_password(pincode, user.requisition_transaction_pin)

    @classmethod
    def create_payroll_user(cls, serialized_data: dict):

        email = serialized_data.get("email")
        phone_no = serialized_data.get("phone_no")
        password = serialized_data.get("password")
        first_name = serialized_data.get("first_name")
        last_name = serialized_data.get("last_name")
        gender = serialized_data.get("gender")

        try:
            user = User.objects.create(
                email=email,
                password=make_password(password),
                is_active=False,
            )
        except:
            return Response(
                {
                    "status": False,
                    "message": "failed",
                    "error_message": "user with email or phone already exist",
                    "error_code": "892",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # token, _ = Token.objects.get_or_create(user=user)

        # save user in UserProfile Model
        formated_phone = User.format_number_from_back_add_234(phone_no)
        user.phone_no = formated_phone
        user.first_name = first_name
        user.last_name = last_name
        user.gender = gender.upper()
        user.save()

        user_data = serialized_data

        del user_data["password"]

        try:
            del user_data["confirm_password"]
        except KeyError:
            pass

        # user_data["token"] = token.key

        return Response(
            {
                "status": True,
                "user_data": user_data,
            },
            status=status.HTTP_201_CREATED,
        )

    @classmethod
    def username_exist(cls, username):
        try:
            user = cls.objects.get(username=username)
        except cls.DoesNotExist:
            return None
        return user

    def update_bvn_field(self, bvn_number):
        if not self.bvn_number:
            self.bvn_number = bvn_number
            self.save(update_fields=["bvn_number"])
            self.refresh_from_db
        return

class ConstantTable(models.Model):
    vfd_fee = models.FloatField(default=10.00)
    payroll_charge_fee = models.FloatField(default=2100.00)
    payroll_buddy_charge_fee = models.FloatField(default=2000.00)
    withdrawal_fee = models.FloatField(default=100.00)
    instant_wage_withdrawal_fee = models.FloatField(default=100.00)
    is_active = models.BooleanField(default=True)
    send_money = models.BooleanField(default=False)
    send_money_via_buddy = models.BooleanField(default=False)
    send_automated_pfa = models.BooleanField(default=False)
    company_active_days = models.IntegerField(default=35)
    company_payroll_count = models.IntegerField(default=3)
    company_validity_days = models.IntegerField(default=90)
    employee_payroll_count = models.IntegerField(default=3)
    instant_wage_withdrawal_percentage = models.IntegerField(default=50)
    one_click_charge_fee = models.FloatField(default=100.00)
    vfd_stamp_duty = models.FloatField(default=50.00)
    stamp_duty_base_amount = models.FloatField(default=10000.00)
    use_float_to_internal = models.BooleanField(default=False)
    attendance_time = models.IntegerField(default=10)
    instant_wage_company_name = models.CharField(
        max_length=255, default="PAYBOX", null=True, blank=True
    )
    to_add_num_bvn = models.IntegerField(default=1)
    send_sms_campaign = models.BooleanField(default=False)
    bnpl_head_office = models.UUIDField(null=True, blank=True)
    priority_campaign_sms_charge = models.FloatField(default=4.99)
    normal_campaign_sms_charge = models.FloatField(default=2.50)
    company_verification_number = models.IntegerField(default=3)
    multiple_payroll_limit = models.IntegerField(default=3)
    minimum_wage_amount = models.FloatField(default=840000.0)
    sales_team_emails = models.JSONField(default=list, blank=True, null=True)
    asset_useful_life = models.PositiveIntegerField(default=5)
    skip_pension_id = models.IntegerField(null=True, blank=True)
    skip_company_pension_id = models.CharField(max_length=50, null=True, blank=True)
    verify_company_employee_id = models.CharField(max_length=50, null=True, blank=True)
    allowed_utility_month = models.IntegerField(default=6)
    user_address_match_percentage = models.IntegerField(default=80)
    user_guarantor_address_match_percentage = models.IntegerField(default=80)
    liberty_custom_pension_ids = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = "CONSTANT VARIABLE"
        verbose_name_plural = "CONSTANT VARIABLE"

    @classmethod
    def get_constant_instance(cls):
        return cls.objects.last()


class KycDetails(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    user_email = models.CharField(max_length=255, blank=True, null=True)
    bvn_first_name = models.CharField(max_length=255, blank=True, null=True)
    bvn_middle_name = models.CharField(max_length=255, blank=True, null=True)
    bvn_last_name = models.CharField(max_length=255, blank=True, null=True)
    bvn_gender = models.CharField(max_length=255, blank=True, null=True)
    bvn_birthdate = models.CharField(max_length=255, blank=True, null=True)
    bvn_number = models.CharField(max_length=255, blank=True, null=True)
    bvn_phone_number = models.CharField(max_length=255, blank=True, null=True)
    bvn_phone_number_2 = models.CharField(max_length=255, blank=True, null=True)
    kyc_level = models.CharField(max_length=255, blank=True, null=True)
    bvn_nationality = models.CharField(max_length=255, blank=True, null=True)
    bvn_watchlisted = models.CharField(max_length=255, blank=True, null=True)
    bvn_marital_status = models.CharField(max_length=255, blank=True, null=True)
    bvn_lga_of_origin = models.CharField(max_length=255, blank=True, null=True)
    bvn_residential_address = models.CharField(max_length=255, blank=True, null=True)
    bvn_state_of_origin = models.CharField(max_length=255, blank=True, null=True)
    verification_status = models.CharField(max_length=20, blank=True, null=True)
    result = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = "KYC DETAILS"
        verbose_name_plural = "KYC DETAILS"


class IPWhitelist(BaseModel):
    ip_address = models.CharField(max_length=255, blank=True, null=True)
    allowed = models.BooleanField(default=False)

    # def __str__(self):
    #     return self.ip_address

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "WHITE LISTED IP"
        verbose_name_plural = "WHITE LISTED IP"


class CategoryList(models.Model):
    title = models.CharField(max_length=255)
    budgeted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = "LIST OF CATEGORIES"
        verbose_name_plural = "LIST OF CATEGORIES"


class WaitList(models.Model):
    PRODUCT_INTEREST = (
        ("STOCK", "STOCK"),
        ("HR_MANAGEMENT", "HR_MANAGEMENT"),
        ("PERSONAL", "PERSONAL"),
        ("SALES_HUB", "SALES_HUB"),
        ("SPEND_MGMT", "SPEND_MGMT"),
        ("CARDS", "CARDS"),
    )

    LEAD_SOURCES = (
        ("SOCIAL_MEDIA", "SOCIAL_MEDIA"),
        ("IN_PERSON_EVENTS", "IN_PERSON_EVENTS"),
        ("WEBINAR", "WEBINAR"),
        ("SEARCH_ENGINE", "SEARCH_ENGINE"),
        ("EMAIL", "EMAIL"),
        ("ONLINE_ADS", "ONLINE_ADS"),
        ("EMPLOYER_COWORKER", "EMPLOYER_COWORKER"),
        ("WORD_OF_MOUTH", "WORD_OF_MOUTH"),
        ("OTHER", "OTHER"),
    )

    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    email = models.EmailField()
    phone_number = models.CharField(max_length=255)
    industry = models.CharField(max_length=255, null=True, blank=True)
    other_industry = models.CharField(max_length=255, null=True, blank=True)
    company_name = models.CharField(max_length=255)
    company_size = models.IntegerField()
    product_interest = models.CharField(max_length=255, choices=PRODUCT_INTEREST)
    lead_source = models.CharField(
        max_length=1200, default="OTHER", choices=LEAD_SOURCES, null=True, blank=True
    )
    created_at = models.DateTimeField(_("date created"), auto_now_add=True)
    updated_at = models.DateTimeField(_("date updated"), auto_now=True)
    paybox_agent_phone_number = models.CharField(max_length=255, blank=True, null=True)
    paybox_agent_name = models.CharField(max_length=255, blank=True, null=True)
    is_assigned = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        self.first_name = self.first_name.title()
        self.last_name = self.last_name.title()
        super().save(*args, **kwargs)
        self.notify_sales_team()

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.company_name}"

    def notify_sales_team(self):
        from payroll_app.tasks import send_payroll_payment_notification_email

        # sales_team_emails = ConstantTable.get_constant_instance().sales_team_emails

        sales_team_emails = list(SalesAgents.objects.filter(is_active = True).values_list("email", flat=True))

        if sales_team_emails:
            for email in sales_team_emails:
                send_payroll_payment_notification_email(
                    recipient=email,
                    subject="New Lead Added to Waitlist",
                    email_body="A new lead has been added to the waitlist. Please check the dashboard for more details.",
                    template_dir="waitlist_notify_team.html",
                    use_template=True,
                    name=email,
                    email_subject="New Lead Added to Waitlist",
                    subscriber_name=f"{self.first_name} {self.last_name}",
                    subscriber_phone=self.phone_number,
                    subscriber_email=self.email,
                )
        return True
    
    @classmethod
    def assign_demo_to_sales_agent(cls, email):
        wait_list_instance = cls.objects.filter(email=email, is_assigned=False).last()
        if wait_list_instance == None:
            return "DOES NOT EXIST"
        
        sales_agents = SalesAgents.objects.filter(is_active=True)

        if not sales_agents:
            print("NO AGENT AVAILABLE!") 

        wait_list_demo_count = {}

        for agent in sales_agents:
            wait_list_demo_count[agent.phone_number] = cls.objects.filter(is_assigned=True, paybox_agent_phone_number=agent.phone_number).count()

        min_demos = min(wait_list_demo_count.values())

        min_demos_agent = [agent for agent, count in wait_list_demo_count.items() if count == min_demos][0]

        min_agent_instance = SalesAgents.objects.get(phone_number=min_demos_agent)

        wait_list_instance.is_assigned = True
        wait_list_instance.paybox_agent_phone_number = min_demos_agent
        wait_list_instance.paybox_agent_name = f"{min_agent_instance.name}"
        wait_list_instance.save()

        return {
            "paybox_agent_name": min_agent_instance.name,
            "paybox_agent_email": min_agent_instance.email,
        }

    class Meta:
        verbose_name = "PAYBOX WAIT LIST"
        verbose_name_plural = "PAYBOX WAIT LIST"


class BlackListEntry(models.Model):
    general_black_list = models.JSONField(
        help_text='{"email": ["<EMAIL>", "<EMAIL>"],"phones": '
        '["************", '
        '"************"],"bvn": ["22134256"]}'
    )
    send_money = models.JSONField(
        help_text='{"email": ["<EMAIL>", "<EMAIL>"],"phones":'
        ' ["************", '
        '"************"],"bvn": ["22134256"]}'
    )
    wallet_entry = models.JSONField(
        help_text='{"email": ["<EMAIL>", "<EMAIL>"],"phones":'
        ' ["************", '
        '"************"],"bvn": ["22134256"]}',
        null=True, blank=True
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BLACK LIST ENTRY"
        verbose_name_plural = "BLACK LIST ENTRY"

    @classmethod
    def get_entry_instance(cls):
        entry_instance = cls.objects.last()
        if entry_instance:
            return entry_instance
        else:
            entry = cls.objects.create()
            return entry

    @classmethod
    def is_blacklisted(cls, email: str) -> bool:
        object_instance = cls.get_entry_instance()
        general_black_list_email = object_instance.general_black_list.get("email", [])
        return email in general_black_list_email

    @classmethod
    def can_send_money(cls, email: str) -> bool:
        object_instance = cls.get_entry_instance()
        send_money_black_list = object_instance.send_money.get("email", [])
        return email in send_money_black_list

    @classmethod
    def failled_wallet_update(cls, email: str) -> bool:
        object_instance = cls.get_entry_instance()
        wallet_entry_black_list = object_instance.wallet_entry.get("email", [])
        return email in wallet_entry_black_list

    @classmethod
    def blacklist_user_email(cls, user_email, blacklist_type="FUNDS_TRANSFER"):

        entry_instance = cls.get_entry_instance()

        if blacklist_type == "FUNDS_TRANSFER":
            send_money_black_list = entry_instance.send_money.get("email", [])
            if user_email not in send_money_black_list:
                send_money_black_list.append(user_email)
                entry_instance.save()
                return

        elif blacklist_type == "ACCOUNT_CREATION":
            wallet_entry_black_list = entry_instance.wallet_entry.get("email", [])
            if user_email not in wallet_entry_black_list:
                wallet_entry_black_list.append(user_email)
                entry_instance.save()
                return

        else:
            general_black_list = entry_instance.general_black_list.get("email", [])
            if user_email not in general_black_list:
                general_black_list.append(user_email)
                entry_instance.save()
                return

    @classmethod
    def remove_user_eamil_from_blacklist(
        cls, user_email, blacklist_type="FUNDS_TRANSFER"
    ):

        entry_instance = cls.get_entry_instance()
        if blacklist_type == "FUNDS_TRANSFER":
            send_money_black_list = entry_instance.send_money.get("email", [])
            if user_email in send_money_black_list:
                send_money_black_list.remove(user_email)
                entry_instance.save()
                return

        elif blacklist_type == "ACCOUNT_CREATION":
            wallet_entry_black_list = entry_instance.wallet_entry.get("email", [])
            if user_email in wallet_entry_black_list:
                wallet_entry_black_list.remove(user_email)
                entry_instance.save()
                return

        else:
            general_black_list = entry_instance.general_black_list.get("email", [])
            if user_email in general_black_list:
                general_black_list.remove(user_email)
                entry_instance.save()
                return


class OTP(BaseModel):
    """
    Model representing a One-Time Password (OTP).
    Relationships:
        objects (OTPManager): Custom manager for OTP objects.
    """

    type = models.CharField(
        max_length=255,
        help_text="The type of OTP being generated, e.g.,'REGISTRATION', e.t.c.",
    )
    recipient = models.CharField(
        max_length=255,
        help_text="The recipient's identifier for whom the OTP is being generated.",
    )
    length = models.IntegerField(
        default=8, help_text="The length of the OTP to be generated (max=10)."
    )
    expiry_time = models.IntegerField(
        default=10, help_text="The validity time of the OTP in minutes (default=10)."
    )
    code = models.CharField(max_length=255, editable=False)
    is_used = models.BooleanField(default=False)

    objects = OTPManager()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "ONE TIME PASSWORD"
        verbose_name_plural = "ONE TIME PASSWORDS"

    def __str__(self) -> str:
        return f"{self.type} OTP sent to {self.recipient}"

    @property
    def time_valid(self) -> bool:
        """
        Property that checks if the object's created time is still within the valid time range.
        Returns:
            bool: True if the object's created time is within the valid time range, False otherwise.
        """
        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        return (
            True
            if (current_time - self.created_at) <= timedelta(minutes=self.expiry_time)
            else False
        )

    @classmethod
    def get_otp(
        cls,
        type: str,
        recipient: str,
        length: Optional[int] = 6,
        expiry_time: Optional[int] = 5,
    ) -> str:
        """
        Generate and retrieve a new OTP (One-Time Password) object.
        Returns:
            OTP: The newly created OTP object.
        """
        otp = cls.objects.create_otp(
            type=type, recipient=recipient, length=length, expiry_time=expiry_time
        )
        return otp

    @classmethod
    def verify_otp(cls, recipient: str, otp: str) -> dict:
        """
        Verify the OTP (One-Time Password) for the given recipient.
        Returns:
            dict:
            - a dictionary containing the verification status and message.
        """
        one_time_password = cls.objects.filter(
            recipient=recipient, is_used=False
        ).first()
        if one_time_password is None:
            return {"status": False, "message": "invalid or expired OTP."}
        else:
            if not one_time_password.time_valid:
                return {"status": False, "message": "invalid or expired OTP."}
            else:
                verified = check_password(otp, one_time_password.code)
                if not verified:
                    return {"status": False, "message": "invalid or expired OTP."}
                else:
                    one_time_password.is_used = True
                    one_time_password.save()
                    return {"status": True, "message": "OTP is valid for recipient."}


class Notification(BaseModel):
    from requisition.models import Company
    from stock_inventory.models import Branch

    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    branch = models.ForeignKey(
        Branch,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=25, choices=NotificationTypes.choices)
    is_read = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.title

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "NOTIFICATION"
        verbose_name_plural = "NOTIFICATIONS"

    @classmethod
    def mark_as_read(cls, id: list, company: Company):
        notification = cls.objects.filter(id__in=id, company=company, is_read=False)
        if notification is None:
            return None

        notification.update(is_read=True)
        # notification.save()
        return True


class PaystackPayment(BaseModel):
    confirmed = models.BooleanField(default=False)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    company = models.ForeignKey(
        "requisition.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    branch = models.ForeignKey(
        "stock_inventory.Branch",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    reference = models.CharField(max_length=255, null=True, blank=True)
    amount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    status = models.CharField(max_length=255, null=True, blank=True)
    payer_name = models.CharField(max_length=255, null=True, blank=True)
    channel = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    event = models.CharField(max_length=255, null=True, blank=True)
    gateway_response = models.CharField(max_length=255, null=True, blank=True)
    payment_id = models.CharField(max_length=255, null=True, blank=True)
    currency = models.CharField(
        max_length=3,
        choices=Currency.choices,
        default=Currency.NAIRA,
    )
    payload = models.TextField(null=True, blank=True)
    confirmation_payload = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PAYSTACK PAYMENT"
        verbose_name_plural = "PAYSTACK PAYMENTS"

    @classmethod
    def generate_payment_link(cls, email: str, amount: float, reference: str):
        paystack_manager = Paystack()
        response = paystack_manager.get_authorization_url(
            email=email, amount=amount, reference=reference
        )
        if response.get("status"):
            return {
                "status": True,
                "details": "payment link authorized.",
                "payment_link": response.get("data").get("authorization_url"),
            }
        else:
            return {
                "status": False,
                "details": "error occurred while generating a payment link.",
                "payment_link": "",
            }

    @classmethod
    def create_event_transaction(cls, data: dict):
        from core.tasks import process_sales_online_payments, tie_paid_modules_to_subscription

        channel = data.get("data").get("channel")
        payer_name = (
            data.get("data").get("authorization").get("account_name")
            if channel == "card"
            else data.get("data").get("authorization").get("sender_name")
        )

        try:
            event = PaystackPayment.objects.create(
                reference=data.get("data").get("reference"),
                amount=int(data.get("data").get("amount")) / 100,
                status=data.get("data").get("status"),
                payer_name=payer_name,
                channel=channel,
                email=data.get("data").get("customer").get("email"),
                gateway_response=data.get("data").get("gateway_response"),
                event=data.get("event"),
                payment_id=data.get("data").get("id"),
                payload=json.dumps(data),
            )
            process_sales_online_payments.delay(event_id=event.id)
            tie_paid_modules_to_subscription.delay(event_id=event.id)


        except Exception as e:
            print(str(e))
            event = None
        return event

    # def create_subscription_transaction(cls):
    #     pass

    @classmethod
    def verify_event_transaction(cls, reference: str):
        paystack_manager = Paystack()
        response = paystack_manager.verify_payment(reference=reference)
        if response.get("status"):
            return {"status": True, "response": response}
        else:
            return {"status": False, "response": response}


class CampaignSenderId(BaseModel):
    company = models.ForeignKey("requisition.Company", on_delete=models.PROTECT)
    sender_id = models.CharField(max_length=20)
    sample_message = models.CharField(max_length=1000)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CAMPAIGN SENDER ID"
        verbose_name_plural = "CAMPAIGN SENDER IDS"

    def __str__(self) -> str:
        return f"{self.company}:{self.sender_id}"

    @classmethod
    def retrieve_sender_id(cls, sender_id, company_id):
        sender = cls.objects.filter(id=sender_id, company__id=company_id).first()
        if sender:
            campaign_sender = sender
        else:
            campaign_sender = None
        return campaign_sender

    @classmethod
    def create_sender_id(cls, company, sender_id, sample_message, user):
        cls.objects.create(
            company=company,
            sender_id=sender_id,
            sample_message=sample_message,
            created_by=user,
        )


class CampaignBatch(BaseModel):
    company = models.ForeignKey("requisition.Company", on_delete=models.PROTECT)
    sender = models.ForeignKey(CampaignSenderId, on_delete=models.PROTECT)
    message = models.CharField(max_length=1000)
    priority_route = models.BooleanField(default=False)
    campaign_name = models.CharField(max_length=100)
    flash_route = models.BooleanField(default=False)
    send_later = models.BooleanField(default=False)
    send_date = models.CharField(max_length=100, null=True, blank=True)
    total_amount = models.FloatField(default=0)
    request = models.TextField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CAMPAIGN BATCH"
        verbose_name_plural = "CAMPAIGN BATCHES"

    def __str__(self) -> str:
        return f"{self.company}:{self.sender.sender_id}"

    @classmethod
    def create_campaign_batch(
        cls,
        sender_id,
        message,
        company,
        priority_route,
        campaign_name,
        flash_route,
        send_later,
        send_date,
        user,
        amount,
    ):
        campaign_batch = cls.objects.create(
            sender=sender_id,
            message=message,
            company=company,
            priority_route=priority_route,
            campaign_name=campaign_name,
            flash_route=flash_route,
            send_later=send_later,
            send_date=send_date,
            created_by=user,
            total_amount=amount,
        )
        return campaign_batch


class CampaignSmsTransaction(BaseModel):
    company = models.ForeignKey("requisition.Company", on_delete=models.PROTECT)
    campaign_batch = models.ForeignKey(CampaignBatch, on_delete=models.PROTECT)
    amount = models.FloatField(default=0)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CAMPAIGN SMS TRANSACTION"
        verbose_name_plural = "CAMPAIGN SMS TRANSACTION"


class CampaignMessage(BaseModel):
    CAMPAIGN_MESSAGE_STATUS = [
        ("PENDING", "PENDING"),
        ("SENT", "SENT"),
        ("FAILED", "FAILED"),
        ("DELIVERED", "DELIVERED"),
    ]

    sender = models.ForeignKey(CampaignSenderId, on_delete=models.PROTECT)
    campaign_name = models.CharField(max_length=100)
    message = models.CharField(max_length=1000)
    phone_number = models.CharField(max_length=25)
    status = models.CharField(
        max_length=200,
        choices=CAMPAIGN_MESSAGE_STATUS,
        default="PENDING",
        null=True,
        blank=True,
    )
    company = models.ForeignKey("requisition.Company", on_delete=models.PROTECT)
    priority_route = models.BooleanField(default=False)
    flash_route = models.BooleanField(default=False)
    send_later = models.BooleanField(default=False)
    send_date = models.CharField(max_length=100, null=True, blank=True)
    campaign_batch = models.ForeignKey(CampaignBatch, on_delete=models.PROTECT)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CAMPAIGN MESSAGE"
        verbose_name_plural = "CAMPAIGN MESSAGES"

    def __str__(self) -> str:
        return f"{self.company}:{self.sender.sender_id}"

    @classmethod
    def send_campaign_message(
        cls,
        user: User,
        company,
        sender_id: CampaignSenderId,
        message: str,
        campaign_name: str,
        contacts: list,
        priority_route: bool,
        flash_route: bool,
        send_later: False,
        send_date: Optional[datetime] = None,
        amount=float,
    ):
        # create a campaign batch for the contact list
        campaign_batch = CampaignBatch.create_campaign_batch(
            sender_id,
            message,
            company,
            priority_route,
            campaign_name,
            flash_route,
            send_later,
            send_date,
            user,
            amount,
        )

        CampaignSmsTransaction.objects.create(
            company=company,
            campaign_batch=campaign_batch,
            amount=campaign_batch.total_amount,
        )

        # register each contact data to the database
        for contact in contacts:
            cls.objects.create(
                sender=sender_id,
                message=message,
                phone_number=contact,
                status="PENDING",
                company=company,
                priority_route=priority_route,
                campaign_name=campaign_name,
                flash_route=flash_route,
                send_later=send_later,
                send_date=send_date,
                created_by=user,
                campaign_batch=campaign_batch,
            )
        WhisperSms.send_campaign_sms(
            campaign_name,
            contacts,
            flash_route,
            message,
            priority_route,
            send_date,
            send_later,
            sender_id.sender_id,
            campaign_batch=campaign_batch,
        )

        return True


class SalesTeamShift(BaseModel):
    company = models.ForeignKey("requisition.Company", on_delete=models.PROTECT)
    branch = models.ForeignKey(
        "stock_inventory.Branch",
        on_delete=models.SET_NULL,
        related_name="sales_shift_branch",
        null=True,
        blank=True,
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    start_cash_in_hand = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)]
    )
    end_cash_in_hand = models.FloatField(
        default=0.0, validators=[MinValueValidator(0.0)]
    )
    work_duration = models.DurationField(null=True, blank=True)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES TEAM SHIFT"
        verbose_name_plural = "SALES TEAM SHIFTS"

    def __str__(self) -> str:
        return f"{self.company}"


class SalesTeamBreakShift(BaseModel):
    company = models.ForeignKey("requisition.Company", on_delete=models.PROTECT)
    branch = models.ForeignKey(
        "stock_inventory.Branch",
        on_delete=models.SET_NULL,
        related_name="sales_shift_break_branch",
        null=True,
        blank=True,
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    user_shift = models.ForeignKey(
        SalesTeamShift, on_delete=models.CASCADE, null=True, blank=True
    )
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    break_duration = models.DurationField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES TEAM BREAK SHIFT"
        verbose_name_plural = "SALES TEAM BREAK SHIFTS"

    def __str__(self) -> str:
        return f"{self.company}"


class SalesUserRegistrationDump(BaseModel):
    username = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    email = models.CharField(max_length=255, null=True, blank=True)
    default_company = models.ForeignKey(
        "requisition.Company",
        on_delete=models.SET_NULL,
        related_name="sales_reg_dump_company",
        null=True,
        blank=True,
    )
    default_branch = models.ForeignKey(
        "stock_inventory.Branch",
        on_delete=models.SET_NULL,
        related_name="sales_reg_dump_branch",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SALES REGISTER DUMP"
        verbose_name_plural = "SALES REGISTER DUMPS"

    def __str__(self) -> str:
        return f"{self.username}"


class IssueLog(models.Model):
    """
    Model to track issues and their resolutions related to users
    """
    user = models.ForeignKey(
        "User",
        on_delete=models.CASCADE,
        related_name='issue_logs',
        help_text="User who the issue is about"
    )
    description = models.TextField(
        help_text="Description of the issue and its resolution"
    )
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Issue Log - {self.user.email} - {self.date_created}"

    class Meta:
        ordering = ['-date_created']
        verbose_name = "ISSUE LOG"
        verbose_name_plural = "ISSUE LOGS"


class VfdMeeting(models.Model):
    work_email = models.EmailField(max_length=255)
    full_name = models.CharField(max_length=255)
    phone_number = models.CharField(max_length=255)
    country = models.CharField(max_length=255)
    employee_headcount = models.CharField(max_length=255, choices=Employee_Headcount.choices)
    availability = models.DateTimeField(null=True, blank=True)
    product_vertical = models.JSONField(default=list)
    meeting_link = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    paybox_agent_phone_number = models.CharField(max_length=100, blank=True, null=True)
    paybox_agent_name = models.CharField(max_length=100, blank=True, null=True)
    is_assigned = models.BooleanField(default=False)
    company_name = models.CharField(max_length=255, blank=True, null=True)
    class Meta:
        verbose_name = "VFD MEETING"
        verbose_name_plural = "VFD MEETINGS"

    def __str__(self):
        return f"VFD meeting - {self.work_email} - {self.created_at}"


class SalesAgents(models.Model):
    name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    phone_number = models.CharField(max_length=255)
    created_at = models.DateTimeField(
        auto_now_add=True,
        null=True,
        blank=True,
    )
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        verbose_name = "SALES AGENT"
        verbose_name_plural = "SALES AGENTS"

    def __str__(self):
        return self.name
    
    def soft_delete(self):
        """
        Soft deletes the object by setting the 'is_active' attribute to False
        and the 'is_deleted' attribute to True.
        NOTE:
        - It marks the object as inactive and deleted without removing it from the database.
        """
        self.is_active = False
        self.is_deleted = True
        self.save()

        
class CampaignLead(models.Model):
    unique_id = models.CharField(max_length=3000, unique=True)
    visited_page = models.BooleanField(default=False)
    opened_video = models.BooleanField(default=False)
    opened_form = models.BooleanField(default=False)
    submitted_form_data = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.unique_id

    class Meta:
        verbose_name = "PAYBOX ADS CAMPAIGN LEAD"
        verbose_name_plural = "PAYBOX ADS CAMPAIGN LEADS"


class OfflineApplication(BaseModel):
    from core.helpers.func import local_store_upload

    app_type = models.CharField(max_length=7, choices=SupportedAppChoices.choices)
    version = models.CharField(max_length=25)
    app = models.FileField(upload_to=local_store_upload)
    uploaded_by = models.ForeignKey(User, on_delete=models.PROTECT)

    def __str__(self):
        return f"{self.app_type} - {self.version}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "OFFLINE APPLICATION"
        verbose_name_plural = "OFFLINE APPLICATIONS"



class Guarantor(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    email = models.EmailField(max_length=255, unique=True)
    guarantor_name = models.CharField(max_length=255)
    guarantor_phone_number = models.CharField(max_length=20)
    guarantor_email = models.EmailField(max_length=255)
    guarantor_occupation = models.CharField(max_length=255)
    guarantor_address = models.TextField()
    next_of_kin_name = models.CharField(max_length=255)
    next_of_kin_relationship = models.CharField(max_length=255)
    next_of_kin_phone_number = models.CharField(max_length=20)
    next_of_kin_address = models.TextField()
    verified = models.BooleanField(default=False)
    

    def __str__(self):
        return self.user.username
