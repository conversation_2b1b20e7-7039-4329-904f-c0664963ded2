import base64
import bin<PERSON><PERSON><PERSON>

import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework import permissions, status
from rest_framework.authentication import BaseAuthentication, get_authorization_header
from rest_framework.exceptions import APIException, AuthenticationFailed
from rest_framework.permissions import BasePermission

from core.helpers.apis.request_cls import LibertyPayPlus
from core.tasks import update_kyc_details
from helpers.reusable_functions import (
    get_account_details,
    get_wema_account_details,
)


User = get_user_model()


class CustomUserAuthentication(BaseAuthentication):
    keyword = "Bearer"

    @staticmethod
    def decode_access_token(access_token: str, signature: str) -> dict:
        try:
            verification = jwt.decode(access_token, signature, algorithms=["HS256"])
            return {"status": True, "verification": verification}
        except jwt.exceptions.PyJWTError as e:
            return {"status": False, "error_message": str(e)}

    def authenticate(self, request):
        token = request.headers.get("Authorization")
        if not token:
            raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)
        if not token.startswith(self.keyword):
            raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

        # verify token
        token = token.split(" ")[1]  # Remove the token type prefix
        # print(token)
        token_details = self.decode_access_token(
            access_token=token, signature=settings.SECRET_KEY
        )
        # print(token_details)

        if token_details["status"] is False:
            raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

        return self.authenticate_credentials(token, request)

    def authenticate_credentials(self, auth_token, request):
        token_details = self.decode_access_token(
            access_token=auth_token, signature=settings.SECRET_KEY
        )
        if token_details.get("status", False):
            user_id = token_details["verification"]["user_id"]
            try:
                # Retrieve the user based on the custom key
                user = User.objects.get(liberty_pay_id=user_id)

                """
                Update account details:
                This update happens either because the user account details
                wasn't created successfully or the bvn was ignored on creation. for users 
                that signed up with just ordinary email
                """

                if (user.account_no is None and user.bvn_number is None) or (
                    user.account_no is not None and user.bvn_number is None) or (
                    user.kyc_level is None
                ):

                    liberty_pay_plus_response = LibertyPayPlus.request_user_details(
                        auth_token=auth_token
                    )

                    if liberty_pay_plus_response.get("status", False):
                        json_response = liberty_pay_plus_response["response"]
                        user_data = json_response.get("user_data")

                        if user_data is not None:

                            accounts_data = json_response.get("accounts_data")
                            liberty_pay_acct_detail = get_account_details(
                                account_data=accounts_data
                            )
                            kyc_level = user_data.get("kyc_level")
                            if liberty_pay_acct_detail is not None:
                                account_no = liberty_pay_acct_detail.get(
                                    "account_number"
                                )
                                account_name = liberty_pay_acct_detail.get(
                                    "account_name"
                                )
                                bank_name = liberty_pay_acct_detail.get("bank_name")
                                bvn_number = user_data.get("bvn_number")
                            else:
                                account_no = None
                                account_name = None
                                bank_name = None
                                bvn_number = None

                            user.account_no = account_no
                            user.bvn_number = bvn_number
                            user.account_name = account_name
                            user.bank = bank_name
                            user.bank_code = "999999"
                            user.kyc_level = kyc_level
                            user.save()
                            update_kyc_details.delay(user_id=user.id)
                            # update_kyc_details.apply_async(
                            #     queue="kyc_update",
                            #     kwargs={"user": user.id},
                            # )

            except User.DoesNotExist:
                # check liberty pay plus for user details

                liberty_pay_plus_response = LibertyPayPlus.request_user_details(
                    auth_token=auth_token
                )
                if liberty_pay_plus_response.get("status", False):
                    json_response = liberty_pay_plus_response["response"]
                    user_data = json_response.get("user_data")
                    if (
                        user_data is not None
                    ):  # create user on liberty requisition backend
                        status = bool(user_data.get("is_active"))

                        accounts_data = json_response.get("accounts_data")
                        liberty_pay_acct_detail = get_account_details(
                            account_data=accounts_data
                        )
                        libertypay_wema_detail = get_wema_account_details(
                            account_data=accounts_data
                        )
                        if liberty_pay_acct_detail is not None:
                            account_no = liberty_pay_acct_detail.get("account_number")
                            account_name = liberty_pay_acct_detail.get("account_name")
                            bank_name = liberty_pay_acct_detail.get("bank_name")
                        if libertypay_wema_detail is not None:
                            wema_account_number = libertypay_wema_detail.get("account_number")
                            wema_account_name = libertypay_wema_detail.get("account_name")
                        else:
                            account_no = None
                            account_name = None
                            bank_name = None
                            wema_account_number = None
                            wema_account_name = None
                        email = user_data.get("email")
                        user_instance_with_email = User.objects.filter(email=email)
                        if not user_instance_with_email.exists():

                            user = User.objects.create(
                                liberty_pay_id=user_data.get("id"),
                                liberty_pay_customer_id=user_data.get("customer_id"),
                                phone_no=user_data.get("phone_number"),
                                email=email,
                                first_name=user_data.get("first_name"),
                                last_name=user_data.get("last_name"),
                                bvn_number=user_data.get("bvn_number"),
                                bvn_first_name=user_data.get("bvn_first_name"),
                                bvn_last_name=user_data.get("bvn_last_name"),
                                account_no=account_no,
                                account_name=account_name,
                                street=user_data.get("street"),
                                state=user_data.get("state"),
                                lga=user_data.get("lga"),
                                nearest_landmark=user_data.get("nearest_landmark"),
                                gender=user_data.get("gender"),
                                is_active=status,
                                liberty_pay_customer_status=status,
                                liberty_pay_user_type=user_data.get("type_of_user"),
                                bank=bank_name,
                                bank_code="999999",
                                wema_account_number=wema_account_number,
                                wema_account_name=wema_account_name,
                                kyc_level=user_data.get("kyc_level"),
                            )
                        else:
                            user_instance_with_email.update(
                                liberty_pay_id=user_data.get("id"),
                                liberty_pay_customer_id=user_data.get("customer_id"),
                                phone_no=user_data.get("phone_number"),
                                email=email,
                                first_name=user_data.get("first_name"),
                                last_name=user_data.get("last_name"),
                                bvn_number=user_data.get("bvn_number"),
                                bvn_first_name=user_data.get("bvn_first_name"),
                                bvn_last_name=user_data.get("bvn_last_name"),
                                account_no=account_no,
                                account_name=account_name,
                                street=user_data.get("street"),
                                state=user_data.get("state"),
                                lga=user_data.get("lga"),
                                nearest_landmark=user_data.get("nearest_landmark"),
                                gender=user_data.get("gender"),
                                is_active=status,
                                liberty_pay_customer_status=status,
                                liberty_pay_user_type=user_data.get("type_of_user"),
                                bank=bank_name,
                                bank_code="999999",
                                wema_account_number=wema_account_number,
                                wema_account_name=wema_account_name,
                                kyc_level=user_data.get("kyc_level"),
                            )
                            user = user_instance_with_email.first()

                    else:
                        print("user not found")
                        raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

                else:
                    print("NEGATIVE PAY PLUS RESPONSE")
                    raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

            #### add authentication for Sales User
            except ValueError:
                try:
                    user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    raise AuthenticationFailed("User inactive or deleted.")

            if not user.is_active:
                raise AuthenticationFailed("User inactive or deleted.")
            # Save the user ID in the request for future use
            request.user_id = user_id

            return user, auth_token

    def authenticate_header(self, request):
        """
        Return a custom WWW-Authenticate header with a 401 status code.
        Args:
            request (HttpRequest): The current request object.
        Returns:
            str: The custom WWW-Authenticate header.
        """
        return 'Bearer realm="api", error="invalid_token", error_description="Authentication required", status_code=401'


class SuperUserException(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "You do not have the necessary permission to perform this action.",
    }
    default_code = "Not permitted"


class IsSuperUser(permissions.BasePermission):

    def has_permission(self, request, view):
        user = request.user

        if user.is_superuser is True:
            return True
        else:
            raise SuperUserException


class IsStaff(permissions.BasePermission):

    def has_permission(self, request, view):
        user = request.user
        if user.is_staff is True:
            return True
        else:
            raise SuperUserException


class CustomBasicAuthentication(BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.headers.get("Authorization")

        if not auth_header:
            return None

        try:
            auth_type, credentials = auth_header.split(" ")
            if auth_type.lower() != "basic":
                return None

            decoded_credentials = base64.b64decode(credentials).decode("utf-8")
            username, password = decoded_credentials.split(":")

        except (ValueError, TypeError, binascii.Error):
            raise AuthenticationFailed("Invalid basic authentication credentials")

        try:
            user = User.objects.get(username=username)
            if not user.check_password(password):
                raise AuthenticationFailed("Invalid username/password")

        except User.DoesNotExist:
            raise AuthenticationFailed("Invalid username/password")

        return (user, None)


class IsAuthenticatedOrCustomBasic(BasePermission):
    def has_permission(self, request, view):
        # Check if the user is authenticated via session authentication
        if request.user and request.user.is_authenticated:
            return True

        # Check for custom basic authentication
        auth_header = get_authorization_header(request).split()
        if auth_header and auth_header[0].lower() == b"basic":
            # Perform the basic authentication check
            try:
                user_auth_tuple = CustomBasicAuthentication().authenticate(request)
                if user_auth_tuple is not None:
                    request.user = user_auth_tuple[0]  # Update the request user
                    return True
            except Exception as e:
                # Log the exception or handle it as needed
                pass

        return False


class CustomSalesAuthentication(BaseAuthentication):
    bearer_keyword = "Bearer"
    basic_keyword = "Basic"

    @staticmethod
    def decode_access_token(access_token: str, signature: str) -> dict:
        try:
            verification = jwt.decode(access_token, signature, algorithms=["HS256"])
            return {"status": True, "verification": verification}
        except jwt.exceptions.PyJWTError as e:
            return {"status": False, "error_message": str(e)}

    def authenticate(self, request):
        auth_header = get_authorization_header(request).split()

        if not auth_header:
            raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

        auth_type = auth_header[0].decode("utf-8").lower()

        if auth_type == self.bearer_keyword.lower():
            return self._authenticate_bearer(auth_header[1].decode("utf-8"), request)
        elif auth_type == self.basic_keyword.lower():
            return self._authenticate_basic(auth_header[1].decode("utf-8"))

        raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

    def _authenticate_bearer(self, token, request):
        # verify token
        token_details = self.decode_access_token(
            access_token=token, signature=settings.SECRET_KEY
        )

        if token_details["status"] is False:
            raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

        return self.authenticate_credentials(token, request)

    def _authenticate_basic(self, credentials):
        try:
            decoded_credentials = base64.b64decode(credentials).decode("utf-8")
            username, password = decoded_credentials.split(":")
        except (ValueError, TypeError, binascii.Error):
            raise AuthenticationFailed("Invalid basic authentication credentials")

        try:
            user = User.objects.get(username=username, sales_is_deleted=False)
            if not user.default_company:
                raise AuthenticationFailed("user does not belong to a company")
            if not user.default_branch:
                raise AuthenticationFailed("user does not belong to a company")
            if user.sales_is_suspended:
                raise AuthenticationFailed("user suspended, contact admin")
            if not user.check_password(password):
                raise AuthenticationFailed("Invalid username/password")

        except User.DoesNotExist:
            raise AuthenticationFailed("Invalid username/password")

        return (user, None)

    def authenticate_credentials(self, auth_token, request):
        token_details = self.decode_access_token(
            access_token=auth_token, signature=settings.SECRET_KEY
        )
        if token_details.get("status", False):
            user_id = token_details["verification"]["user_id"]
            try:
                user = User.objects.get(liberty_pay_id=user_id)

                if (user.account_no is None and user.bvn_number is None) or (
                    user.account_no is not None and user.bvn_number is None
                ):
                    liberty_pay_plus_response = LibertyPayPlus.request_user_details(
                        auth_token=auth_token
                    )

                    if liberty_pay_plus_response.get("status", False):
                        json_response = liberty_pay_plus_response["response"]
                        user_data = json_response.get("user_data")

                        if user_data is not None:
                            accounts_data = json_response.get("accounts_data")
                            liberty_pay_acct_detail = get_account_details(
                                account_data=accounts_data
                            )
                            if liberty_pay_acct_detail is not None:
                                account_no = liberty_pay_acct_detail.get(
                                    "account_number"
                                )
                                account_name = liberty_pay_acct_detail.get(
                                    "account_name"
                                )
                                bank_name = liberty_pay_acct_detail.get("bank_name")
                                bvn_number = user_data.get("bvn_number")
                            else:
                                account_no = None
                                account_name = None
                                bank_name = None
                                bvn_number = None

                            user.account_no = account_no
                            user.bvn_number = bvn_number
                            user.account_name = account_name
                            user.bank = bank_name
                            user.bank_code = "999999"
                            user.save()

            except User.DoesNotExist:
                liberty_pay_plus_response = LibertyPayPlus.request_user_details(
                    auth_token=auth_token
                )
                if liberty_pay_plus_response.get("status", False):
                    json_response = liberty_pay_plus_response["response"]
                    user_data = json_response.get("user_data")
                    if user_data is not None:
                        status = bool(user_data.get("is_active"))

                        accounts_data = json_response.get("accounts_data")
                        liberty_pay_acct_detail = get_account_details(
                            account_data=accounts_data
                        )
                        if liberty_pay_acct_detail is not None:
                            account_no = liberty_pay_acct_detail.get("account_number")
                            account_name = liberty_pay_acct_detail.get("account_name")
                            bank_name = liberty_pay_acct_detail.get("bank_name")
                        else:
                            account_no = None
                            account_name = None
                            bank_name = None

                        user = User.objects.create(
                            liberty_pay_id=user_data.get("id"),
                            liberty_pay_customer_id=user_data.get("customer_id"),
                            phone_no=user_data.get("phone_number"),
                            email=user_data.get("email"),
                            first_name=user_data.get("first_name"),
                            last_name=user_data.get("last_name"),
                            bvn_number=user_data.get("bvn_number"),
                            bvn_first_name=user_data.get("bvn_first_name"),
                            bvn_last_name=user_data.get("bvn_last_name"),
                            account_no=account_no,
                            account_name=account_name,
                            street=user_data.get("street"),
                            state=user_data.get("state"),
                            lga=user_data.get("lga"),
                            nearest_landmark=user_data.get("nearest_landmark"),
                            gender=user_data.get("gender"),
                            is_active=status,
                            liberty_pay_customer_status=status,
                            liberty_pay_user_type=user_data.get("type_of_user"),
                            bank=bank_name,
                            bank_code="999999",
                        )
                    else:
                        raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

                else:
                    raise AuthenticationFailed(settings.TOKEN_ERROR_RESPONSE)

            if not user.is_active:
                raise AuthenticationFailed("User inactive or deleted.")
            request.user_id = user_id

            return user, auth_token

    def authenticate_header(self, request):
        return 'Bearer realm="api", error="invalid_token", error_description="Authentication required", status_code=401'
