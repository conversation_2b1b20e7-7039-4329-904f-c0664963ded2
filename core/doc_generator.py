import os
import openai
import re
from pathlib import Path
import tiktoken



openai.api_key = os.getenv("OPENAI_API_KEY")

def get_sensitive_files():
    """Return a list of hard-coded sensitive file patterns"""
    return [
        # Sensitive files (exact matches)
        "credentials.json",
        "client_secrets.json",
        "service_account.json",
        "token.json",
        ".env",
        ".env.local",
        "secrets.py",
        "config.py",
        "settings/local.py",
        "/check_project.sh",
        "/.gitignore",
        "/.flake8",
        "/.coverage",
        
        # Sensitive file patterns (regex)
        re.compile(r'.*\.key'),
        re.compile(r'.*\.pem'),
        re.compile(r'.*\.crt'),
        re.compile(r'.*\.secret'),
        re.compile(r'.*\.env.*'),  # Matches .env, .env.local, .env.prod, etc.
        re.compile(r'.*secret.*\.(py|json|yaml|yml)'),  # Matches any file with "secret" in the name
    ]


def get_default_ignore_patterns():
    """Return compiled regex patterns for common ignore cases"""
    return [
        # Version control systems
        re.compile(r'.*\.git/.*'),
        re.compile(r'.*\.hg/.*'),
        re.compile(r'.*\.svn/.*'),
        
        # Python specific
        re.compile(r'.*__pycache__/.*'),
        re.compile(r'.*\.py[cod]'),
        re.compile(r'.*\$py\.class'),
        
        # Django specific
        re.compile(r'.*migrations/.*\.py'),
        re.compile(r'.*staticfiles/.*'),
        re.compile(r'.*media/.*'),
        
        # Environment files
        re.compile(r'.*venv/.*'),
        re.compile(r'.*\.env'),
        re.compile(r'.*\.venv'),
        
        # Build/output files
        re.compile(r'.*dist/.*'),
        re.compile(r'.*build/.*'),
        re.compile(r'.*output\.txt'),
        
        # IDE/Editor files
        re.compile(r'.*\.sw[op]'),
        re.compile(r'.*\.bak'),
        re.compile(r'.*\.tmp'),
    ]

# def read_gitignore(directory):
#     gitignore_path = os.path.join(directory, ".gitignore")
#     ignore_patterns = [
#         ".git", "venv", "./output.txt", ".coverage", "templates", "__pycache__", ".github", ".flake8", "./check_project.sh", "client_secrets.json", "CONTRIBUTING.md", "README.md", "manage.py", "gunicorn_config.py", "credentials.json", "receipt_1.png", "requirements.txt", "scopes.txt", "service_account.json", "token.json", "storage_backend.py", "token.json"
#         "./.git/", "./.git/.", ".\.git", 
#         ]
#     if os.path.exists(gitignore_path):
#         with open(gitignore_path, 'r', encoding='utf-8') as f:
#             for line in f:
#                 line = line.strip()
#                 if line and not line.startswith("#"):
#                     ignore_patterns.append(line)
#                     ignore_patterns.append(".gitignore")
#     return ignore_patterns

def parse_gitignore(gitignore_path):
    """Convert .gitignore patterns to regex"""
    patterns = []
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    # Convert .gitignore pattern to regex
                    pattern = line
                    
                    # Handle directory matching
                    if pattern.endswith('/'):
                        pattern = pattern[:-1] + '(/.*)?'
                    
                    # Handle wildcards
                    pattern = pattern.replace('.', r'\.')
                    pattern = pattern.replace('*', r'.*')
                    pattern = pattern.replace('?', r'.')
                    
                    # Handle absolute paths
                    if pattern.startswith('/'):
                        pattern = '^' + pattern[1:] + '(/.*)?$'
                    else:
                        pattern = '.*/' + pattern + '(/.*)?$'
                        
                    patterns.append(re.compile(pattern))
    return patterns

def should_ignore(path):
    """Check if path matches any ignore patterns"""
    # Normalize path to POSIX style
    posix_path = Path(path).as_posix()
    
    # Check sensitive files first (hard-coded)
    sensitive_files = get_sensitive_files()
    for pattern in sensitive_files:
        if isinstance(pattern, re.Pattern):
            if pattern.fullmatch(posix_path):
                return True
        elif posix_path.endswith(pattern):
            return True
    
    # Check default patterns and .gitignore patterns
    default_patterns = get_default_ignore_patterns()
    gitignore_patterns = parse_gitignore(".gitignore")
    all_patterns = default_patterns + gitignore_patterns
    
    for pattern in all_patterns:
        if pattern.fullmatch(posix_path):
            return True
    return False

def read_files_in_directory(directory, output_file):
    with open(output_file, 'w', encoding='utf-8') as out_f:
        for root, _, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)

                if not "models" in file_path: continue;
                if should_ignore(file_path):
                    print(f"Ignoring: {file_path}")  # Optional: Log ignored files
                    continue
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        out_f.write(f"\n--- Reading: {file_path} ---\n\n")
                        out_f.write(f.read() + "\n")
                except Exception as e:
                    out_f.write(f"Error reading {file_path}: {e}\n")


output_file = "output.txt"
doc_file = "documentation.txt"
read_files_in_directory(".", output_file)
config = {
    "openai": {
        "model": "gpt-4o",  
        "system_prompt": "You are an AI that generates a comprehensive backend django documentation based on file content."
    }
}

def print_word_length(output_file):
    with open(output_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    words = content.split()
    
    total_length = sum(len(word) for word in words)
    
    print(f"Total length of all words in '{output_file}': {total_length}")
    
print_word_length(output_file)

def generate_documentation(doc_file, config, output_file):
    with open(output_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        encoder = tiktoken.encoding_for_model(config['openai']['model'])
    except KeyError:
        encoder = tiktoken.get_encoding("cl100k_base")
    
    tokens = encoder.encode(content, disallowed_special=())
    total_tokens = len(tokens)
    
    max_tokens_per_chunk = 127800
    num_chunks = (total_tokens + max_tokens_per_chunk - 1) 

    chunks = []
    for i in range(num_chunks):
        start = i * max_tokens_per_chunk
        end = start + max_tokens_per_chunk
        if end > total_tokens:
            end = total_tokens
        chunks.append(encoder.decode(tokens[start:end]))
    
    for idx, chunk in enumerate(chunks):
        client = openai.OpenAI()
        response = client.chat.completions.create(
            model=config['openai']['model'],
            messages=[
                {"role": "system", "content": config['openai']['system_prompt']},
                {"role": "user", "content": f"Repository structure:\n\n{chunk}"}
            ]
        )
        
        base_name, ext = os.path.splitext(doc_file)
        numbered_doc = f"{base_name}#{idx+1}{ext}"
        
        with open(numbered_doc, 'w', encoding='utf-8') as doc_f:
            doc_f.write(response.choices[0].message.content)

generate_documentation(doc_file, config, output_file)
