from django.db.models import Q
from django.conf import settings


from core.models import ConstantTable
from payroll_app.services import valid_uuid_check
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.authentication import BasicAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import APIException
from rest_framework import status
from rest_framework import permissions
from django.utils.datastructures import MultiValueDictKeyError
from account.models import User

from payroll_app.models import CompanyEmployeeList
from requisition.models import Company


# from main.models import User


class NoRegistration(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "please register your account",
    }
    default_code = "Not permitted"


class AccountSuspended(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "account suspended please contact admin",
    }
    default_code = "Not permitted"


class CompanyNotVerified(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "company not verified",
    }
    default_code = "Not permitted"


class CompanyNotFound(APIException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = {
        "error": "error",
        "message": "company not found",
    }
    default_code = "Not permitted"


class CompanyNotVerified(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "message": "company not verified",
    }
    default_code = "Not permitted"


class EmptyCompanyIDparams(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "you must pass in the company_uuid",
    }
    default_code = "Not permitted"


class NullCompanyIDparams(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "company_uuid cannot be empty",
    }
    default_code = "Not permitted"


class NoPermission(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "you are not permitted to perform this action",
    }
    default_code = "Not permitted"


class IncorrectPin(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "incorrect pin",
    }
    default_code = "Not permitted"


class NoPin(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "create user pin first",
    }
    default_code = "Not permitted"


class HasPin(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "user already have pin",
    }
    default_code = "Not permitted"


class NoInstantWagePermission(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "company cannot access instant wage at this time",
    }
    default_code = "Not permitted"


class EmployeeSuspended(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "you have been suspended, kindly contact your HR!",
    }
    default_code = "Not permitted"


class EmployeeInactive(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "you are currently inactive, kindly contact your HR!",
    }
    default_code = "Not permitted"


class InvalidCompanyUUID(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = {
        "error": "error",
        "params": "invalid ID type provided for company",
    }
    default_code = "Not permitted"

class NoSeedsandPenniesVerification(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = {
        "error": "error",
        "params": "You need to verify your phone number and guarantors phone number",
    }
    default_code = "Not permitted"


class UserIsActive(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):
        if request.user.is_active is False:
            raise AccountSuspended()
        else:
            return True


class EmployeeIsActive(permissions.BasePermission):
    """
    View based permission employee is active.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_deleted=False
            ).first()
            if employee:
                if employee.is_suspended:
                    raise EmployeeSuspended()
                elif not employee.is_active:
                    raise EmployeeInactive()
                else:
                    return True
            else:
                raise NoPermission()


class EmployeePermission(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()

        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                        
                    if (employee.employee_role.can_add_member is True) or (
                        employee.employee_role.can_edit_member is True) or (
                        employee.employee_role.can_delete_member is True
                    ):
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


# class CanAddEditDeleteEmployeePermission(permissions.BasePermission):
#     """
#     View based permission user is active.
#     """

#     def has_permission(self, request, view):
#         company_uuid = request.query_params.get('company_uuid')

#         try:
#             company_ins = Company.objects.get(id=company_uuid, is_active=True)
#         except Company.DoesNotExist:
#             raise CompanyNotFound()
#         if company_ins.user == request.user:
#             return True
#         else:
#             employee = CompanyEmployeeList.objects.filter(employee=request.user, company=company_ins, is_active=True).first()
#             if employee:
#                 if employee.can_add_member:
#                     return True
#                 elif employee.can_delete_member:
#                     return True
#                 elif employee.can_edit_member:
#                     return True
#                 else:
#                     raise NoPermission()
#             else:
#                 raise NoPermission()


class UserHasPin(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):
        if (
            request.user.requisition_transaction_pin is None
            or request.user.requisition_transaction_pin == ""
        ):
            raise NoPin()
        else:
            return True


class CheckPin(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):
        if request.user.requisition_transaction_pin:
            raise HasPin()
        else:
            return True


class CanAddEmployeePermission(permissions.BasePermission):
    """
    View based permission user can add employee to a company.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_add_member:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class CanEditEmployeePermission(permissions.BasePermission):
    """
    View based permission user can edit an existing employee in a company.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_member:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class CanDeleteEmployeePermission(permissions.BasePermission):
    """
    View based permission user can delete an employee from a company.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_delete_member:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class CanRunPayrollPermission(permissions.BasePermission):
    """
    View based permission user can run payroll.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_run_payroll:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class PayrollApprovalPermission(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_approve:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class DeletePayrollPermission(permissions.BasePermission):
    """
    View based permission user can delete running payroll in a company.
    """

    def has_permission(self, request, view):
        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_delete_payroll:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class PayrollDisbursePermission(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_disburse:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class AdminPermission(permissions.BasePermission):
    """
    View based permission user is an Admin or Owner.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()        
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, employee=request.user, is_active=True
            ).first()
            if employee:
                if employee.user_role == "ADMIN":
                    return True
                else:
                    raise NoPermission()
            else:
                raise NoPermission()


class CompanyPermission(permissions.BasePermission):
    """
    View based permission user is an Admin or Owner.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.instant_wage:
            return True
        else:
            raise NoInstantWagePermission()


class ManagerPermission(permissions.BasePermission):
    """
    View based permission user is an Admin or Owner.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()        
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()

        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                company__id=company_uuid, employee=request.user, is_active=True
            ).first()
            if employee:
                if employee.manager_role == "MANAGER":
                    return True
                else:
                    raise NoPermission()
            else:
                raise NoPermission()

class CanCreatePermissionRoles(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_create_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanEditPermissionRoles(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeletePermissionRoles(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        company_uuid = request.query_params.get("company_uuid")

        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_delete_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanCreateDepartmentRoles(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_create_department_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanEditDepartmentRoles(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_department_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeleteDepartmentRoles(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_delete_department_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanCreateDepartment(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_create_department:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanEditDepartment(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_department:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeleteDepartment(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_delete_department:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeployDepartment(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_deploy_department:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeployDepartmentRole(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_deploy_department_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeployEmployeeRole(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_deploy_employee_role:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanCreateLeaveType(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_create_leave_type:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanEditLeaveType(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_leave_type:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeleteLeaveType(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_delete_leave_type:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanCreateLeavePolicy(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_create_leave_policy:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanEditLeavePolicy(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_leave_policy:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanDeleteLeavePolicy(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_leave_policy:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanApproveLeave(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_approve_leave:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class CanEditPayrollSettings(permissions.BasePermission):
    """
    View based permission user is active.
    """

    def has_permission(self, request, view):

        company_uuid = request.query_params.get("company_uuid")
        if not valid_uuid_check(company_uuid):
            raise InvalidCompanyUUID()
        try:
            company_ins = Company.objects.get(id=company_uuid, is_active=True)
        except Company.DoesNotExist:
            raise CompanyNotFound()
        if company_ins.user == request.user:
            return True
        else:
            employee = CompanyEmployeeList.objects.filter(
                employee=request.user, company=company_ins, is_active=True
            ).first()
            if employee:
                if employee.employee_role:
                    if employee.employee_role.can_edit_payroll_settings:
                        return True
                    else:
                        raise NoPermission()
                else:
                    raise NoPermission()
            else:
                raise NoPermission()
            
class VerifyCompanyEmployeeSendMoney(permissions.BasePermission):
    """
    View based permission user is has done verification.
    """

    def has_permission(self, request, view):
        const_table = ConstantTable.get_constant_instance()
        verify_company_id = const_table.verify_company_employee_id

        if not valid_uuid_check(verify_company_id):
            return True
        else:
            try:
                company_ins = Company.objects.get(id=verify_company_id)
            except Company.DoesNotExist:
                return True
            if company_ins.user == request.user:
                return True
            else:
                if verify_company_id is not None:
                    employee = CompanyEmployeeList.objects.filter(
                        employee=request.user, company=company_ins
                    )
                    if employee:
                        if employee.filter(is_phone_number_verified=True, is_guarantor_phone_number_verified=True):
                            return True
                        else:
                            raise NoSeedsandPenniesVerification()
                    else:
                        return True
                else:
                    return True