from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.dispatch import receiver
from core.tasks import send_email

from payroll_app.apis.func import generate_readable_employee_id
from payroll_app.models import CompanyDepartmentSettings, CompanyEmployeeList, CompanyEmployeeOnboardingForm, OtherDeductionSettings, OtherDependencySettings, PensionFundAdminSettings, SalaryComponentSettings
from payroll_app.tasks import generate_staff_id
from requisition.models import Team, TeamMember, Company

User = get_user_model()


# @receiver(post_save, sender=CompanyDetailsData)
# def update_payroll_date(sender, instance, created, **kwargs):
#     if created:
#         # update payroll date on create
#         month = int(instance.payroll_month)
#         year = int(instance.payroll_year)
#         day = int(1)
#         payroll_date = datetime.date(year, month, day)
#         instance.payroll_date = payroll_date
#         instance.save()


@receiver(post_save, sender=CompanyEmployeeList)
def signal_company_employee_list_model(sender, instance, created, **kwargs):

    """This function creates a team and add all employees
        added to a payroll company as soon as the employee list is created
    """
    if created:
        autogenerated_id = generate_readable_employee_id(instance.id)
        instance.paybox_id = autogenerated_id
        if instance.company:
            staff_id = CompanyEmployeeList.add_generate_staff_id(date=instance.created_at, company=instance.company)
        instance.employee_staff_id = staff_id
        instance.save()
        company_instance = instance.company
        employer = instance.employer
        # employee = instance.employee
        employee_email = instance.employee_email
        employee_phone_number = instance.employee_phone_number
        if company_instance:
            team_name = f"{company_instance.company_name} Team"
            _team = Team.objects.filter(company=company_instance, team_name=team_name,
                                        team_type="PAYROLL").first()

            if not _team:
                _team_instance = Team.objects.create(user=employer,
                                                     company_id=company_instance.id,
                                                     team_name=team_name,
                                                     team_type="PAYROLL")

                _company = Company.objects.filter(id=company_instance.id).first()
                _company.teams.add(_team_instance)

                team_member = TeamMember.member_exists(email=employee_email, team_ins=_team_instance)

                if team_member is None:
                    user = User.user_exist(email=employee_email, phone_no=employee_phone_number)
                    member = TeamMember.objects.create(
                        email=employee_email,
                        phone_no=employee_phone_number,
                        member=user if user is not None else None,
                        team=_team_instance,
                        is_registered=True if user is not None else False,
                        status="NOT_JOINED" if not user else "ACTIVE",
                        role="MEMBER",
                        channel=_team_instance.channel
                    )
                    _team_instance.members.add(member)
            else:

                team_member = TeamMember.member_exists(email=employee_email, team_ins=_team)

                if team_member is None:
                    user = User.user_exist(email=employee_email, phone_no=employee_phone_number)
                    member = TeamMember.objects.create(
                        email=employee_email,
                        phone_no=employee_phone_number,
                        member=user if user is not None else None,
                        team=_team,
                        is_registered=True if user is not None else False,
                        status="NOT_JOINED" if not user else "ACTIVE",
                        role="MEMBER",
                        channel=_team.channel
                    )
                    _team.members.add(member)



    else:
        if instance.is_deleted:
            CompanyEmployeeOnboardingForm.objects.filter(employee_email=instance.employee_email, 
                                                         employee_company=instance.company,
                                                         is_deleted=True).update(is_deleted=True)
    #     if instance.is_active is True:
    #         payroll_user = CompanyEmployeeList.objects.filter(id=instance.id).first()
    #         if payroll_user is not None:
    #             payroll_user.employee_status = "ACTIVE"
    #             payroll_user.is_suspended = False
    #             payroll_user.save()

    #     elif instance.is_suspended is True:
    #         payroll_user = CompanyEmployeeList.objects.filter(id=instance.id).first()
    #         if payroll_user is not None:
    #             payroll_user.is_active = False
    #             payroll_user.employee_status = "SUSPENDED"
    #             payroll_user.save()
                
    #     elif instance.is_deleted is True:
    #         payroll_user = CompanyEmployeeList.objects.filter(id=instance.id).first()
    #         if payroll_user is not None:
    #             payroll_user.is_active = False
    #             payroll_user.is_suspended = False
    #             payroll_user.employee_status = "SUSPENDED"
    #             payroll_user.save()

        # if instance.is_active is True:

        #     instance.employee_status = "ACTIVE"
        #     instance.is_suspended = False
        #     # instance.save()
        # elif instance.is_suspended is True:
        #     instance.is_active = False
        #     instance.is_suspended = True
        #     instance.employee_status = "SUSPENDED"
        #     # instance.save()
        # elif instance.is_deleted is True:
        #     instance.is_active = False
        #     instance.is_suspended = False
        #     instance.employee_status = "SUSPENDED"
        #     # instance.save()


@receiver(post_save, sender=CompanyDepartmentSettings)
def signal_remove_deleted_employee_department(sender, instance, created, **kwargs):

    """This function removes all the associated department attached to a employee 
        of a company when the department is deleted.
    """
    if created:
       pass
    else:
        if instance.is_deleted:
            deleted_department = CompanyEmployeeList.objects.filter(employee_department=instance)
            for department in deleted_department:
                department.employee_department = None
                department.save()

@receiver(post_save, sender=CompanyEmployeeOnboardingForm)
def employee_onboarding_form_email(sender, instance, created, **kwargs):
    if created:
        send_email.delay(recipient=instance.employee_email, subject="Payroll Form Invite",
                        template_dir="employee_form_invite.html",
                        team_name="PAYROLL", company_name=instance.employee_company.company_name,
                        form_invite_link=f"https://www.home.paybox360.com/employee-invite-link/{instance.invite_id}")
               
@receiver(post_save, sender=OtherDeductionSettings)
def deduction_edit(sender, instance, created, **kwargs):
    if not created:
        if instance.calculation_type == "FIXED_AMOUNT" or instance.calculation_type == "GROSS_INCOME":
            instance.custom_name.remove(*instance.custom_name.all())
        if instance.is_deleted:
            OtherDependencySettings.objects.filter(deduction__id=instance.id).update(is_deleted=True, is_active=False)

@receiver(post_save, sender=SalaryComponentSettings)
def deduction_edit(sender, instance, created, **kwargs):
    if not created:
        if instance.is_deleted:
            all_others = OtherDeductionSettings.objects.filter(custom_name__id=instance.id, is_deleted=False)  
            for other in all_others:
                if other.custom_name.count()<=1:
                    other.is_active=False
                    other.custom_name.remove(instance)
                    other.save()
                else:
                    other.custom_name.remove(instance)
                    other.save()

            all_dependency = OtherDependencySettings.objects.filter(custom_name__id=instance.id, is_deleted=False)
            for dependency in all_dependency:
                if dependency.custom_name.count() <=1:
                    dependency.is_deleted=True
                    dependency.is_active=False
                    dependency.custom_name.remove(instance)
                    dependency.save()
                else:
                    dependency.custom_name.remove(instance)
                    dependency.save()

@receiver(post_save, sender=PensionFundAdminSettings)
def signal_remove_deleted_employee_pension_fund_admin(sender, instance, created, **kwargs):

    """This function removes all the associated Pension Administrators attached to a all employees

    """
    if created:
       pass
    else:
        if instance.is_deleted:
            deleted_pfa = CompanyEmployeeList.objects.filter(pension_fund_admin=instance)
            for pfa in deleted_pfa:
                pfa.pension_fund_admin = None
                pfa.save()