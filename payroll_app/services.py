from account.models import Transaction
from django.db.models import Sum, Count
from datetime import datetime
from payroll_app.apis.func import date_utility, get_percentage_diff
import calendar
from payroll_app.models import CompanyEmployeeList, PayrollTable
import uuid
import json
import requests
from django.conf import settings

filter_date = date_utility(datetime=datetime)
previous_month_start = filter_date.get("previous_month_start")
previous_month_end = filter_date.get("previous_month_end")
start_of_all_users = filter_date.get("start_of_all_transactions")
previous_year_current_month_start = filter_date.get("previous_year_current_month_start")
previous_year_current_month_end = filter_date.get("previous_year_current_month_end")
previous_year_current_previous_month = filter_date.get("previous_year_current_previous_month")
year_end = filter_date.get("year_end")
year_start = filter_date.get("year_start")
init_start = filter_date.get("init_start")
previous_year_current_following_month = filter_date.get("previous_year_current_following_month")
start_of_all_transactions = filter_date.get("start_of_all_transactions")
month_start = filter_date.get("month_start")
today = filter_date.get("today")
previous_day = filter_date.get("previous_day") 
previous_year_end = filter_date.get("previous_year_end") 
previous_year_start = filter_date.get("previous_year_start")
week_start = filter_date.get("week_start")
date_from = filter_date.get("date_from")
date_today = filter_date.get("date_today")
month_start = filter_date.get("month_start")
month_ago = filter_date.get("month_ago")

months_list = list(range(previous_year_current_month_start.month, (previous_year_current_month_start.month+13-previous_year_current_month_start.month)))
months_list2 = list(range(1, 12-(12-previous_year_current_month_start.month)+1))
months_list_names = [ 
                      f"{calendar.month_name[month][:3]} {previous_year_current_month_start.year}" 
                      for month in months_list
                    ]
months_list2_names = [f"{calendar.month_name[month][:3]} {today.year}" for month in months_list2]
months_list_names = months_list_names + months_list2_names
months_list = months_list + months_list2

class CompanyChart:
    def get_company_chart(request, start_date, end_date):
        user_role = request.user
        company_uuid = request.query_params.get("company_uuid")
        base_company_data = PayrollTable.objects.filter(company__id=company_uuid, payroll_deleted=False)
        all_possible_transactions_qs = base_company_data.filter(status="DISBURSED")

        #salary data
        total_salary_amount  =  all_possible_transactions_qs.aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        total_salary_til_previous_month  =  all_possible_transactions_qs.filter(payroll_date__range=[start_of_all_transactions, previous_month_end]).aggregate(Sum("payable_amount"))["payable_amount__sum"] or 0
        total_salary_amount_monthly_chart = all_possible_transactions_qs.filter(payroll_date__gte=previous_year_current_month_start).values("payroll_date__year", "payroll_date__month").annotate(total_amount = Sum("payable_amount")
                                                                                                                                                                                                        ).order_by("payroll_date__year")
        salary_amount_past_year_list = [(obj["payroll_date__year"], obj["payroll_date__month"], obj["total_amount"]) for obj in total_salary_amount_monthly_chart]
        total_salary_change = get_percentage_diff(
                                    previous=total_salary_til_previous_month,
                                    current=total_salary_amount
                                    )
        
        #tax
        total_tax_amount  =  all_possible_transactions_qs.aggregate(Sum("tax_amount"))["tax_amount__sum"] or 0
        total_tax_til_previous_month  =  all_possible_transactions_qs.filter(payroll_date__range=[start_of_all_transactions, previous_month_end]).aggregate(Sum("tax_amount"))["tax_amount__sum"] or 0
        total_tax_amount_monthly_chart = all_possible_transactions_qs.filter(payroll_date__gte=previous_year_current_month_start).values("payroll_date__year", "payroll_date__month").annotate(total_amount = Sum("tax_amount")
                                                                                                                                                                                                        ).order_by("payroll_date__year")
        tax_amount_past_year_list = [(obj["payroll_date__year"], obj["payroll_date__month"], obj["total_amount"]) for obj in total_tax_amount_monthly_chart]
        total_tax_change = get_percentage_diff(
                                    previous=total_tax_til_previous_month,
                                    current=total_tax_amount
                                    )
        
        #pension
        total_pension_amount  =  all_possible_transactions_qs.aggregate(Sum("pension_amount"))["pension_amount__sum"] or 0
        total_pension_til_previous_month  =  all_possible_transactions_qs.filter(payroll_date__range=[start_of_all_transactions, previous_month_end]).aggregate(Sum("pension_amount"))["pension_amount__sum"] or 0
        total_pension_amount_monthly_chart = all_possible_transactions_qs.filter(payroll_date__gte=previous_year_current_month_start).values("payroll_date__year", "payroll_date__month").annotate(total_amount = Sum("pension_amount")
                                                                                                                                                                                                        ).order_by("payroll_date__year")
        pension_amount_past_year_list = [(obj["payroll_date__year"], obj["payroll_date__month"], obj["total_amount"]) for obj in total_pension_amount_monthly_chart]
        total_pension_change = get_percentage_diff(
                                    previous=total_pension_til_previous_month,
                                    current=total_pension_amount
                                    )
        
        #benefit
        total_other_amount  =  all_possible_transactions_qs.aggregate(Sum("other_amount"))["other_amount__sum"] or 0
        total_other_amount_til_previous_month  =  all_possible_transactions_qs.filter(payroll_date__range=[start_of_all_transactions, previous_month_end]).aggregate(Sum("other_amount"))["other_amount__sum"] or 0
        total_other_amount_monthly_chart = all_possible_transactions_qs.filter(payroll_date__gte=previous_year_current_month_start).values("payroll_date__year", "payroll_date__month").annotate(total_amount = Sum("other_amount")
                                                                                                                                                                                                        ).order_by("payroll_date__year")
        other_amount_past_year_list = [(obj["payroll_date__year"], obj["payroll_date__month"], obj["total_amount"]) for obj in total_other_amount_monthly_chart]
        total_other_amount_change = get_percentage_diff(
                                    previous=total_other_amount_til_previous_month,
                                    current=total_other_amount
                                    )
        number_of_employees = CompanyEmployeeList.objects.filter(company__id=company_uuid, is_deleted=False).count() or 0

        #all salary month_transaction
        all_month_salary = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if salary_amount_past_year_list:
                for transaction in (
                    salary_amount_past_year_list[:len(salary_amount_past_year_list)-1]
                    if salary_amount_past_year_list[-1][1]==date_today.month
                    else salary_amount_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_salary.append(month_sum)
        current_month_salary_transaction_amount = salary_amount_past_year_list[-1][2] if salary_amount_past_year_list and salary_amount_past_year_list[-1][1]==date_today.month else 0
        all_month_salary.append(current_month_salary_transaction_amount)

        #all tax month_transaction
        all_month_tax = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if tax_amount_past_year_list:
                for transaction in (
                    tax_amount_past_year_list[:len(tax_amount_past_year_list)-1]
                    if tax_amount_past_year_list[-1][1]==date_today.month
                    else tax_amount_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_tax.append(month_sum)
        current_month_tax_transaction_amount = tax_amount_past_year_list[-1][2] if tax_amount_past_year_list and tax_amount_past_year_list[-1][1]==date_today.month else 0
        all_month_tax.append(current_month_tax_transaction_amount)

        #all pension month_transaction
        all_month_pension = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if pension_amount_past_year_list:
                for transaction in (
                    pension_amount_past_year_list[:len(pension_amount_past_year_list)-1]
                    if pension_amount_past_year_list[-1][1]==date_today.month
                    else pension_amount_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_pension.append(month_sum)
        current_month_pension_transaction_amount = pension_amount_past_year_list[-1][2] if pension_amount_past_year_list and pension_amount_past_year_list[-1][1]==date_today.month else 0
        all_month_pension.append(current_month_pension_transaction_amount)

        #all benefit month_transaction
        all_month_benefit = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if other_amount_past_year_list:
                for transaction in (
                    other_amount_past_year_list[:len(other_amount_past_year_list)-1]
                    if other_amount_past_year_list[-1][1]==date_today.month
                    else other_amount_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_benefit.append(month_sum)
        current_month_benefit_transaction_amount = other_amount_past_year_list[-1][2] if other_amount_past_year_list and other_amount_past_year_list[-1][1]==date_today.month else 0
        all_month_benefit.append(current_month_benefit_transaction_amount)

        response = {
                "salary_overview": {
                "total_salary_transactions": total_salary_amount if total_salary_amount else 0,
                "salary_percentage_change": total_salary_change.get("percentage"),
                "salary_change": total_salary_change.get("change")
                },
                "tax_overview": {
                "total_tax_transactions": total_tax_amount if total_tax_amount else 0,
                "tax_percentage_change": total_tax_change.get("percentage"),
                "tax_change": total_tax_change.get("change")
                },
                "pension_overview": {
                "total_pension_transactions": total_pension_amount if total_pension_amount else 0,
                "pension_percentage_change": total_pension_change.get("percentage"),
                "pension_change": total_pension_change.get("change")
                },
                "benefit_overview": {
                "total_benefit_transactions": total_other_amount if total_other_amount else 0,
                "benefit_percentage_change": total_other_amount_change.get("percentage"),
                "benefit_change": total_other_amount_change.get("change")
                },
                "chart_labels": months_list_names,
                "salary_chart_data": all_month_salary,
                "tax_chart_data": all_month_tax,
                "pension_chart_data": all_month_pension,
                "benefit_chart_data": all_month_benefit,
                "total_employees": number_of_employees
            }
        return response
    
    def employment_trend_chart(request, start_date, end_date):
        company_uuid = request.query_params.get("company_uuid")
        employee_qs = CompanyEmployeeList.objects.filter(company__id=company_uuid)

        #active employee
        total_active_employee_monthly_chart = employee_qs.filter(employee_status="ACTIVE", created_at__gte=previous_year_current_month_start).values("created_at__year", "created_at__month").annotate(total_employee = Count("id")                                                                                                                                                                  ).order_by("created_at__year")
        active_employee_past_year_list = [(obj["created_at__year"], obj["created_at__month"], obj["total_employee"]) for obj in total_active_employee_monthly_chart]

        #active employee
        total_new_employee_monthly_chart = employee_qs.filter(employee_status="NOT_JOINED", created_at__gte=previous_year_current_month_start).values("created_at__year", "created_at__month").annotate(total_employee = Count("id")                                                                                                                                                                  ).order_by("created_at__year")
        new_employee_past_year_list = [(obj["created_at__year"], obj["created_at__month"], obj["total_employee"]) for obj in total_new_employee_monthly_chart]

        #deleted employee
        total_deleted_employee_monthly_chart = employee_qs.filter(employee_status="DELETED", updated_at__gte=previous_year_current_month_start).values("updated_at__year", "updated_at__month").annotate(total_employee = Count("id")                                                                                                                                                                  ).order_by("created_at__year")
        deleted_employee_past_year_list = [(obj["updated_at__year"], obj["updated_at__month"], obj["total_employee"]) for obj in total_deleted_employee_monthly_chart]

        #all active employee month_transaction
        all_month_active_employee = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if active_employee_past_year_list:
                for transaction in (
                    active_employee_past_year_list[:len(active_employee_past_year_list)-1]
                    if active_employee_past_year_list[-1][1]==date_today.month
                    else active_employee_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_active_employee.append(month_sum)
        current_month_active_employee = active_employee_past_year_list[-1][2] if active_employee_past_year_list and active_employee_past_year_list[-1][1]==date_today.month else 0
        all_month_active_employee.append(current_month_active_employee)

        #all new employee month_transaction
        all_month_new_employee = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if new_employee_past_year_list:
                for transaction in (
                    new_employee_past_year_list[:len(new_employee_past_year_list)-1]
                    if new_employee_past_year_list[-1][1]==date_today.month
                    else new_employee_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_new_employee.append(month_sum)
        current_month_new_employee = new_employee_past_year_list[-1][2] if new_employee_past_year_list and new_employee_past_year_list[-1][1]==date_today.month else 0
        all_month_new_employee.append(current_month_new_employee)

        #all deleted employee month_transaction
        all_month_deleted_employee = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if deleted_employee_past_year_list:
                for transaction in (
                    deleted_employee_past_year_list[:len(deleted_employee_past_year_list)-1]
                    if deleted_employee_past_year_list[-1][1]==date_today.month
                    else deleted_employee_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_deleted_employee.append(month_sum)
        current_month_deleted_employee = deleted_employee_past_year_list[-1][2] if deleted_employee_past_year_list and deleted_employee_past_year_list[-1][1]==date_today.month else 0
        all_month_deleted_employee.append(current_month_deleted_employee)

        response = {
                "chart_labels": months_list_names,
                "active_employee_chart_data": all_month_active_employee,
                "new_employee_chart_data": all_month_new_employee,
                "deleted_employee_chart_data": all_month_deleted_employee,
            }
        return response
    
def valid_uuid_check(uuid_string):
    try:
        # Attempt to create a UUID object from the string
        val = uuid.UUID(uuid_string, version=4)
    except ValueError:
        # If a ValueError is raised, the string is not a valid UUID
        return False
    except TypeError:
        return False

    # Ensure that the string is in the correct UUID format
    return str(val) == uuid_string

class EmployeeOnboarding:
    base_url = settings.LIBERTY_PAY_PLUS_BASE_URL
    this_headers = {
        "Content-Type": "application/json"
    }


    @classmethod
    def send_agency_onboarding_data(
        cls,
        phone_number,
        username,
        first_name,
        last_name,
        email,
        state,
        lga,
        nearest_landmark,
        street,
        gender,
        device_type
    ):
        agency_status_code = [201, 400, 403]
        """
            This is a custom api request to onboard employees from PayBox360 to Liberty Pay
        """
        absolute_url = f"{cls.base_url}/agency/user/create_user_detail/"
        payload = json.dumps(
            {
                "phone_number":phone_number,
                "first_name":first_name,
                "last_name":last_name,
                "email":email,
                "state":state,
                "lga":lga,
                "username": username,
                "nearest_landmark":nearest_landmark,
                "street":street,
                "gender":gender,
                "device_type":device_type,
                "is_paybox_merchant": True,
            },
        )
        response = requests.post(
            url=absolute_url, headers=cls.this_headers, data=payload
        )
        if response.status_code in agency_status_code:
            agency_response = response.json()
            if response.status_code == 201:
                data = {
                    "error": False,
                    "status": "201",
                    "message": "successful",
                    "data": agency_response
                }
            elif response.status_code == 400:
                data = {
                    "error": True,
                    "status": "400",
                    "message": "bad request",
                    "data": agency_response
                }    
            else:
                data = {
                    "error": True,
                    "status": "403",
                    "message": "forbidden",
                    "data": agency_response
                } 
        else:
            data = {
                "error": True,
                "status": "500",
                "message": "An error occurred, try again!",
                "data": None
            }
        return data
    
    @classmethod
    def send_agency_onboarding_phone_data(
        cls,
        phone_number,
        username,
        first_name,
        last_name,
        email,
        state,
        lga,
        nearest_landmark,
        street,
        gender,
        device_type
    ):
        agency_status_code = [201, 400, 403]
        """
            This is a custom api request to onboard employees from PayBox360 to Liberty Pay
        """
        absolute_url = f"{cls.base_url}/agency/user/create_user_detail/"
        payload = json.dumps(
            {
                "phone_number":phone_number,
                "first_name":first_name,
                "last_name":last_name,
                "email":email,
                "state":state,
                "lga":lga,
                "username": username,
                "nearest_landmark":nearest_landmark,
                "street":street,
                "gender":gender,
                "device_type":device_type,
                "is_paybox_merchant": True,
            },
        )
        response = requests.post(
            url=absolute_url, headers=cls.this_headers, data=payload
        )
        if response.status_code in agency_status_code:
            agency_response = response.json()
            if response.status_code == 201:
                data = {
                    "error": False,
                    "status": "201",
                    "message": "successful",
                    "data": agency_response
                }
            elif response.status_code == 400:
                data = {
                    "error": True,
                    "status": "400",
                    "message": "bad request",
                    "data": agency_response
                }    
            else:
                data = {
                    "error": True,
                    "status": "403",
                    "message": "forbidden",
                    "data": agency_response
                } 
        else:
            data = {
                "error": True,
                "status": "500",
                "message": "An error occurred, try again!",
                "data": None
            }
        return data
    
    @classmethod
    def check_agency_onboarding_data(
        cls,
        email,
    ):
        agency_status_code = [200, 201, 400]
        """
       
        """
        absolute_url = f"{cls.base_url}/agency/check_email_exist/?email={email}"

        response = requests.get(
            url=absolute_url, headers=cls.this_headers
        )
        if response.status_code in agency_status_code:
            agency_response = response.json()
            if response.status_code == 200:
                data = {
                    "error": False,
                    "status": "200",
                    "message": "successful",
                    "data": agency_response
                }  
            elif response.status_code == 201:
                data = {
                    "error": False,
                    "status": "201",
                    "message": "user exists",
                    "data": agency_response
                }
            else:
                data = {
                    "error": True,
                    "status": "400",
                    "message": "error",
                    "data": agency_response
                } 
        else:
            data = {
                "error": True,
                "status": "500",
                "message": "An error occurred, try again!",
                "data": None
            }
        return data
    
    @classmethod
    def check_agency_onboarding_phone_data(
        cls,
        phone_number,
    ):
        agency_status_code = [200, 201, 400]
        """
       
        """
        absolute_url = f"{cls.base_url}/agency/check_phone_number_exist/?phone_number={phone_number}"

        response = requests.get(
            url=absolute_url, headers=cls.this_headers
        )
        if response.status_code in agency_status_code:
            agency_response = response.json()
            if response.status_code == 200:
                data = {
                    "error": False,
                    "status": "200",
                    "message": "successful",
                    "data": agency_response
                }  
            elif response.status_code == 201:
                data = {
                    "error": False,
                    "status": "201",
                    "message": "user exists",
                    "data": agency_response
                }
            else:
                data = {
                    "error": True,
                    "status": "400",
                    "message": "error",
                    "data": agency_response
                } 
        else:
            data = {
                "error": True,
                "status": "500",
                "message": "An error occurred, try again!",
                "data": None
            }
        return data
    
    @classmethod
    def fetch_liberty_user_via_email(
        cls,
        email,
    ):
        agency_status_code = [200, 400]
        """
       
        """
        absolute_url = f"{cls.base_url}/agency/fetch_user_via_email/?email={email}"

        response = requests.get(
            url=absolute_url, headers=cls.this_headers
        )
        if response.status_code in agency_status_code:
            agency_response = response.json()
            if response.status_code == 200:
                data = {
                    "error": False,
                    "status": "200",
                    "message": "successful",
                    "data": agency_response
                }  
            else:
                data = {
                    "error": True,
                    "status": "400",
                    "message": "error",
                    "data": agency_response
                } 
        else:
            data = {
                "error": True,
                "status": "500",
                "message": "An error occurred, try again!",
                "data": None
            }
        return data
    
    @classmethod
    def fetch_liberty_user_via_phone_number(
        cls,
        phone_number,
    ):
        agency_status_code = [200, 400]
        """
       
        """
        absolute_url = f"{cls.base_url}/agency/fetch_user_via_phone_number/?phone_number={phone_number}"

        response = requests.get(
            url=absolute_url, headers=cls.this_headers
        )
        if response.status_code in agency_status_code:
            agency_response = response.json()
            if response.status_code == 200:
                data = {
                    "error": False,
                    "status": "200",
                    "message": "successful",
                    "data": agency_response
                }  
            else:
                data = {
                    "error": True,
                    "status": "400",
                    "message": "error",
                    "data": agency_response
                } 
        else:
            data = {
                "error": True,
                "status": "500",
                "message": "An error occurred, try again!",
                "data": None
            }
        return data
    


    @classmethod
    def create_passcode_for_user_on_agency_banking_and_activate_user_email(cls, passcode, phone_number):

        absolute_url = f"{cls.base_url}/agency/user/create_login_passcode_and_activate_email/"

        payload = {
            "pin1": passcode,
            "pin2": passcode,
            "phone_number": phone_number
        }

        response = requests.post(
            url=absolute_url, headers=cls.this_headers, json=payload
        )

        if response.status_code == 201:
            data = {
                "error": False,
                "status": "201",
                "message": "",
                "data": response.text
            }
        else:
            data = {
                "error": True,
                "status": response.status_code,
                "message": "",
                "data": response.text
            }

        
        return data

