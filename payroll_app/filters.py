
from django_filters.rest_framework import FilterSet
import django_filters

from account.models import Transaction
from payroll_app.models import PayrollTable


class TransactionDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')
    user_id = django_filters.NumberFilter(field_name="user_id", lookup_expr='exact')
    transaction_id = django_filters.CharFilter(field_name="transaction_id", lookup_expr='exact')

    class Meta:
        model = Transaction
        fields = ["user_id", "transaction_id", "timestamp_gte", "timestamp_lte"]

class PayrollTableDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')
    user_id = django_filters.NumberFilter(field_name="payroll_user", lookup_expr='exact')
    bulk_id = django_filters.CharFilter(field_name="bulk_id", lookup_expr='exact')

    class Meta:
        model = PayrollTable
        fields = ["user_id", "bulk_id", "timestamp_gte", "timestamp_lte"]