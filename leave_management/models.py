from collections.abc import Iterable
from django.conf import settings
from django.db import models
from requisition.models import Company
from payroll_app.models import CompanyEmployeeList as Employee, \
    CompanyDepartmentSettings as Department, CompanyPayGradeSettings as GradeLevel, \
    CompanyPayGroupSettings as PayGroup
from core.models import BaseModel

LEAVE_FREQUENCY = [
    ("MONTH", "MONTH"),
    ("DAY", "DAY"),
    ("WEEK", "WEEK"),
]

LEAVE_PRIORITY = [
    ("HIGH", "HIGH"),
    ("MEDIUM", "MEDIUM"),
    ("LOW", "LOW"),
]

class LeaveType(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True)
    # functional_group = models.ForeignKey(PayGroup, on_delete=models.CASCADE, null=True, blank=True)
    # grade_level = models.ForeignKey(GradeLevel, on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=255, null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    created_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                 related_name="leave_type_creator")
    def __str__(self):
        return f'{self.company}:{self.title}'
    class Meta:
        ordering = ["title"]
        verbose_name = "LEAVE TYPE"
        verbose_name_plural = "LEAVE TYPES" 

    @classmethod
    def fetch_leave_type(cls, leave_type_id, company_id):
        leave_type_ins = cls.objects.filter(id=leave_type_id, company__id=company_id, is_deleted=False).first()
        if leave_type_ins:
            return leave_type_ins
        else:
            return None   

class LeavePolicy(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True)
    grade_level = models.ForeignKey(GradeLevel, on_delete=models.CASCADE, null=True, blank=True)
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, null=True, blank=True)
    # title = models.CharField(max_length=255, null=True, blank=True)
    monthly_allocation = models.PositiveIntegerField(null=True, blank=True)
    yearly_allocation = models.PositiveIntegerField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    created_by = models.ForeignKey("core.User", on_delete=models.CASCADE, null=True, blank=True,
                                 related_name="leave_policy_creator")
    def __str__(self) -> str:
        if self.grade_level:
            return f'{self.company.company_name}:{self.grade_level.pay_grade_name}'
        else:
            return f'{self.company.company_name}'
    
    class Meta:
        ordering = ["grade_level"]
        verbose_name = "LEAVE POLICY"
        verbose_name_plural = "LEAVE POLICIES"



class LeaveRequest(BaseModel):
    REQUESTED = 'R'
    WITHDRAWN = 'W'
    APPROVED = 'A'
    DISAPPROVED = 'D'
    COMPLETED = 'C'

    LEAVE_STATUS = [
        ('REQUESTED', 'requested'),
        ('WITHDRAWN', 'withdrawn'),
        ('APPROVED', 'approved'),
        ('ACTIVE', 'active'),
        ('DISAPPROVED', 'disapproved'),
        ('COMPLETED', 'completed')
    ]
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='leave_request_employee')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    approved_start_date = models.DateField(null=True, blank=True)
    approved_end_date = models.DateField(null=True, blank=True)
    number_of_days = models.PositiveIntegerField(null=True, blank=True)
    reason = models.CharField(max_length=1000, null=True, blank=True)
    rejection_reason = models.CharField(max_length=1000, null=True, blank=True)
    other_information = models.CharField(max_length=1000, null=True, blank=True)
    status = models.CharField(max_length=255, choices=LEAVE_STATUS, default='REQUESTED')
    leave_days = models.PositiveIntegerField(null=True, blank=True)
    leave_priority = models.CharField(max_length=255, choices=LEAVE_PRIORITY, default='LOW')
    leave_document = models.TextField(blank=True, null=True)
    minute = models.CharField(max_length=1000, null=True, blank=True)
    signed_by = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='leave_request_approved_by')
    rejected_by = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='leave_request_rejected_by')
    is_taken = models.BooleanField(null=True, blank=True, default=False)
    is_deleted = models.BooleanField(default=False)
    completed_date = models.DateTimeField(null=True, blank=True)
    leave_days_taken = models.IntegerField(null=True, blank=True)
    approved_leave_days = models.IntegerField(null=True, blank=True)
    leave_days_exceeded = models.IntegerField(null=True, blank=True)
    leave_days_remaining = models.IntegerField(null=True, blank=True)
    total_leave_days = models.IntegerField(null=True, blank=True)   
    def __str__(self):
        if self.employee and self.employee.employee is not None:
            return f'{self.employee.employee.first_name} {self.employee.employee.last_name}'
        else:
            return f'{self.id}'

    class Meta:
        ordering = ['employee']
        verbose_name = 'LEAVE REQUEST'
        verbose_name_plural = 'LEAVE REQUESTS'

class LeaveRecord(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, null=True, blank=True, related_name='leave_record')
    leave_request = models.ForeignKey(LeaveRequest, on_delete=models.SET_NULL, null=True, blank=True)
    leave_days_taken = models.IntegerField(null=True, blank=True)
    leave_days_exceeded = models.IntegerField(null=True, blank=True)
    leave_days_remaining = models.IntegerField(null=True, blank=True)
    total_leave_days = models.IntegerField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    def __str__(self):
        if self.employee and self.employee.employee is not None:
            return f'{self.employee.employee.first_name} {self.employee.employee.last_name}'
        else:
            return f'{self.id}'
    class Meta:
        ordering = ["employee"]
        verbose_name = "LEAVE RECORD"
        verbose_name_plural = "LEAVE RECORDS"