from django.urls import path
from leave_management import views
# from .views import LeaveTypeDeta<PERSON>, Leave<PERSON><PERSON><PERSON>ist, LeavePolicyList, LeavePolicyDetail, LeaveRequestApprovalList,\
#     LeaveRequestApprovalDetail, LeaveRequestList, LeaveRequestDetail, LeaveRecordList, LeaveRecordDetail,\
#     NewLeaveRequest, LeaveRecordRecords, LeaveRecordMetrics, LeaveDashboardRecord, LeaveDashboardRequest, LeaveDashboardMetrics


urlpatterns = [

    path('fetch-leave-type/', views.FetchLeaveTypeList.as_view(), name='fetch-leave-type'),
    path('update-leave-type/', views.UpdateLeaveType.as_view(), name='update-leave-type'),
    path('delete-leave-type/', views.DeleteLeaveType.as_view(), name='delete-leave-type'),
    path('leave-type-detail/', views.LeaveTypeDetails.as_view(), name='leave-type-detail'),
    path('create-leave-type/', views.CreateLeaveType.as_view(), name='create-leave-type'),
    path('create-leave-policy/', views.CreateLeavePolicy.as_view(), name='create-leave-policy'),
    path('fetch-leave-policy/', views.FetchLeavePolicyList.as_view(), name='fetch-leave-policy'),
    path('leave-policy-detail/', views.LeavePolicyDetails.as_view(), name='leave-policy-detail'),
    path('update-policy-detail/', views.UpdateLeavePolicy.as_view(), name='update-policy-detail'),
    path('delete-leave-policy/', views.DeleteLeavePolicy.as_view(), name='delete-leave-policy'),
    path('create-leave-request/', views.CreateLeaveRequest.as_view(), name='create-leave-request'),
    path('withdraw-leave-request/', views.WithDrawLeaveRequest.as_view(), name='withdraw-leave-request'),
    path('approve-leave-request/', views.ApproveLeaveRequest.as_view(), name='approve-leave-request'),
    path('reject-leave-request/', views.RejectLeaveRequest.as_view(), name='reject-leave-request'),
    path('fetch-leave-records/', views.FetchLeaveRecordList.as_view(), name='fetch-leave-records'),
    path('leave-record-details/', views.LeaveRecordDetails.as_view(), name='leave-record-details'),
    path('leave-record-dashboard/', views.LeaveRecordDashboard.as_view(), name='leave-record-dashboard/'),
    path('leave-management-dashboard/', views.LeaveManagementDashboard.as_view(), name='leave-management-dashboard/'),
    path('fetch-leave-request/', views.FetchNewLeaveRequestList.as_view(), name='fetch-leave-request/'),
    path('leave-type/', views.LeaveTypeList.as_view(), name='leave-type'),
    path('leave-type-detail/', views.LeaveTypeDetail.as_view(), name='leave-type'),
    path('leave-policy/', views.LeavePolicyList.as_view(), name='leave-policy'),
    # path('leave-policy-detail/', views.LeavePolicyDetail.as_view(), name='leave-policy'),
    path('leave-request/', views.LeaveRequestList.as_view(), name='leave-request'),
    path('leave-request-detail/', views.LeaveRequestDetail.as_view(), name='leave-request'),
    path('leave-request-approval/',views.LeaveRequestApprovalList.as_view(), name='leave-request'),
    path('leave-request-approval-detail/', views.LeaveRequestApprovalDetail.as_view(), name='leave-request'),
    path('leave-record/', views.LeaveRecordList.as_view(), name='leave-record'),
    path('leave-record-detail/', views.LeaveRecordDetail.as_view(), name='leave-record'),
    path('leave-dashboard-metrics/', views.LeaveDashboardMetrics.as_view(), name='leave-dashboard'),
    path('leave-dashboard-requests/', views.LeaveDashboardRequest.as_view(), name='leave-dashboard'), 
    path('leave-dashboard-records/', views.LeaveDashboardRecord.as_view(), name='leave-dashboard'), 
    path('leave-record-metrics/', views.LeaveRecordMetrics.as_view(), name='leave-record'), 
    path('leave-record-records/', views.LeaveRecordRecords.as_view(), name='leave-record'), 
    path('new-leave-requests/', views.NewLeaveRequest.as_view(), name='new-leave')
]