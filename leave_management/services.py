from .models import LeaveRecord, LeaveRequest,LeavePolicy, LeaveType
from datetime import datetime
from .helpers import filter_dates
from django.db.models import Sum
from django.utils import timezone
from requisition.models import Company
# from payroll_app.models import CompanyEmployeeList as Employee

today = datetime.now()

leave_record_qs = LeaveRecord.objects.all()
leave_request_qs = LeaveRequest.objects.all()
leave_policy_qs = LeavePolicy.objects.all()
leave_type_qs = LeaveType.objects.all()

class DateFilter:
    def __init__(self):
        filter_date = filter_dates(datetime=datetime)

        self.inception = filter_date.get("inception")

        self.today = filter_date.get("today")
        self.previous_day = filter_date.get("previous_day")

        self.week_start = filter_date.get("week_start")
        self.previous_week_start = filter_date.get("previous_week_start")
        self.previous_week_end = filter_date.get("previous_week_end")

        self.month_start = filter_date.get("month_start")
        self.previous_month_start = filter_date.get("previous_month_start")
        self.previous_month_end = filter_date.get("previous_month_end")

        self.year_start = filter_date.get("year_start")
        self.previous_year_start = filter_date.get("previous_year_start")
        self.previous_year_end = filter_date.get("previous_year_end")

    def get_date_filter(self, request):
        filter = request.query_params.get("filter")

        range_start = request.query_params.get("range_start")
        if range_start is not None:
            range_start = datetime.strptime(range_start, "%Y-%m-%d")
        range_end = request.query_params.get("range_end")
        if range_end is not None:
            range_end = datetime.strptime(range_end, "%Y-%M-%d")
        if range_start and range_end:
            self.date_filter = {"date_created__gte": range_start, "date_created_lte": range_end}
            self.date_filter_two = {"created_at__gte": range_start, "created_at_lte": range_end}
            return {"date_filter": self.date_filter,
                    "date_filter_two": self.date_filter_two}

        specific_day = request.query_params.get("specific_day")
        if specific_day is not None:
            specific_day = datetime.strptime(specific_day, "%Y-%m-%d")
            self.date_filter = {"date_created__date": specific_day}
            self.date_filter_two = {"created_at__date": specific_day}
            return {"date_filter": self.date_filter,
                    "date_filter_two": self.date_filter_two}

        if filter is not None:
            if filter == "today":
                self.date_filter = {"date_created__date": timezone.now().date()}
                self.date_filter_two = {"created_at__date": timezone.now().date()}
            elif filter == "previous_day":
                self.date_filter = {"date_created__date": self.previous_day.date()}
                self.date_filter_two = {"created_at__date": self.previous_day.date()}
            elif filter == "this_week":
                self.date_filter = {"date_created__gte": self.week_start}
                self.date_filter_two = {"created_at__gte": self.week_start}
            elif filter == "last_week":
                self.date_filter = {"date_created__gte": self.previous_week_start, "date_created__lte": self.previous_week_end}
                self.date_filter_two = {"created_at__gte": self.previous_week_start, "created_at__lte": self.previous_week_end}
            elif filter == "this_month":
                self.date_filter = {"date_created__gte": self.month_start}
                self.date_filter_two = {"created_at__gte": self.month_start}
            elif filter == "last_month":
                self.date_filter = {"date_created__gte": self.previous_month_start, "date_created__lte": self.previous_month_end}
                self.date_filter_two = {"created_at__gte": self.previous_month_start, "created_at__lte": self.previous_month_end}
            elif filter == "this_year":
                self.date_filter = {"date_created__gte": self.year_start}
                self.date_filter_two = {"created_at__gte": self.year_start}
            elif filter == "last_year":
                self.date_filter = {"date_created__gte": self.previous_year_end, "date_created__lte": self.previous_year_end}
                self.date_filter_two = {"created_at__gte": self.previous_year_end, "created_at__lte": self.previous_year_end}
            else: 
                self.date_filter = {"date_created__date": self.inception}
                self.date_filter_two = {"created_at__date": self.inception}
        else: 
            self.date_filter = {"date_created__date": self.inception}
            self.date_filter_two = {"created_at__date": self.inception}
        return {"date_filter": self.date_filter, 
                "date_filter_two": self.date_filter_two}

class LeaveDashboard:
    def __init__(self, request):
        self.inception = DateFilter().inception
        self.today = DateFilter().today
        self.previous_day = DateFilter().previous_day
        self.week_start = DateFilter().week_start
        self.month_start = DateFilter().month_start
        self.year_start = DateFilter().year_start
        self.previous_week_start = DateFilter().previous_week_start
        self.previous_week_end = DateFilter().previous_week_end
        self.previous_month_end = DateFilter().previous_month_end
        self.previous_month_start = DateFilter().previous_month_start
        self.previous_year_start = DateFilter().previous_year_start
        self.previous_year_end = DateFilter().previous_year_end

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.company_id=request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.available_leave = leave_type_qs.filter(company=company, is_deleted=False).count()
        self.active_leave = leave_request_qs.filter(company=company, status__icontains="approved", is_taken=True,
                                                    start_date__lte=today.date(), 
                                                    end_date__gte=today.date()).count()
        self.upcoming_leave = leave_request_qs.filter(company=company, status__icontains="approved", 
                                                      start_date__gt=today.date()).count()
        self.approved_leave = leave_request_qs.filter(company=company, status__icontains="approved").count()
        self.pending_request = leave_request_qs.filter(company=company, status__icontains="requested").count()
        self.rejected_leave = leave_request_qs.filter(company=company, status__icontains="disapproved").count()

        new_leave_requests = []
        serial_number = 1

        new_leave_request = leave_request_qs.filter(company=company, status__icontains="requested").values(
            'id', 'employee', 'start_date', 'reason')

        for instance in new_leave_request:
            new_leave_requests.append({
                'sn': serial_number,
                'id': instance['id'],
                'employee': instance['employee'],
                'start_date': instance['start_date'],
                'reason': instance['reason']
            })
            serial_number += 1

        self.new_leave_requests = new_leave_requests

        leave_records = []
        serial_number = 1

        leave_record = leave_request_qs.filter(company=company).values(
            'id','employee', 'leave_type', 'number_of_days', 'reason', 'start_date', 'end_date', 'status')

        for instance in leave_record:
            leave_records.append({
                'sn': serial_number,
                'id': instance['id'],
                'employee': instance['employee'],
                'leave_type': instance['leave_type'], 
                'number_of_days': instance['number_of_days'], 
                'reason': instance['reason'], 
                'from': instance['start_date'], 
                'to': instance['end_date'], 
                'status': instance['status']
            })
            serial_number +=1

        self.leave_records = leave_records

    def get_metrics(self):
        data = {
            "available_leave": self.available_leave if self.available_leave else 0,
            "active_leave": self.active_leave if self.active_leave else 0,
            "upcoming_leave": self.upcoming_leave if self.upcoming_leave else 0,
            "approved_leave": self.approved_leave if self.approved_leave else 0,
            "pending_leave": self.pending_request if self.pending_request else 0,
            "rejected_leave": self.rejected_leave if self.rejected_leave else 0
        }
        return data
    
    def get_new_leave_request(self):
        data = {
            "new_leave_requests": self.new_leave_requests if self.new_leave_requests else 0,
            "count": len(self.new_leave_requests) if self.new_leave_requests else 0
        }
        return data
    
    def get_leave_record(self):
        data = {
            "leave_records": self.leave_records if self.leave_records else 0,
            "count": len(self.leave_records) if self.leave_records else 0
        }
        return data
class LeaveRecords:
    def __init__(self, request):
        self.inception = DateFilter().inception
        self.today = DateFilter().today
        self.previous_day = DateFilter().previous_day
        self.week_start = DateFilter().week_start
        self.month_start = DateFilter().month_start
        self.year_start = DateFilter().year_start
        self.previous_week_start = DateFilter().previous_week_start
        self.previous_week_end = DateFilter().previous_week_end
        self.previous_month_end = DateFilter().previous_month_end
        self.previous_month_start = DateFilter().previous_month_start
        self.previous_year_start = DateFilter().previous_year_start
        self.previous_year_end = DateFilter().previous_year_end

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.company_id=request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        self.total_active_leave = leave_request_qs.filter(company=company, status__icontains="approved",
                                                          start_date__lte=today.date(), 
                                                          end_date__gte=today.date()).count()
        self.total_leave_request = leave_request_qs.filter(company=company, status__icontains="requested").count()
        self.total_approved_leave = leave_request_qs.filter(company=company, status__icontains="approved").count()
        self.total_declined_leave = leave_request_qs.filter(company=company, status__icontains="disapproved").count()

        leave_records = []
        serial_number = 1

        leave_record = leave_request_qs.filter(company=company).values(
            'id','employee', 'leave_type', 'number_of_days', 'reason', 'start_date', 'end_date', 'status')

        for instance in leave_record:
            leave_records.append({
                'sn': serial_number,
                'id': instance['id'],
                'employee': instance['employee'],
                'leave_type': instance['leave_type'], 
                'number_of_days': instance['number_of_days'], 
                'reason': instance['reason'], 
                'from': instance['start_date'], 
                'to': instance['end_date'], 
                'status': instance['status']
            })
            serial_number +=1

        self.leave_records = leave_records

    def get_metrics(self):
        data = {
            "total_active_leave": self.total_active_leave if self.total_active_leave else 0,
            "total_leave_request": self.total_leave_request if self.total_leave_request else 0,
            "total_approved_request": self.total_approved_leave if self.total_approved_leave else 0,
            "total_declined_request": self.total_declined_leave if self.total_declined_leave else 0,
        }
        return data
    
    def get_leave_records(self):
        data = {
            "leave_records": self.leave_records if self.leave_records else 0,
            "count": len(self.leave_records) if self.leave_records else 0
        }
        return data
class NewLeaveRequests:
    def __init__(self, request):
        self.inception = DateFilter().inception
        self.today = DateFilter().today
        self.previous_day = DateFilter().previous_day
        self.week_start = DateFilter().week_start
        self.month_start = DateFilter().month_start
        self.year_start = DateFilter().year_start
        self.previous_week_start = DateFilter().previous_week_start
        self.previous_week_end = DateFilter().previous_week_end
        self.previous_month_end = DateFilter().previous_month_end
        self.previous_month_start = DateFilter().previous_month_start
        self.previous_year_start = DateFilter().previous_year_start
        self.previous_year_end = DateFilter().previous_year_end

        self.filter = DateFilter().get_date_filter(request=request).get("date_filter")
        self.company_id=request.query_params.get('company_uuid')
        company = Company.objects.filter(id=self.company_id).last()

        new_leave_requests = []
        serial_number = 1

        new_leave_request = leave_request_qs.filter(company=company, status__icontains="requested").values(
            'id', 'employee', 'leave_type', 'number_of_days', 'reason', 'start_date', 'end_date', 'status')

        for instance in new_leave_request:
            new_leave_requests.append({
                'sn': serial_number,
                'id': instance['id'],
                'employee': instance['employee'],
                'leave_type': instance['leave_type'],
                'number_of_days': instance['number_of_days'], 
                'from': instance['start_date'],
                'to': instance['end_date'],
                'status': instance['status']
            })
            serial_number += 1

        self.new_leave_requests = new_leave_requests

    def get_new_leave_requests(self):
        data = {
            'new_leave_requests': self.new_leave_requests if self.new_leave_requests else 0,
            'count': len(self.new_leave_requests) if self.new_leave_requests else 0
        }
        return data
